import os
import logging
from pathlib import Path

from celery import Celery
from kombu import Queue
from dotenv import load_dotenv

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Load environment variables so Celery has access to aida-llm credentials
BASE_DIR = Path(__file__).resolve().parents[1]
load_dotenv(BASE_DIR / '.env')

logger = logging.getLogger(__name__)
if not os.getenv('AIDA_LLM_URL'):
    logger.warning(
        'AIDA_LLM_URL is not set; default client URL will be used for aida-llm uploads.'
    )
if not os.getenv('AIDA_LLM_API_TOKEN'):
    logger.warning(
        'AIDA_LLM_API_TOKEN is not set; authenticated requests to aida-llm will fail.'
    )

app = Celery('aida')

# Explicitly tell <PERSON>ler<PERSON> to use Redis
app.conf.update(
    broker_url='redis://localhost:6379/0',
    result_backend='redis://localhost:6379/0',
    broker_transport='redis',
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    task_default_queue='default',
    task_queues=(Queue('default', routing_key='default'),),
    broker_connection_retry=True,
    broker_connection_retry_on_startup=True,
)

# Load Django settings after our explicit Redis configuration
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks(['main.services'])

print("Celery worker initialized with Redis broker")
