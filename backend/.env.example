# Django Core Settings
DJANGO_SECRET_KEY=your-super-secret-key-at-least-50-characters
JWT_SECRET_KEY=your-jwt-secret-key-at-least-32-characters

# Database Configuration
POSTGRES_DB=aida_db
POSTGRES_USER=aida_user
POSTGRES_PASSWORD=your-secure-db-password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Email Configuration (Gmail Example)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-specific-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Google OAuth2 Settings
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# # AnythingLLM Configuration (legacy)
# ANYTHING_LLM_URL=http://localhost:8002/api/v1/
# ANYTHING_LLM_API_KEY=your-anything-llm-api-key

# AIDA LLM Configuration
AIDA_LLM_URL=http://localhost:8000/api/v1/
AIDA_LLM_API_TOKEN=your-aida-llm-api-token

# Widget Configuration
WIDGET_DEFAULT_VERSION=1.0

STRIPE_SECRET_KEY=sk_test_51NXXXXXXXXXXXXXXXXXXXXXX

AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION_NAME=us-east-1
AWS_SNS_SENDER_ID=AIDA
