from django.contrib import admin
from .models import Account, AccountUser, AccountWebsite

@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ('account_name', 'get_domain', 'status', 'industry_name', 'subscription_tier', 'subscription_status')
    list_filter = ('status', 'industry_name', 'subscription_tier', 'subscription_status')
    search_fields = ('account_name', 'website__domain', 'primary_contact')
    fieldsets = (
        ('Basic Information', {
            'fields': ('account_name', 'status', 'industry_name')
        }),
        ('Subscription Information', {
            'fields': ('subscription_tier', 'subscription_status', 'subscription_start_date', 'subscription_renewal_date')
        }),
        ('Contact Information', {
            'fields': ('primary_phone', 'secondary_phone', 'primary_contact')
        }),
        ('Address', {
            'fields': ('address_line1', 'address_line2', 'city', 'state', 'zip_code')
        })
    )

    def get_domain(self, obj):
        website = AccountWebsite.objects.filter(account=obj).first()
        return website.domain if website else '-'
    get_domain.short_description = 'Domain'

@admin.register(AccountWebsite)
class AccountWebsiteAdmin(admin.ModelAdmin):
    list_display = ('account', 'domain')
    search_fields = ('domain', 'account__account_name')
    fieldsets = (
        (None, {
            'fields': ('account', 'domain')
        }),
        ('Integration', {
            'fields': ('url_patterns',),
            'description': 'URL patterns where Aida should be displayed (one per line)'
        })
    )

@admin.register(AccountUser)
class AccountUserAdmin(admin.ModelAdmin):
    list_display = ('account', 'user', 'role')
    list_filter = ('role',)
    search_fields = ('account__account_name', 'user__email')