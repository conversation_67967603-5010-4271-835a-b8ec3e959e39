from django.db import models
from django.utils import timezone
from .account import Account
from .conversation import Conversation

class Order(models.Model):
    id = models.CharField(primary_key=True, max_length=100)
    account = models.ForeignKey(Account, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=20)
    conversation = models.OneToOneField(
        Conversation, 
        on_delete=models.CASCADE,
        related_name='order',
        unique=True  # Ensure one conversation per order
    )
    
    # Customer information
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    
    # Order summary from LLM
    order_summary = models.TextField(blank=True, null=True)
    
    # Delivery method
    DELIVERY_CHOICES = [
        ('pickup', 'Pickup'),
        ('delivery', 'Delivery')
    ]
    delivery_method = models.CharField(max_length=20, choices=DELIVERY_CHOICES, default='pickup')
    
    # Payment method
    PAYMENT_CHOICES = [
        ('pay_now', 'Pay Now'),
        ('pay_later', 'Pay Later')
    ]
    payment_method = models.CharField(max_length=20, choices=PAYMENT_CHOICES, default='pay_later')
    
    # Payment status
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed')
    ]
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_intent_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Delivery information
    delivery_address_line1 = models.CharField(max_length=255, blank=True, null=True)
    delivery_address_line2 = models.CharField(max_length=255, blank=True, null=True)
    delivery_city = models.CharField(max_length=100, blank=True, null=True)
    delivery_state = models.CharField(max_length=100, blank=True, null=True)
    delivery_zip_code = models.CharField(max_length=20, blank=True, null=True)
    
    # Delivery charges
    delivery_charge = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    tip_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    # Order timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Order status
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processed', 'Processed'),
            ('failed', 'Failed')
        ],
        default='pending'
    )
    error_message = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Order {self.id} from {self.first_name} {self.last_name} ({self.phone_number})"