from django.db import models
from .account import Account
from django.conf import settings

class Document(models.Model):
    DOCUMENT_TYPES = [
        ('pdf', 'PDF'),
        ('txt', 'TXT'),
        ('md', 'MD'),
        ('doc', 'DOC'),
        ('docx', 'DOCX'),
        ('html', 'HTML'),
    ]

    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='documents')
    file = models.FileField(upload_to='documents/%Y/%m/%d/')
    file_type = models.CharField(max_length=4, choices=DOCUMENT_TYPES)
    description = models.TextField(blank=True)
    original_filename = models.CharField(max_length=255)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    llm_processed = models.BooleanField(default=False)
    llm_processing_error = models.TextField(blank=True)
    llm_id = models.CharField(max_length=255, blank=True)

    class Meta:
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.original_filename} ({self.account.name})"

    def get_file_size(self):
        try:
            return self.file.size
        except:
            return 0
