from django.db import models
from django.conf import settings

class AccountUser(models.Model):
    account = models.ForeignKey('Account', on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    role = models.Char<PERSON>ield(
        max_length=20,
        choices=[
            ('admin', 'Admin'),
            ('user', 'User'),
            ('viewer', 'Viewer')
        ],
        default='user'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_users'
        unique_together = ('account', 'user')

    def __str__(self):
        return f"{self.account.account_name} - {self.user.email} ({self.role})" 