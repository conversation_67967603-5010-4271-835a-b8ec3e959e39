from django.db import models
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError

def validate_domain(value):
    """Custom domain validator"""
    # Remove http:// or https:// if present
    domain = value.lower()
    if domain.startswith('http://'):
        domain = domain[7:]
    elif domain.startswith('https://'):
        domain = domain[8:]

    # Remove www. if present
    if domain.startswith('www.'):
        domain = domain[4:]

    # Domain regex pattern
    domain_regex = r'^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$'
    domain_validator = RegexValidator(
        regex=domain_regex,
        message='Enter a valid domain (e.g., example.com)'
    )

    try:
        domain_validator(domain)
    except ValidationError:
        raise ValidationError('Enter a valid domain (e.g., example.com)')

class AccountWebsite(models.Model):
    account = models.ForeignKey(
        'Account',
        on_delete=models.CASCADE,
        related_name='websites'
    )
    domain = models.CharField(
        max_length=255,
        validators=[validate_domain],
        help_text="Website domain (e.g., example.com)"
    )
    url_patterns = models.TextField(
        help_text="URL patterns where Aida should show. One pattern per line.",
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Account Website'
        verbose_name_plural = 'Account Websites'
        ordering = ['domain']

    def __str__(self):
        return f"{self.account.account_name} - {self.domain}"

    def clean(self):
        """Clean domain before saving"""
        if self.domain:
            # Remove http:// or https:// if present
            domain = self.domain.lower()
            if domain.startswith('http://'):
                domain = domain[7:]
            elif domain.startswith('https://'):
                domain = domain[8:]

            # Remove www. if present
            if domain.startswith('www.'):
                domain = domain[4:]

            self.domain = domain

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def get_url_patterns(self):
        """Returns URL patterns as a list"""
        if not self.url_patterns:
            return []
        return [pattern.strip() for pattern in self.url_patterns.split('\n') if pattern.strip()]