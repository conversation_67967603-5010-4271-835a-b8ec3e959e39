from django.db import models
from django.utils import timezone
from .account import Account

class Conversation(models.Model):
    id = models.CharField(primary_key=True, max_length=100)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='conversations')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['id']),
            models.Index(fields=['account', 'created_at']),
        ]

    def __str__(self):
        return f"Conversation {self.id}"

class Message(models.Model):
    ROLE_CHOICES = [
        ('user', 'User'),
        ('assistant', 'Assistant'),
    ]

    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    content = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['created_at']

    # Add validation and debugging for role field
    def save(self, *args, **kwargs):
        # Log the role value before saving
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Saving message with role: {self.role} for conversation: {self.conversation_id}")
        
        # Ensure role is valid
        if self.role not in [choice[0] for choice in self.ROLE_CHOICES]:
            logger.warning(f"Invalid role {self.role}, defaulting to 'user'")
            self.role = 'user'
            
        super().save(*args, **kwargs) 