import uuid
from django.db import models
from django.core.validators import URL<PERSON><PERSON><PERSON><PERSON>, RegexValidator
from django.conf import settings

class Account(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('pending', 'Pending'),
        ('suspended', 'Suspended'),
    ]

    SUBSCRIPTION_TIERS = [
        ('basic', 'Basic'),
        ('pro', 'Pro'),
        ('premium', 'Premium'),
    ]

    # Basic Information
    id = models.AutoField(primary_key=True)
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    account_name = models.CharField(max_length=100)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active'
    )
    industry_name = models.CharField(max_length=100)

    # Subscription Information
    subscription_tier = models.CharField(
        max_length=20,
        choices=SUBSCRIPTION_TIERS,
        default='basic',
        help_text="The subscription tier for this business account"
    )
    subscription_start_date = models.DateField(null=True, blank=True)
    subscription_renewal_date = models.DateField(null=True, blank=True)
    subscription_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('trial', 'Trial'),
            ('expired', 'Expired'),
            ('canceled', 'Canceled'),
        ],
        default='active',
        null=True,
        blank=True
    )

    # Contact Information
    phone_regex = RegexValidator(
        regex=r'^[0-9()+\- ]*$',
        message="Phone number can only contain digits, spaces, parentheses, plus, and hyphens"
    )
    primary_phone = models.CharField(
        validators=[phone_regex],
        max_length=20,
        help_text="Format: +****************"
    )
    secondary_phone = models.CharField(
        validators=[phone_regex],
        max_length=20,
        blank=True,
        null=True,
        help_text="Format: +****************"
    )
    primary_contact = models.CharField(max_length=200)

    # Address Information
    address_line1 = models.CharField(max_length=200)
    address_line2 = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=10)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # User relationship
    users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='AccountUser',
        related_name='accounts'
    )

    # AnythingLLM workspace information
    llm_id = models.CharField(max_length=255, null=True, blank=True)  # AnythingLLM workspace ID
    llm_slug = models.CharField(max_length=255, null=True, blank=True)  # AnythingLLM workspace slug
    llm_folder = models.CharField(max_length=255, null=True, blank=True)
    chat_icon = models.ImageField(
        upload_to='chat_icons/',
        null=True,
        blank=True,
        help_text="SVG, JPG, or PNG icon for AIDA chat customization"
    )

    class Meta:
        ordering = ['account_name']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['subscription_tier']),
        ]

    def __str__(self):
        return f"{self.account_name}"

    def save(self, *args, **kwargs):
        is_new = self._state.adding
        super().save(*args, **kwargs)
        if is_new:
            # Create default config for new accounts
            from .account_config import AccountConfig
            AccountConfig.objects.get_or_create(account=self)
