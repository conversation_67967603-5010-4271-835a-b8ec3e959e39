from django.db import models
from .account import Account
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError

def validate_hex_color(value):
    """Validate that the value is a valid hex color code"""
    if not value.startswith('#'):
        raise ValidationError('Color code must start with #')
    
    if len(value) != 7:  # #RRGGBB format
        raise ValidationError('Color code must be in #RRGGBB format')
    
    try:
        int(value[1:], 16)  # Try to convert hex to int
    except ValueError:
        raise ValidationError('Invalid hex color code')

class AccountConfig(models.Model):
    THEME_CHOICES = [
        ('light', 'Light'),
        ('dark', 'Dark'),
    ]
    
    account = models.OneToOneField(
        Account,
        on_delete=models.CASCADE,
        related_name='config',
        primary_key=True
    )
    widget_version = models.CharField(
        max_length=10,
        default='1.0',
        help_text="Version of the widget to serve for this account"
    )
    primary_color = models.CharField(
        max_length=7, 
        default='#007bff',
        validators=[validate_hex_color]
    )
    secondary_color = models.Char<PERSON>ield(
        max_length=7,
        default='#e3f2fd',
        validators=[validate_hex_color]
    )
    greeting_message = models.CharField(
        max_length=200,
        default="Hello! How can I help you today?",
        help_text="Initial greeting message shown in chat"
    )
    chat_title = models.CharField(
        max_length=50,
        default="AiDA",
        help_text="Title displayed in the chat header"
    )
    order_taking_enabled = models.BooleanField(default=False)
    order_taking_email = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Email address where order notifications will be sent"
    )
    theme = models.CharField(max_length=10, choices=THEME_CHOICES, default='light')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'main_account_config'
        verbose_name = 'Account Configuration'
        verbose_name_plural = 'Account Configurations'

    def __str__(self):
        return f"Config for {self.account.account_name}"
