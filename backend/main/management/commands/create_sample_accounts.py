from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from main.models import Account, AccountUser, AccountWebsite

User = get_user_model()

class Command(BaseCommand):
    help = 'Creates sample account data'

    def handle(self, *args, **kwargs):
        accounts = [
            {
                'account_name': "AIDA",
                'status': 'active',
                'industry_name': 'Technology',
                'primary_contact': '<PERSON>',
                'primary_phone': '******-555-0123',
                'secondary_phone': '',
                'address_line1': '1 Main St',
                'city': 'Apex',
                'state': 'NC',
                'zip_code': '27502',
                'websites': [{
                    'domain': 'aida.research-triangle.ai.com',
                    'url_patterns': ''
                }]
            },
            {
                'account_name': 'RACL',
                'status': 'active',
                'industry_name': 'Education',
                'primary_contact': 'Katie <PERSON>',
                'primary_phone': '******-888-7777',
                'address_line1': '400 E Moore Street',
                'city': 'Apex',
                'state': 'NC',
                'zip_code': '27502',
                'websites': [{
                    'domain': 'racl.org',
                    'url_patterns': ''
                }]
            },
            {
                'account_name': 'Research Triangle AI',
                'status': 'active',
                'industry_name': 'Technology',
                'primary_contact': 'John Doe',
                'primary_phone': '******-555-4444',
                'address_line1': '1 Main St',
                'city': 'Apex',
                'state': 'NC',
                'zip_code': '27502',
                'websites': [{
                    'domain': 'research-triangle.ai',
                    'url_patterns': ''
                }]
            },
            {
                'account_name': 'Test Account',
                'status': 'active',
                'industry_name': 'Restaurant',
                'primary_contact': 'John Doe',
                'primary_phone': '******-555-6666',
                'address_line1': '456 Tech Park Dr',
                'city': 'Durham',
                'state': 'NC',
                'zip_code': '27701',
                'websites': [{
                    'domain': 'localhost',
                    'url_patterns': '/'
                }]
            },
        ]

        for account_data in accounts:
            # Extract websites data
            websites_data = account_data.pop('websites')
            
            # Create account
            account, created = Account.objects.get_or_create(
                account_name=account_data['account_name'],
                defaults=account_data
            )

            # Create websites for account
            if created:
                for website_data in websites_data:
                    AccountWebsite.objects.create(
                        account=account,
                        **website_data
                    )
            
        # Create test account if it doesn't exist
        account, created = Account.objects.get_or_create(
            account_name='Test Account',
        )

        # Create localhost website if it doesn't exist
        website, created = AccountWebsite.objects.get_or_create(
            account=account,
            domain='localhost',  # Vite's default dev server port
        )

        self.stdout.write(self.style.SUCCESS('Successfully created sample accounts')) 
