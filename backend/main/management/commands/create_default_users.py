"""
Management command for creating default users.

This command creates default users for development and testing:
1. Superuser (admin) for full access
2. Manager user for staff access
3. Test user for basic access
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site
from django.db import transaction

User = get_user_model()

class Command(BaseCommand):
    help = 'Creates default users and site for the application'

    def handle(self, *args, **kwargs):
        with transaction.atomic():
            # Create default site if it doesn't exist
            if not Site.objects.filter(id=1).exists():
                Site.objects.create(
                    id=1,
                    domain='localhost:8000',
                    name='Local Development'
                )
                self.stdout.write(self.style.SUCCESS('Created default site'))

            # Create superuser if it doesn't exist
            if not User.objects.filter(username='admin').exists():
                User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123'
                )
                self.stdout.write(self.style.SUCCESS('Created superuser: admin'))

            # Create default admin user if it doesn't exist
            if not User.objects.filter(username='manager').exists():
                User.objects.create_user(
                    username='manager',
                    email='<EMAIL>',
                    password='manager123',
                    is_staff=True
                )
                self.stdout.write(self.style.SUCCESS('Created staff user: manager'))

            # Create test user if it doesn't exist
            if not User.objects.filter(username='test').exists():
                User.objects.create_user(
                    username='test',
                    email='<EMAIL>',
                    password='test123'
                )
                self.stdout.write(self.style.SUCCESS('Created test user: test')) 