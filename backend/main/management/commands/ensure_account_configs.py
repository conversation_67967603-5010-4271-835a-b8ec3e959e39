from django.core.management.base import BaseCommand
from main.models import Account, AccountConfig

class Command(BaseCommand):
    help = 'Ensures all accounts have associated configs'

    def handle(self, *args, **options):
        accounts = Account.objects.all()
        created_count = 0
        
        for account in accounts:
            config, created = AccountConfig.objects.get_or_create(account=account)
            if created:
                created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {len(accounts)} accounts. Created {created_count} new configs.'
            )
        ) 