from django.core.management.base import BaseCommand
from main.models.document import Document
from main.services.tasks import process_document
from celery.exceptions import OperationalError
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Check document processing status and optionally retry failed documents'

    def add_arguments(self, parser):
        parser.add_argument(
            '--retry-failed',
            action='store_true',
            help='Retry processing for documents with errors',
        )
        parser.add_argument(
            '--retry-unprocessed',
            action='store_true',
            help='Retry processing for unprocessed documents',
        )
        parser.add_argument(
            '--account-id',
            type=int,
            help='Filter by specific account ID',
        )

    def handle(self, *args, **options):
        self.stdout.write("Checking document processing status...")
        
        # Base queryset
        queryset = Document.objects.all()
        
        # Filter by account if specified
        if options['account_id']:
            queryset = queryset.filter(account_id=options['account_id'])
            self.stdout.write(f"Filtering by account ID: {options['account_id']}")
        
        # Get statistics
        total_docs = queryset.count()
        processed_docs = queryset.filter(llm_processed=True).count()
        failed_docs = queryset.filter(llm_processed=False, llm_processing_error__isnull=False).exclude(llm_processing_error='').count()
        unprocessed_docs = queryset.filter(llm_processed=False, llm_processing_error='').count()
        
        self.stdout.write(f"\nDocument Processing Statistics:")
        self.stdout.write(f"  Total documents: {total_docs}")
        self.stdout.write(f"  Successfully processed: {processed_docs}")
        self.stdout.write(f"  Failed processing: {failed_docs}")
        self.stdout.write(f"  Unprocessed: {unprocessed_docs}")
        
        # Show failed documents
        if failed_docs > 0:
            self.stdout.write(f"\nFailed Documents:")
            failed_documents = queryset.filter(
                llm_processed=False, 
                llm_processing_error__isnull=False
            ).exclude(llm_processing_error='')
            
            for doc in failed_documents[:10]:  # Show first 10
                self.stdout.write(f"  ID {doc.id}: {doc.original_filename}")
                self.stdout.write(f"    Error: {doc.llm_processing_error[:100]}...")
                self.stdout.write(f"    Account: {doc.account.name if doc.account else 'None'}")
                self.stdout.write(f"    LLM Slug: {doc.account.llm_slug if doc.account else 'None'}")
                self.stdout.write("")
        
        # Show unprocessed documents
        if unprocessed_docs > 0:
            self.stdout.write(f"\nUnprocessed Documents:")
            unprocessed_documents = queryset.filter(llm_processed=False, llm_processing_error='')
            
            for doc in unprocessed_documents[:10]:  # Show first 10
                self.stdout.write(f"  ID {doc.id}: {doc.original_filename}")
                self.stdout.write(f"    Account: {doc.account.name if doc.account else 'None'}")
                self.stdout.write(f"    LLM Slug: {doc.account.llm_slug if doc.account else 'None'}")
                self.stdout.write("")
        
        # Retry failed documents if requested
        if options['retry_failed'] and failed_docs > 0:
            self.stdout.write(f"\nRetrying {failed_docs} failed documents...")
            failed_documents = queryset.filter(
                llm_processed=False, 
                llm_processing_error__isnull=False
            ).exclude(llm_processing_error='')
            
            retry_count = 0
            for doc in failed_documents:
                if doc.account and doc.account.llm_slug:
                    try:
                        process_document.delay(doc.id)
                        retry_count += 1
                        self.stdout.write(f"  Queued retry for document {doc.id}")
                    except OperationalError as e:
                        self.stdout.write(f"  Failed to queue document {doc.id}: {e}")
                else:
                    self.stdout.write(f"  Skipping document {doc.id} - missing account or llm_slug")
            
            self.stdout.write(f"Queued {retry_count} documents for retry")
        
        # Retry unprocessed documents if requested
        if options['retry_unprocessed'] and unprocessed_docs > 0:
            self.stdout.write(f"\nRetrying {unprocessed_docs} unprocessed documents...")
            unprocessed_documents = queryset.filter(llm_processed=False, llm_processing_error='')
            
            retry_count = 0
            for doc in unprocessed_documents:
                if doc.account and doc.account.llm_slug:
                    try:
                        process_document.delay(doc.id)
                        retry_count += 1
                        self.stdout.write(f"  Queued retry for document {doc.id}")
                    except OperationalError as e:
                        self.stdout.write(f"  Failed to queue document {doc.id}: {e}")
                else:
                    self.stdout.write(f"  Skipping document {doc.id} - missing account or llm_slug")
            
            self.stdout.write(f"Queued {retry_count} documents for retry")
        
        self.stdout.write("\nDone!")
