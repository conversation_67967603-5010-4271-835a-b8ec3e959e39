from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from main.models import AccountUser
from django.db.models import Count

User = get_user_model()

class Command(BaseCommand):
    help = 'Verify user group assignments and role mappings'

    def handle(self, *args, **kwargs):
        self.stdout.write('Verifying user groups and role mappings...\n')

        # Check groups exist
        groups = Group.objects.all()
        self.stdout.write('Groups:')
        for group in groups:
            user_count = group.user_set.count()
            self.stdout.write(f'- {group.name}: {user_count} users')
        
        self.stdout.write('\nUser Group Assignments:')
        for user in User.objects.prefetch_related('groups'):
            groups = list(user.groups.values_list('name', flat=True))
            self.stdout.write(f'- {user.email}: {groups or "No groups"}')
        
        # Cross-reference with AccountUser roles
        self.stdout.write('\nRole to Group Mapping Verification:')
        account_users = AccountUser.objects.select_related('user').all()
        
        for account_user in account_users:
            user = account_user.user
            role = account_user.role
            groups = list(user.groups.values_list('name', flat=True))
            
            # Check if mapping is correct
            is_correct = (
                (role == 'admin' and 'Business Admins' in groups) or
                (role == 'user' and 'Business Regular Users' in groups)
            )
            
            status = '✓' if is_correct else '✗'
            self.stdout.write(
                f'{status} {user.email}: role={role}, groups={groups}'
            )
        
        # Summary statistics
        total_users = User.objects.count()
        users_with_groups = User.objects.filter(groups__isnull=False).distinct().count()
        account_users_count = AccountUser.objects.count()
        
        self.stdout.write('\nSummary:')
        self.stdout.write(f'- Total users: {total_users}')
        self.stdout.write(f'- Users with groups: {users_with_groups}')
        self.stdout.write(f'- AccountUser entries: {account_users_count}') 