#!/usr/bin/env python
"""
AWS SNS SMS Test Script
-----------------------
This script tests SMS delivery using AWS SNS and helps diagnose common issues.
"""

import os
import boto3
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# AWS credentials
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_REGION_NAME = os.getenv('AWS_REGION_NAME', 'us-east-1')

def check_sns_attributes():
    """Check the current SMS attributes for the AWS account"""
    try:
        sns_client = boto3.client(
            'sns',
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME
        )
        
        # Get SMS attributes
        response = sns_client.get_sms_attributes()
        logger.info("Current SMS attributes:")
        for key, value in response.get('attributes', {}).items():
            logger.info(f"  {key}: {value}")
        
        # Check if account is in sandbox
        try:
            sandbox_response = sns_client.get_sms_sandbox_account_status()
            is_in_sandbox = sandbox_response.get('IsInSandbox', True)
            logger.info(f"Account SMS sandbox status: {'IN SANDBOX' if is_in_sandbox else 'PRODUCTION'}")
            
            if is_in_sandbox:
                logger.warning("Your account is in the SMS sandbox. You can only send to verified phone numbers.")
                # List verified phone numbers
                verified_numbers = sns_client.list_phone_numbers_opted_out()
                logger.info(f"Verified phone numbers: {verified_numbers}")
        except Exception as e:
            logger.warning(f"Could not check sandbox status: {str(e)}")
            
        return True
    except Exception as e:
        logger.error(f"Error checking SNS attributes: {str(e)}")
        return False

def send_test_sms(phone_number):
    """Send a test SMS message"""
    if not phone_number.startswith('+'):
        phone_number = '+' + phone_number.lstrip('+')
    
    try:
        sns_client = boto3.client(
            'sns',
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME
        )
        
        # Set message attributes
        message_attributes = {
            'AWS.SNS.SMS.SMSType': {
                'DataType': 'String',
                'StringValue': 'Transactional'
            }
        }
        
        # Send the SMS
        response = sns_client.publish(
            PhoneNumber=phone_number,
            Message="This is a test message from your AIDA application.",
            MessageAttributes=message_attributes
        )
        
        message_id = response.get('MessageId')
        logger.info(f"SMS sent successfully! MessageId: {message_id}")
        logger.info(f"Full response: {response}")
        return True
    except Exception as e:
        logger.error(f"Error sending test SMS: {str(e)}")
        return False

def main():
    """Main function to run the test"""
    logger.info("Starting AWS SNS SMS test")
    
    # Check AWS credentials
    if not all([AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY]):
        logger.error("AWS credentials not found. Please check your .env file.")
        return
    
    logger.info(f"Using AWS region: {AWS_REGION_NAME}")
    
    # Check SNS attributes
    logger.info("Checking SNS attributes...")
    check_sns_attributes()
    
    # Get phone number from user
    phone_number = input("Enter your phone number with country code (e.g., +1XXXXXXXXXX): ")
    
    # Send test SMS
    logger.info(f"Sending test SMS to {phone_number}...")
    send_test_sms(phone_number)
    
    logger.info("Test completed. Check your phone for the message.")
    logger.info("If you didn't receive the message, please check the following:")
    logger.info("1. Verify your AWS account is enabled for SMS (check AWS SNS console)")
    logger.info("2. If your account is in the SMS sandbox, verify your phone number in the AWS console")
    logger.info("3. Check your monthly SMS spending limit in the AWS console")
    logger.info("4. Try sending from a different AWS region (some regions have better SMS delivery)")

if __name__ == "__main__":
    main()
