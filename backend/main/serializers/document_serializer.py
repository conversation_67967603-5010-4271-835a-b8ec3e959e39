from rest_framework import serializers
from ..models.document import Document

class DocumentSerializer(serializers.ModelSerializer):
    file_size = serializers.SerializerMethodField()
    uploaded_by_username = serializers.SerializerMethodField()
    file_type = serializers.CharField(required=False)  # We'll set this in perform_create
    original_filename = serializers.CharField(required=False)  # We'll set this in perform_create

    class Meta:
        model = Document
        fields = [
            'id', 'file', 'file_type', 'description', 'original_filename',
            'uploaded_at', 'llm_processed', 'llm_processing_error',
            'file_size', 'uploaded_by_username', 'llm_id'
        ]
        read_only_fields = ['llm_processed', 'llm_processing_error', 'uploaded_by', 'llm_id']

    def get_file_size(self, obj):
        return obj.get_file_size()

    def get_uploaded_by_username(self, obj):
        return obj.uploaded_by.username if obj.uploaded_by else None

    def create(self, validated_data):
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['uploaded_by'] = request.user
        return super().create(validated_data)
