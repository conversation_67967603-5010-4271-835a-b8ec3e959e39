from rest_framework import serializers
from main.models.conversation import Conversation, Message

class MessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Message
        fields = ['id', 'role', 'content', 'created_at']

class ConversationSerializer(serializers.ModelSerializer):
    messages = MessageSerializer(many=True, read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)

    class Meta:
        model = Conversation
        fields = [
            'id', 'account_name', 'messages',
            'created_at', 'updated_at'
        ]

    def to_representation(self, instance):
        """Add additional formatting for email templates"""
        data = super().to_representation(instance)
        if self.context.get('for_email'):
            data['timestamp'] = instance.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        return data

    def create(self, validated_data):
        messages_data = validated_data.pop('messages', [])
        conversation = Conversation.objects.create(**validated_data)
        
        for message_data in messages_data:
            Message.objects.create(conversation=conversation, **message_data)
            
        return conversation

    def update(self, instance, validated_data):
        messages_data = validated_data.pop('messages', [])
        
        # Update conversation fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Add new messages
        for message_data in messages_data:
            Message.objects.create(conversation=instance, **message_data)
            
        return instance 