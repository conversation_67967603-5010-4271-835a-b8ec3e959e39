from rest_framework import serializers
from main.models import AccountConfig

class AccountConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountConfig
        fields = [
            'greeting_message',
            'chat_title',
            'primary_color',
            'secondary_color',
            'order_taking_enabled',
            'order_taking_email',
            'widget_version'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at'] 
