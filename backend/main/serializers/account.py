from rest_framework import serializers
from main.models import Account, AccountUser, AccountWebsite, AccountConfig
from django.contrib.auth import get_user_model
from .account_config import AccountConfigSerializer

User = get_user_model()

class AccountUserSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)

    class Meta:
        model = AccountUser
        fields = ['id', 'role', 'email', 'first_name', 'last_name']
        read_only_fields = ['id', 'email', 'first_name', 'last_name']

class AccountWebsiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountWebsite
        fields = ['id', 'domain', 'url_patterns']

class AccountSerializer(serializers.ModelSerializer):
    users = AccountUserSerializer(source='accountuser_set', many=True, read_only=True)
    websites = AccountWebsiteSerializer(many=True, required=False)
    config = AccountConfigSerializer(read_only=True)

    chat_icon = serializers.SerializerMethodField()

    def get_chat_icon(self, obj):
        if obj.chat_icon:
            return f'/media/{obj.chat_icon.name}'
        return None

    class Meta:
        model = Account
        fields = [
            'id', 'uuid', 'account_name', 'status', 'industry_name',
            'primary_contact', 'primary_phone', 'secondary_phone',
            'address_line1', 'address_line2', 'city', 'state', 'zip_code',
            'websites', 'created_at', 'updated_at', 'users',
            'llm_id', 'llm_slug', 'chat_icon', 'config',
            'subscription_tier', 'subscription_start_date', 
            'subscription_renewal_date', 'subscription_status'
        ]
        read_only_fields = ['created_at', 'updated_at', 'chat_icon']

    def create(self, validated_data):
        websites_data = validated_data.pop('websites', [])
        account = Account.objects.create(**validated_data)
        
        # Create websites for account if provided
        for website_data in websites_data:
            AccountWebsite.objects.create(account=account, **website_data)
        
        return account

    def update(self, instance, validated_data):
        websites_data = validated_data.pop('websites', None)
        
        # Update account fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Update websites if provided
        if websites_data is not None:
            # Remove existing websites
            instance.websites.all().delete()
            # Create new websites
            for website_data in websites_data:
                AccountWebsite.objects.create(account=instance, **website_data)
        
        instance.save()
        return instance
