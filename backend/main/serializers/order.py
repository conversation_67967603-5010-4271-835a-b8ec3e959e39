from rest_framework import serializers
from main.models import Order
from .conversation import ConversationSerializer

class OrderSerializer(serializers.ModelSerializer):
    conversation = ConversationSerializer(read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'phone_number', 'account_name', 'first_name', 'last_name',
            'order_summary', 'delivery_method', 'payment_method', 'payment_status',
            'payment_intent_id', 'delivery_address_line1', 'delivery_address_line2',
            'delivery_city', 'delivery_state', 'delivery_zip_code',
            'delivery_charge', 'tip_amount', 'conversation', 
            'created_at', 'updated_at', 'status', 'error_message'
        ]