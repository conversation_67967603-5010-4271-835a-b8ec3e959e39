<!DOCTYPE html>
<html>
<head>
    <style>
        .header { margin-bottom: 20px; }
        .section { margin: 20px 0; }
        .order-details { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
        .delivery-info { background-color: #f0f7ff; padding: 15px; border-radius: 5px; }
        .payment-info { background-color: #f5fff0; padding: 15px; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; }
        table, th, td { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f2f2f2; text-align: left; }
        .total-row { font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h2>New Order from {{ account_name }}</h2>
        <h3>Customer: {{ first_name }} {{ last_name }}</h3>
        <p><strong>Phone:</strong> {{ phone_number }}</p>
        <p><strong>Received on:</strong> {{ timestamp }}</p>
    </div>

    <div class="section order-details">
        <h3>Order Details</h3>

        {% if order_items and order_items|length > 0 %}
        <!-- Order Items Table when items exist -->
        <p><strong>Items Ordered:</strong> {{ order_items|length }} item(s)</p>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <tr style="background-color: #f2f2f2;">
                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Item</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">Quantity</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Price</th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total</th>
            </tr>
            {% for item in order_items %}
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">{{ item.name }}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{{ item.quantity }}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ item.price|floatformat:2 }}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ item.total|floatformat:2 }}</td>
            </tr>
            {% endfor %}

            <!-- Subtotal, Tax, Delivery, Tip, Total -->
            <tr>
                <td colspan="3" style="text-align: right;"><strong>Subtotal:</strong></td>
                <td>${{ subtotal|floatformat:2 }}</td>
            </tr>
            {% if tax %}
            <tr>
                <td colspan="3" style="text-align: right;"><strong>Tax:</strong></td>
                <td>${{ tax|floatformat:2 }}</td>
            </tr>
            {% endif %}
            {% if delivery_charge and delivery_charge != "0.00" %}
            <tr>
                <td colspan="3" style="text-align: right;"><strong>Delivery Charge:</strong></td>
                <td>${{ delivery_charge|floatformat:2 }}</td>
            </tr>
            {% endif %}
            {% if tip_amount and tip_amount != "0.00" %}
            <tr>
                <td colspan="3" style="text-align: right;"><strong>Tip:</strong></td>
                <td>${{ tip_amount|floatformat:2 }}</td>
            </tr>
            {% endif %}
            <tr class="total-row">
                <td colspan="3" style="text-align: right;"><strong>TOTAL:</strong></td>
                <td>${{ total|floatformat:2 }}</td>
            </tr>
        </table>
        {% else %}
        <!-- Order Summary Section when no specific items are available -->
        <div style="margin-bottom: 20px;">
            <h4>Order Summary:</h4>
            <p>{{ order_summary }}</p>

            <!-- Simple totals table for when no items are present -->
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Subtotal:</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ subtotal|floatformat:2 }}</td>
                </tr>
                {% if tax %}
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tax:</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ tax|floatformat:2 }}</td>
                </tr>
                {% endif %}
                {% if delivery_charge and delivery_charge != "0.00" %}
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Delivery Charge:</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ delivery_charge|floatformat:2 }}</td>
                </tr>
                {% endif %}
                {% if tip_amount and tip_amount != "0.00" %}
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;"><strong>Tip:</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${{ tip_amount|floatformat:2 }}</td>
                </tr>
                {% endif %}
                <tr class="total-row">
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;"><strong>TOTAL:</strong></td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right; font-weight: bold;">${{ total|floatformat:2 }}</td>
                </tr>
            </table>
        </div>
        {% endif %}

        <!-- Order Summary Text -->
        <div style="margin-top: 20px;">
            <h4>Additional Details:</h4>
            <p>{{ order_summary }}</p>
        </div>
    </div>

    <div class="section delivery-info">
        <h3>Delivery Information</h3>
        <p><strong>Method:</strong> {{ delivery_method }}</p>

        {% if delivery_method == 'delivery' and delivery_address %}
        <div>
            <h4>Delivery Address:</h4>
            <p>{{ delivery_address.line1 }}</p>
            {% if delivery_address.line2 %}<p>{{ delivery_address.line2 }}</p>{% endif %}
            <p>{{ delivery_address.city }}, {{ delivery_address.state }} {{ delivery_address.zip_code }}</p>
        </div>
        {% endif %}
    </div>

    <div class="section payment-info">
        <h3>Payment Information</h3>
        <p><strong>Payment Method:</strong> {{ payment_method }}</p>

        {% if delivery_charge or tip_amount %}
        <table>
            <tr>
                <th>Item</th>
                <th>Amount</th>
            </tr>
            {% if delivery_charge %}
            <tr>
                <td>Delivery Charge</td>
                <td>${{ delivery_charge }}</td>
            </tr>
            {% endif %}
            {% if tip_amount %}
            <tr>
                <td>Tip</td>
                <td>${{ tip_amount }}</td>
            </tr>
            {% endif %}
        </table>
        {% endif %}
    </div>
</body>
</html>