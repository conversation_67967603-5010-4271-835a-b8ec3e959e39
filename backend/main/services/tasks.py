from worker.app import app
from celery import shared_task
from main.models.document import Document
# Migrate or remove: from main.services.LLMServices.AnythingLLMService import AnythingLLMService
from main.services.aida_llm_client import AidaLLMClient, AidaLLMClientError
import logging
import os
from django.db import transaction

logger = logging.getLogger('main')

@shared_task(bind=True, autoretry_for=(AidaLLMClientError, FileNotFoundError), retry_backoff=True, max_retries=3)
def process_document(self, document_id):
    try:
        with transaction.atomic():
            # Get document from database
            try:
                document = Document.objects.select_for_update().get(id=document_id)
            except Document.DoesNotExist:
                logger.warning(f"Document {document_id} no longer exists, likely deleted. Skipping processing.")
                return {"status": "skipped", "reason": "document_not_found"}

            logger.info(f"Found document in database: {document.id}")

            organization_slug = document.account.llm_slug
            if not organization_slug:
                error_msg = "Account is missing llm_slug required for aida-llm uploads"
                logger.error(f"Document {document_id}: {error_msg}")
                _update_document_failure(document_id, error_msg)
                return {"status": "failed", "reason": "missing_llm_slug"}

            # Check if file exists on disk
            if not document.file or not document.file.path:
                error_msg = "Document file path is missing"
                logger.error(f"Document {document_id}: {error_msg}")
                _update_document_failure(document_id, error_msg)
                return {"status": "failed", "reason": "missing_file_path"}

            if not os.path.exists(document.file.path):
                error_msg = f"Document file not found on disk: {document.file.path}"
                logger.error(f"Document {document_id}: {error_msg}")
                _update_document_failure(document_id, error_msg)
                return {"status": "failed", "reason": "file_not_found"}

            client = AidaLLMClient()

            try:
                upload_result = client.upload_document(organization_slug=organization_slug, file_path=document.file.path)
            except (AidaLLMClientError, FileNotFoundError, ValueError) as exc:
                error_msg = f"Failed to upload document to aida-llm: {exc}"
                logger.error(f"Document {document_id}: {error_msg}")
                _update_document_failure(document_id, error_msg)
                raise Exception(error_msg) from exc

            logger.info("aida-llm upload successful: %s", upload_result)

            document.llm_processed = True
            document.llm_processing_error = ''
            document.llm_id = str(upload_result.get('id')) if upload_result.get('id') is not None else ''
            document.save()
            logger.info(
                "Document successfully processed. llm_processed=%s, llm_id=%s",
                document.llm_processed,
                document.llm_id,
            )

            return upload_result

    except AidaLLMClientError as e:
        logger.error(f"AIDA LLM client error while processing document {document_id}: {str(e)}")
        _update_document_failure(document_id, str(e))
        raise  # This will trigger retry
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {str(e)}")
        _update_document_failure(document_id, str(e))
        # Don't re-raise for non-retryable exceptions to avoid infinite retries
        return {"status": "failed", "reason": str(e)}


def _update_document_failure(document_id: int, error_message: str) -> None:
    """Update document with failure status, handling case where document might not exist."""
    try:
        document = Document.objects.get(id=document_id)
        document.llm_processing_error = error_message
        document.llm_processed = False
        document.save()
        logger.info("Updated document %s to failed state: %s", document_id, error_message)
    except Document.DoesNotExist:
        logger.warning(f"Cannot update document {document_id} failure status - document no longer exists")
    except Exception as inner_e:
        logger.error(f"Failed to update document {document_id} status: {str(inner_e)}")
