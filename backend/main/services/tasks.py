from worker.app import app
from celery import shared_task
from main.models.document import Document
# Migrate or remove: from main.services.LLMServices.AnythingLLMService import AnythingLLMService
from main.services.aida_llm_client import AidaLLMClient, AidaLLMClientError
import logging
from django.db import transaction

logger = logging.getLogger('main')

@shared_task(bind=True, autoretry_for=(Exception,), retry_backoff=True, max_retries=3)
def process_document(self, document_id):
    try:
        with transaction.atomic():
            # Get document from database
            document = Document.objects.select_for_update().get(id=document_id)
            logger.info(f"Found document in database: {document.id}")

            organization_slug = document.account.llm_slug
            if not organization_slug:
                raise ValueError("Account is missing llm_slug required for aida-llm uploads")

            client = AidaLLMClient()

            try:
                upload_result = client.upload_document(organization_slug=organization_slug, file_path=document.file.path)
            except (AidaLLMClientError, FileNotFoundError, ValueError) as exc:
                raise Exception(f"Failed to upload document to aida-llm: {exc}") from exc

            logger.info("aida-llm upload successful: %s", upload_result)

            document.llm_processed = True
            document.llm_processing_error = ''
            document.llm_id = str(upload_result.get('id')) if upload_result.get('id') is not None else ''
            document.save()
            logger.info(
                "Document successfully processed. llm_processed=%s, llm_id=%s",
                document.llm_processed,
                document.llm_id,
            )

            return upload_result

    except Document.DoesNotExist:
        logger.error(f"Document not found: {document_id}")
        raise
    except AidaLLMClientError as e:
        logger.error(f"AIDA LLM client error while processing document {document_id}: {str(e)}")
        _update_document_failure(document_id, str(e))
        raise
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {str(e)}")
        _update_document_failure(document_id, str(e))
        raise


def _update_document_failure(document_id: int, error_message: str) -> None:
    try:
        document = Document.objects.get(id=document_id)
        document.llm_processing_error = error_message
        document.llm_processed = False
        document.save()
        logger.info("Updated document %s to failed state: %s", document_id, error_message)
    except Exception as inner_e:
        logger.error(f"Failed to update document status: {str(inner_e)}")
