import asyncio
import json
import os
from typing import Any
from markitdown import MarkItDown
import requests
from requests_toolbelt.multipart.encoder import MultipartEncoder
import logging
from http.client import HTTPConnection
from urllib3.exceptions import InsecureRequestWarning

import ultraimport

# Disable SSL verification warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

scrap_urls_service = ultraimport('__dir__/../../services/LinkServices/scrap_urls_service.py', 'scrap_urls_service')


class AnythingLLMService():

    anythingLLMurl = os.environ.get("ANYTHING_LLM_URL", "http://localhost/api/v1/")
    bearer_token = os.environ.get("ANYTHING_LLM_API_KEY")

    headers = {
        'Authorization': f'Bearer {bearer_token}',
        'Content-Type': 'application/json'
    }


    log = logging.getLogger('urllib3')
    log.setLevel(logging.DEBUG)

    # logging from urllib3 to console
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    log.addHandler(ch)

    # print statements from `http.client.HTTPConnection` to console/stdout
    HTTPConnection.debuglevel = 1
    logging.basicConfig(level=logging.DEBUG)

    session = requests.Session()
    session.verify = False
    session.trust_env = False
    os.environ['CURL_CA_BUNDLE']="" # or whaever other is interfering with 

    #RAG Document Management
    #-----------------------
    @staticmethod
    def uploadDocument(filepath: str) -> str:
        """upload a document to a LLM service platform."""
        try:
            if os.path.exists(filepath):
                #TBA: validate the filename and filepath 
                files = {'file': open(filepath, 'rb')}
                filename = os.path.basename(filepath)
                print(f"Sending files: {filepath}")
                response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/upload",
                    files=files, 
                    headers=AnythingLLMService.headers,
                    verify=False)
                print(response.request.headers["Content-Type"])
                if response.status_code == 200:
                    posts = response.json()
                    return posts
                else:
                    print('Error:', response.status_code)
                    raise Exception (response)
            else:
                print(f"Error : file {filepath} doesnot exist")
                raise Exception (f"Error : file {filepath} doesnot exist")
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            raise

    @staticmethod
    def uploadfromBaseURL(workspace_slug: str, base_url: str, url_filter: str, valid_after: str, crawl_depth=2, max_url_count=100) -> str:
        """retrieve all links within the criteria"""
        ret_list = []
        try:
            #TBA: validate the base url link and other criteria
            #retrieve all valid urls
            link_list = asyncio.run(scrap_urls_service.scrap_urls (base_url, url_filter, valid_after, crawl_depth, max_url_count))
            #links = scrap_urls_service.scrap_urls (base_url, url_filter, valid_after)
            #insert the urls into the default folder of the backend instance
            for  link in link_list:
                try:
                    response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/upload-link/", 
                        json = {"link": link['url']},
                        headers=AnythingLLMService.headers,
                        verify=False)
                    if response.status_code == 200:
                        upload_response = response.json()
                        url_location = upload_response['documents'][0]['location']
                        print (upload_response['documents'][0]['location'])
                        #move those URLs/contents to the destination workspace
                        #???TBA customerFolder is always workspace_slug+'-folder'
                        customerFolder =  workspace_slug+'-folder'
                        newUrlPath = customerFolder+'/'+os.path.basename(url_location)
                        move_response = AnythingLLMService.moveFile(url_location, 
                                                newUrlPath)
                        url_json = {"adds": [ newUrlPath ]}
                        update_embeddings_response = AnythingLLMService.updateEmbeddings(workspace_slug, url_json)
                        #record the embedded url and its web conent into the returing list
                        ret_list.append ({'url':link['url'], 'last_modified': link['last_modified'], 'jsonfile_path': newUrlPath})
                    else:
                        print('Error:', response.status_code)
                except requests.exceptions.RequestException as e:
                    print('Error:', e)
                    raise   
                except Exception as e:
                    print('Error:', e)
                    raise                
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            raise  
        except Exception as e:
            print('Error:', e)
            raise  
        return ret_list

    @staticmethod
    def updatefromBaseURL(workspace_slug: str, base_url: str, url_filter: str, valid_after: str, crawl_depth=2, max_url_count=100) -> str:
        """update all links newer than the last update time"""

        ret_list = {}
        try:
            #retrieve all valid urls
            link_list = asyncio.run(scrap_urls_service.scrap_urls (base_url, url_filter, valid_after, crawl_depth, max_url_count))
            if len(link_list)>0:
                document_response = AnythingLLMService.getDocuments()
                if document_response:
                    documents_by_folders = document_response['localFiles']['items']
                    document_list=None
                    for dl in documents_by_folders:
                        if dl['name']==workspace_slug:
                            document_list = dl
                            break
                    if not document_list:
                        #if the web link json file contain the baseurl
                        # delete it
                        for doc in document_list:
                            if doc['url']:
                                normalized_url = doc['chunkSource'].replace(r"link://", "").rstrip('/')
                                if (base_url not in normalized_url or not (normalized_url in links)): 
                                    AnythingLLMService.deleteFile (doc['name'])
                                #TBA: check if last_modifed_date is new comparing to the one in the existing embeded list
                                    

                #upload the url
                for  link in link_list:
                    response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/upload-link/", 
                        json = {"link": link['url']},
                        headers=AnythingLLMService.headers,
                        verify=False)
                    if response.status_code == 200:
                        upload_response = response.json()
                        url_location = upload_response['documents'][0]['location']
                        print (upload_response['documents'][0]['location'])
                        #move those URLs/contents to the destination workspace
                        #???TBA customerFolder is always workspace_slug+'-folder'
                        customerFolder =  workspace_slug+'-folder'
                        newUrlPath = customerFolder+'/'+os.path.basename(url_location)
                        move_response = AnythingLLMService.moveFile(url_location, 
                                                newUrlPath)
                        url_json = {"adds": [ newUrlPath ]}
                        update_embeddings_response = AnythingLLMService.updateEmbeddings(workspace_slug, url_json) 
                    else:
                        print('Error:', response.status_code)
                    
                    ret_list = ret_list.append ({'url':link['url'], 'last_modified': link['last_modified'], 'jsonfile_path': newUrlPath})
            
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None  
        
    @staticmethod
    def deletefromBaseURL(workspace_slug: str, baseurl: str, ) -> str:
        """delete all links within the root url"""
        ret_delete_list=[]
        try:
            #get all documents from the workspace
            documents = AnythingLLMService.getDocuments()
            if documents:
                for doc in documents['localFiles']['items']:
                    #check the target folder
                    if doc['name']==workspace_slug+'-folder' and len(doc['items'])>0:
                        delete_list=[]
                        for item in doc['items']:
                            #delete_list=[]
                            if (item['url']):
                                #check if the web link json file contain the baseurl
                                # delete it 
                                if baseurl in item['chunkSource']:
                                    filepath=workspace_slug+'-folder/'+item['name']
                                    delete_list.append(filepath)
                                    ret_delete_list.append (item['chunkSource'])
                        
                        if delete_list and len(delete_list) > 0: 
                            print("delete -- ", delete_list)
                            AnythingLLMService.deleteFile (delete_list)
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None 
        return ret_delete_list    

    @staticmethod
    def uploadLink(urlLink: str) -> str:
        """add a url link under current LLM service platform."""
        try:
            #TBA: validate the url link 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/upload-link/", 
                json = {"link": urlLink},
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None  

    @staticmethod
    def uploadRawText(rawTextObject: Any) -> str:
        """add rawText Object to current LLM service platform."""
        try:
            #TBA: validate the url link 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/raw-text/", 
                json = rawTextObject,
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None 

    @staticmethod
    def getDocuments() -> str:
        """List of all locally-stored documents in instance"""
        try:
            #TBA: validate the url link 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"documents", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None


    @staticmethod
    def getAcceptableFileTypes() -> str:
        """Check available filetypes and MIMEs that can be uploaded."""
        try:
            #TBA: validate the url link 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"document/accepted-file-types", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def getSingleDocument(filename: str) -> str:
        """Get a single document by its unique AnythingLLM document name."""
        try:
            #TBA: validate the filename 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"document/filename", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def createFolder(folderName: str) -> str:
        """Create a new folder inside the documents storage directory."""
        try:
            #TBA: validate the folder name 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/create-folder", 
                json={'name' : folderName},
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None  

    @staticmethod
    def moveFile(from_filepath: str, to_filepath: str) -> str:
        """Move files within the documents storage directory."""
        try:
            #TBA: validate the folder name 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/move-files", 
                json={
                    "files": [
                        {
                            "from": from_filepath,
                            "to": to_filepath
                        }
                    ]
                },
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def deleteDocuments(filepathList: str) -> str:
        """Permanently remove documents from the system."""
        try:
            #TBA: validate the folder name 
            response = AnythingLLMService.session.delete(AnythingLLMService.anythingLLMurl+"system/remove-documents", 
                json={"names": filepathList},
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    #Workspaces Management 
    #----------------------
    @staticmethod
    def createWorkSpace(workspace: str) -> str:
        """create a new chat workspace for a business in LLM service platform."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/new", json={
                "name": workspace,
                "similarityThreshold": 0.7,
                "openAiTemp": 0.7,
                "openAiHistory": 20,
                "openAiPrompt": "Custom prompt for responses",
                "queryRefusalResponse": "Custom refusal message",
                "chatMode": "chat",
                "topN": 4}, 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def listAllWorkSpaces() -> str:
        """list all workspaces under current LLM service platform."""
        try:
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"workspaces",  
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None   
         
    @staticmethod
    def listOneWorkSpace(workspace_slug: str) -> str:
        """list one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug,  
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None   

    @staticmethod
    def deleteWorkSpace(workspace_slug: str) -> str:
        """delete one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.delete(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug,  
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = {"Status":"Success"}
                return posts
            else:
                print('Error:', response.status_code)
                return {"Status":"Failed", "Error": response.status_code}
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return {"Status":"Failed", "Error": e}  
        
    @staticmethod
    def updateWorkSpace(workspace_slug: str, updatedData: Any) -> str:
        """update one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug+"/update",
                json=updatedData,  
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None
    @staticmethod

    def getChatsfromWorkSpace(workspace_slug: str) -> str:
        """get chats from one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug+"/chats", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None 

    @staticmethod
    def updateEmbeddings(workspace_slug: str, docSet: Any) -> str:
        """add or remove embedding(s) from one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug+"/update-embeddings", 
                json=docSet,
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None
         
    @staticmethod
    def updatePins(workspace_slug: str, pinSet: Any) -> str:
        """add or remove pin on documents from one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug+"/update-pin", 
                json=pinSet,
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None 

    @staticmethod
    def performVectorSearch(workspace_slug: str, querySet: Any) -> str:
        """conduct vector search on documents from one workspace under current LLM service platform."""
        try:
            #TBA: validate the user input 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/"+workspace_slug+"/vector-search", 
                json=querySet,
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None
                                                      
    #Conversation Management
    #-----------------------
    @staticmethod
    def createChat(slug: str, chat_id: str) -> str:
        """create a new chat in one workspace in LLM service platform."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+f"organization/{slug}/thread/new", json={
                #"userId": 1, #TBA : need a regular user Id
                "name": chat_id,
                "slug": chat_id}, 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def updateChat(workspace_slug: str, conversation_id: str, updatedData: Any) -> str:
        """update a chat of one workspace in LLM service platform."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+f"organization/{workspace_slug}/thread/{conversation_id}/update", 
                json=updatedData, 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None

    @staticmethod
    def deleteChat(workspace_slug: str, conversation_id: str) -> str:
        """delete a chat of one workspace in LLM service platform."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.delete(AnythingLLMService.anythingLLMurl+f"organization/{workspace_slug}/thread/{conversation_id}", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = {"Status":"Success"}
                return posts
            else:
                print('Error:', response.status_code)
                return {"Status":"Failed", "Error": response.status_code}
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return {"Status":"Failed", "Error": e} 

    @staticmethod
    def getChat(workspace_slug: str, conversation_id: str) -> str:
        """get converastion from one chat of one workspace in LLM service platform."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.get(AnythingLLMService.anythingLLMurl+f"organization/{workspace_slug}/thread/{conversation_id}/chats", 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None                
    #Conversation Management
    @staticmethod
    def submitChat(workspace_slug: str, conversation_id: str, question: str) -> str:
        """submit a question into an existing conversation."""
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(
                AnythingLLMService.anythingLLMurl+f"organization/{workspace_slug}/thread/{conversation_id}/chat", 
                json={
                    "message": question,
                    "mode": "restaurant",
                    "userId": 1}, 
                headers=AnythingLLMService.headers,
                verify=False)
            if response.status_code == 200:
                posts = response.json()
                return posts
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None
        
    #The aggregation of Service Integration for AnythingLLM 
    #------------------------------------------------------------------
    @staticmethod
    def register_anythingLLM (businessname: str) -> str:
        """submit a request to create a workspace."""
        """return succ with a workspace_slug and customer folder in json format 
        or error with exception"""
        #Create a workspace
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/new", json={
                "name": businessname,
                "similarityThreshold": 0.5,
                "openAiTemp": 0.7,
                "openAiHistory": 20,
                "openAiPrompt": "Custom prompt for responses",
                "queryRefusalResponse": "Custom refusal message",
                "chatMode": "chat",
                "topN": 4}, 
                headers=AnythingLLMService.headers) 
            if response.status_code == 200:
                posts = response.json()
                workspace_slug = posts["workspace"]["slug"]
            else:
                print('Error:', response.status_code)
                raise Exception (response)
            #Create a customer folder
            customerFolder = workspace_slug+"-folder"
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"document/create-folder", 
                json={'name' : customerFolder},
                headers=AnythingLLMService.headers)
            if response.status_code == 200:
                posts = response.json()
                return {'workspace_slug': workspace_slug, 'customer_folder': customerFolder}
            else:
                print('Error:', response.status_code)
                raise Exception (response)
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise

    @staticmethod
    def upload_file (workspace_slug: str, destination_folder: str, filepath: str) -> str:
        """submit a request to upload a file."""
        """return a succ with filepath or error"""
        try:
            if os.path.exists(filepath):
                #Convert to MD
                md = MarkItDown()
                result = md.convert(filepath)
                with open(os.path.basename(filepath)+'.md', "w", encoding="utf-8") as md_file:
                    md_file.write(result.text_content)
                # upload the file
                md_file_path = os.path.abspath(md_file.name) 
                response3 = AnythingLLMService.uploadDocument(md_file_path)
                #move the file to the destination folder (i.e. customer folder)
                file_location = response3['documents'][0]['location']
                print (response3['documents'][0]['location'])
                newFilePath = destination_folder+'/'+os.path.basename(file_location)
                AnythingLLMService.moveFile(file_location, 
                                                newFilePath)
                #embed the uploaded file
                doc_json = {"adds": [ newFilePath]}
                response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
                #delete this md file
                os.remove(md_file_path)
                return newFilePath
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise

    @staticmethod
    def upload_link (workspace_slug: str, base_url: str, options: Any) -> str:
        """submit a request to upload website."""
        """return a succ with uploaded urls or error"""
        try:
            AnythingLLMService.uploadfromBaseURL(workspace_slug, base_url, options['url_filter'], options['valid_after'], options['crawl_depth'], options['max_url_count']) 
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise
        #scrap the webpages per the criteria within the options  
        #Convert the webpages to MD (need or not??)
        #upload the webpages to the common folder
        # move the webpages to the destination folder (i.e. customer folder)
        #embed the uploaded webpage

    @staticmethod
    def replace_file (workspace_slug: str, destination_folder: str, origin_filepath: str, newfilepath: str) -> str:
        """submit a request to upload  a file."""
        """return a succ with uploaded filepath or error with exception """
        #Convert the new file to MD
        #upload the new file  to the common folder
        # move the new file to the destination folder (i.e. customer folder)
        #embed the new file/delete the original embeding file
        #delete the original file
        try:
            if os.path.exists(newfilepath):
                #Convert to MD
                md = MarkItDown()
                result = md.convert(newfilepath)
                with open(os.path.basename(newfilepath)+'.md', "w") as md_file:
                    md_file.write(result.text_content)
                #upload the new file  to the common folder 
                md_file_path = os.path.abspath(md_file.name) 
                response3 = AnythingLLMService.uploadDocument(md_file_path)
                # move the new file to the destination folder (i.e. customer folder)
                file_location = response3['documents'][0]['location']
                print (response3['documents'][0]['location'])
                newFilePath = destination_folder+'/'+os.path.basename(file_location)
                AnythingLLMService.moveFile(file_location, 
                                                newFilePath)
                #embed the new file/delete the original embeding file
                doc_json = {"adds": [ newFilePath], "deletes":[ origin_filepath ]}
                response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
                #delete the original file
                #os.remove(origin_filepath)
                AnythingLLMService.deleteDocuments( [origin_filepath] )
                #delete this md file
                os.remove(md_file_path)
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise

    @staticmethod
    def delete_file (workspace_slug: str, origin_filepath: str) -> str:
        """submit a request to delete  a file."""
        """return a succ  or error with exception """
        #unembed tthe original embeding file
        doc_json = {"deletes":[ origin_filepath ]}
        response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
        #delete the original file
        AnythingLLMService.deleteDocuments( [origin_filepath] )

    @staticmethod
    def delete_customer_anythingLLM (workspace_slug: str, customer_folder: str) -> str:
        """submit a request to delete  a customer."""
        """return a succ  or error with exception """
        #get the files belonged to this customer
        if (workspace_slug and len(workspace_slug.strip())>0) \
            and (customer_folder and len(customer_folder.strip())>0):
                document_response = AnythingLLMService.getDocuments()
                if document_response:
                    documents_by_folders = document_response['localFiles']['items']
                    document_list=None
                    for dl in documents_by_folders:
                        if dl['name']==customer_folder:
                            document_list = dl['items']
                            break
                    if (document_list):
                        file_list=[]
                        for doc in document_list:
                        #umbeded the customer's files
                            file_list.append(customer_folder+'/'+doc['name'])
                        if (len(file_list)>0):
                            doc_json = {"deletes":file_list}
                            response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
                            #delete the customer's files
                            AnythingLLMService.deleteDocuments(file_list)
                #delete the workspace
                response15 = AnythingLLMService.deleteWorkSpace(workspace_slug)
    
    #The aggregation of Service Integration for AiDA RAGLLM  
    #------------------------------------------------------------------
    @staticmethod
    def register (businessname: str) -> str:
        """submit a request to create a workspace."""
        """return succ with a workspace_slug and empty customer folder in json format 
        or error with exception"""
        #Create a workspace
        try:
            #TBA: validate the workspace 
            response = AnythingLLMService.session.post(AnythingLLMService.anythingLLMurl+"organization/new", json={
                "name": businessname,
                "similarityThreshold": 0.5,
                "openAiTemp": 0.7,
                "openAiHistory": 20,
                "openAiPrompt": "Custom prompt for responses",
                "queryRefusalResponse": "Custom refusal message",
                "chatMode": "chat",
                "topN": 4}, 
                headers=AnythingLLMService.headers) 
            if response.status_code == 200:
                posts = response.json()
                workspace_slug = posts["workspace"]["slug"]
            else:
                print('Error:', response.status_code)
                raise Exception (response)
            if response.status_code == 200:
                posts = response.json()
                return {'workspace_slug': workspace_slug, 'customer_folder': ""}
            else:
                print('Error:', response.status_code)
                raise Exception (response)
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise

    @staticmethod
    def upload_file_aida (workspace_slug: str, destination_folder: str, filepath: str) -> str:
        """submit a request to upload a file."""
        """return a succ with filepath or error"""
        try:
            if os.path.exists(filepath):
                #Convert to MD
                md = MarkItDown()
                result = md.convert(filepath)
                with open(os.path.basename(filepath)+'.md', "w", encoding="utf-8") as md_file:
                    md_file.write(result.text_content)
                # upload the file
                md_file_path = os.path.abspath(md_file.name) 
                response3 = AnythingLLMService.uploadDocument(md_file_path)
                #move the file to the destination folder (i.e. customer folder)
                file_location = response3['documents'][0]['location']
                print (response3['documents'][0]['location'])
                newFilePath = destination_folder+'/'+os.path.basename(file_location)
                AnythingLLMService.moveFile(file_location, 
                                                newFilePath)
                #embed the uploaded file
                doc_json = {"adds": [ newFilePath]}
                response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
                #delete this md file
                os.remove(md_file_path)
                return newFilePath
        except Exception as err:
            print(f"Unexpected {err=}, {type(err)=}")
            raise

    @staticmethod
    def delete_file_aida (workspace_slug: str, origin_filepath: str) -> str:
        """submit a request to delete  a file."""
        """return a succ  or error with exception """
        #unembed tthe original embeding file
        doc_json = {"deletes":[ origin_filepath ]}
        response7 = AnythingLLMService.updateEmbeddings(workspace_slug, doc_json) 
        #delete the original file
        AnythingLLMService.deleteDocuments( [origin_filepath] )

    @staticmethod
    def delete_customer_aida (workspace_slug: str, customer_folder: str) -> str:
        """submit a request to delete  a customer."""
        """return a succ  or error with exception """
        #get the files belonged to this customer
        if (workspace_slug and len(workspace_slug.strip())>0):
            #delete the workspace
            response15 = AnythingLLMService.deleteWorkSpace(workspace_slug)
    
    @staticmethod
    def summarizeOrder (workspace_slug: str, chat_slug: str) -> str:
        """request the LLM to return the order list and the price"""
        try:
            #TBA: validate the workspace 
            user_prompt = """
 Please return only the order list in JSON format
"""
            response = AnythingLLMService.session.post(
                AnythingLLMService.anythingLLMurl+f"organization/{workspace_slug}/thread/{chat_slug}/chat", 
                json={
                    "message": user_prompt,
                    "mode": "restaurant",
                    "userId": 1}, 
                headers=AnythingLLMService.headers)
            if response.status_code == 200:
                # Parse the entire JSON response
                print(response.text)
                response_json = json.loads(response.text)
                
                # Extract the textResponse field
                text_response = response_json['textResponse']
                
                # Find the first occurrence of a JSON object in the response
                import re
                json_match = re.search(r'\{[\s\S]*\}', text_response)
                if json_match:
                    try:
                        # Extract and clean the JSON string
                        json_str = json_match.group(0)
                        # Remove any escaped quotes
                        json_str = json_str.replace('\\"', '"')
                        # Parse the JSON
                        dishes_json = json.loads(json_str)
                        return dishes_json
                    except json.JSONDecodeError as e:
                        print(f"Error parsing dishes JSON: {e}")
                        print(f"Problematic JSON string: {json_str}")
                        return None
                else:
                    print("No JSON object found in response")
                    return None
            else:
                print('Error:', response.status_code)
                return None
        except requests.exceptions.RequestException as e:
            print('Error:', e)
            return None
