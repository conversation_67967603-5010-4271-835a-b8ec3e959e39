from typing import Optional, Dict
from ..models import Account, AccountWebsite
import logging

logger = logging.getLogger(__name__)

class ChatConfigService:
    @staticmethod
    def get_config_for_domain(domain: str) -> Optional[Dict]:
        """
        Get chat configuration based on the domain's associated account.
        Returns None if no configuration is found.
        """
        # Remove port if present
        if ':' in domain:
            domain = domain.split(':')[0]
        
        try:
            # Find the account website by domain
            account_website = AccountWebsite.objects.select_related('account').get(domain=domain)
            account = account_website.account

            # Get the account config
            account_config = account.config
            
            # Build the config dictionary with default values
            config = {
                'enabled': True,
                'account_uuid': str(account.uuid),
                'llm_slug': account.llm_slug,
                'initial_message': "Hello! How can I help you today?",
                'chat_title': "AiDA",  # Default chat title
                'theme': 'light',  # Default theme
                'chat_icon': account.chat_icon.url if account.chat_icon else '/images/chat.png',
                'order_taking_enabled': False,
                'order_taking_email': '',
            }
            
            # If account config exists, override defaults with actual values
            if hasattr(account, 'config') and account.config:
                config.update({
                    'initial_message': getattr(account.config, 'greeting_message', config['initial_message']),
                    'chat_title': getattr(account.config, 'chat_title', config['chat_title']),
                    'theme': getattr(account.config, 'theme', config['theme']),
                    'order_taking_enabled': getattr(account.config, 'order_taking_enabled', config['order_taking_enabled']),
                })
                
                # Only add email if order taking is enabled
                if account.config.order_taking_enabled and account.config.order_taking_email:
                    config['order_taking_email'] = account.config.order_taking_email

            # # Customize based on subscription level
            # if account.subscription_level == 'free':
            #     config.update({
            #         'max_messages_per_day': 50,
            #         'features': ['basic_chat']
            #     })
            # elif account.subscription_level == 'pro':
            #     config.update({
            #         'max_messages_per_day': 500,
            #         'features': ['basic_chat', 'file_upload', 'custom_theme']
            #     })
            # elif account.subscription_level == 'enterprise':
            #     config.update({
            #         'max_messages_per_day': None,  # unlimited
            #         'features': ['basic_chat', 'file_upload', 'custom_theme', 'multi_language']
            #     })

            return config

        except AccountWebsite.DoesNotExist:
            return None
