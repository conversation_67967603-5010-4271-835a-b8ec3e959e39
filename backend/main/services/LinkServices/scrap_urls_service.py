from datetime import datetime
import json
from typing import Any
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
from dateutil import parser
from dateutil.parser import parse
import pytz
utc=pytz.UTC

class SimpleWebsiteScraper:
    base_url = ''
    max_depth = 2
    max_url_num=100
    url_filter = []
    valid_after = ''
    navg_links = []
    valid_links = []
    df = pd.DataFrame()
    count = 1
    def __init__(self, baseurl: str, url_filter: str, valid_after: str, max_depth=2, max_url_num=100):
        self.base_url = baseurl.rstrip('/')
        self.max_depth = max_depth
        self.max_url_num = max_url_num
        self.url_filter = url_filter
        self.valid_after = valid_after
        self.navg_links = [f'{self.base_url}']

    def get_path_depth(self, baseurl: str, this_url: str)->int :
        path = this_url.rstrip("/")  # Remove leading and trailing slashes
        if path != "" and baseurl in path :
            return len(path.replace(baseurl, '').split("/"))-1
        return 0

    def is_date(self, string, fuzzy=False)->bool:
        try: 
            parse(string, fuzzy=fuzzy)
            return True
        except ValueError:
            return False
    
    def extract_published_date (self, response:Any) ->str:
        # Parse the webpage content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Attempt to find the published date in meta tags
        published_date = None
        
        # Common meta tags for publication date
        date_meta_tags = [
            {"property": "article:published_time"},
            {"name": "pubdate"},
            {"name": "publish-date"},
            {"name": "article.published"},
            {"itemprop": "datePublished"}
        ]
        
        for meta_tag in date_meta_tags:
            tag = soup.find("meta", attrs=meta_tag)
            if tag and tag.get("content"):
                try:
                    published_date = datetime.strptime(tag.get("content"), "%Y-%m-%dT%H:%M:%S%z")
                    break
                except ValueError:
                    continue
        
        # Alternative: Search for visible elements (e.g., by class name or ID)
        if not published_date:
            # Example: Look for a class or ID commonly used for publication dates
            possible_elements = soup.select("[class*=date], [id*=date]")
            for element in possible_elements:
                text = element.get_text(strip=True)
                if text:
                    if self.is_date(text, fuzzy=False):
                        try:
                            #donot know the datetime format thus use universal datetime parser
                            published_date = parser.parse(text).replace(tzinfo=utc)
                            break
                        except ValueError:
                            continue
        if not published_date:
            # Find JSON-LD scripts
            json_ld_scripts = soup.find_all("script", type="application/ld+json")
        
            for script in json_ld_scripts:
                try:
                    # Parse the JSON content
                    json_data = json.loads(script.string)
                
                    # Check if the JSON contains "@graph" (a common structure in JSON-LD)
                    if "@graph" in json_data:
                        for item in json_data["@graph"]:
                            if "dateModified" in item:
                                published_date = parser.parse(item['dateModified']).replace(tzinfo=utc)
                                #return f"Date Modified: {item['dateModified']}"
                                break
                            elif "datePublished" in item:
                                published_date = parser.parse(item['datePublished']).replace(tzinfo=utc)
                                break
                    # If "@graph" is not present, check for direct "dateModified"
                    elif "dateModified" in json_data:
                        if json_data['dateModified']:
                            published_date = parser.parse(json_data['dateModified']).replace(tzinfo=utc)
                            break
                        elif json_data['datePublished']:
                            published_date = parser.parse(json_data['datePublished']).replace(tzinfo=utc)
                            break

                except json.JSONDecodeError:
                    continue  # Skip invalid JSON

        # Output the result
        if published_date:
            print(f"Published Date: {published_date}")
        else:
            print("Published date not found.")
    
        return published_date
    #except requests.exceptions.RequestException as e:
    #    print(f"An error occurred: {e}")

    def valid_with_filter (self, link: str, inclusive_filter: str) -> bool:
        #if not filter option, simply return True
        if (inclusive_filter and inclusive_filter != ''):
            # Make a regex that matches if any of our regexes match.
            combined = "(" + ")|(".join(inclusive_filter) + ")"

            if not re.match(combined, link):
                print ('not matched url', link)
                return False
        return True

    def get_last_modified_time (self, response: str) -> str:
        if not response:  
            return None   

        last_modified_date = None
        if ("Last-Modified" in response.headers):
            last_modified_date = datetime.strptime(response.headers["Last-Modified"], "%a, %d %b %Y %H:%M:%S GMT").replace(tzinfo=utc)
            print ( "found the last modified date", last_modified_date)

        # if no last_modified date, use the published date instead
        if not last_modified_date:
            last_modified_date = self.extract_published_date (response)

        return last_modified_date

    def is_within_valid_time(self, last_modified_date: str, valid_after: str) -> bool:
        if not valid_after or valid_after == '':
            return True

        valid_after_ts =datetime.strptime(valid_after, "%Y-%m-%d").replace(tzinfo=utc) if (valid_after and valid_after != '') else None
        is_after_valid = True  

        if (valid_after_ts):
            if last_modified_date:
                if valid_after_ts <= last_modified_date: 
                    is_after_valid = True
                else:
                    is_after_valid = False
            else:
                # by default, return True
                print ('neither published data nor last modified date in the web page')

        return is_after_valid
    
    async def extract_links(self, url: str):
        #global valid_links, navg_links, count
        try:
            response = requests.get(url)
            # the last modified time of this web page is older, remove it
            # even if it is old than the valid time, we will still conitnue to scrap the urls within this page
            last_modified_time = self.get_last_modified_time (response)
            if self.valid_after:
                if self.is_within_valid_time(last_modified_time, self.valid_after):
                    self.valid_links.append ({'url':url.rstrip('/'), 'last_modified': last_modified_time})
                    print(f"{self.count} - source url",url)
                    self.count=self.count+1
            else:
                self.valid_links.append ({'url':url.rstrip('/'), 'last_modified': last_modified_time})
                print(f"{self.count} - source url",url)
                self.count=self.count+1   
            soup = BeautifulSoup(response.text,"html.parser")
            for link in soup.find_all('a',href=True):
                if len(self.valid_links) >=self.max_url_num:
                    print ('reach the max number of links', self.max_url_num)
                    return
                url_str = link.get('href').rstrip('/')
                path_depth = self.get_path_depth(self.base_url, url_str )
                if url_str.startswith("http") \
                    and url_str not in self.navg_links \
                    and url_str.startswith(self.base_url) \
                    and (0 < path_depth) and (path_depth <= self.max_depth) \
                    and self.valid_with_filter (url_str, self.url_filter) :
                    self.navg_links.append(url_str)
                    await self.extract_links(url_str)

        except Exception as e:
            print("Unhandled exception",e)

    async def scrap(self):
        """Start the scraping process asynchronously."""
        await self.extract_links(self.base_url)  # ✅ Ensure async call is awaited
        return self.valid_links

class scrap_urls_service:
    @staticmethod
    def scrap_urls (baseurl: str, url_filter: str, valid_after: str, max_depth=2, max_url=100, ) -> Any:
        """
        scrap the valida urls from the webpage specified by the baseurl 
        """
        scraper = SimpleWebsiteScraper (baseurl, url_filter, valid_after, max_depth, max_url)
        return scraper.scrap()

