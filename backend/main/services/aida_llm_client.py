import logging
import mimetypes
import os
from typing import Any, Dict, Optional

import requests


logger = logging.getLogger(__name__)


class AidaLLMClientError(Exception):
    """Raised when the AIDA LLM service returns an error response."""


class AidaLLMClient:
    """HTTP client for interacting with the aida-llm service document API."""

    def __init__(
        self,
        base_url: Optional[str] = None,
        api_token: Optional[str] = None,
        session: Optional[requests.Session] = None,
    ) -> None:
        self.base_url = self._normalize_base_url(
            base_url or os.getenv("AIDA_LLM_URL", "http://localhost:8000/api/v1/")
        )
        self.api_token = api_token or os.getenv("AIDA_LLM_API_TOKEN")
        self.session = session or requests.Session()

    @staticmethod
    def _normalize_base_url(url: str) -> str:
        return url if url.endswith("/") else f"{url}/"

    def _headers(self) -> Dict[str, str]:
        headers: Dict[str, str] = {}
        if self.api_token:
            headers["Authorization"] = f"Bearer {self.api_token}"
        return headers

    def upload_document(self, organization_slug: str, file_path: str, mime_type: Optional[str] = None) -> Dict[str, Any]:
        """Upload a document to the specified organization."""
        if not organization_slug:
            raise ValueError("organization_slug is required")
        if not os.path.exists(file_path):
            raise FileNotFoundError(file_path)

        guessed_mime = mime_type or mimetypes.guess_type(file_path)[0] or "application/octet-stream"
        url = f"{self.base_url}organization/{organization_slug}/document"
        logger.info("Uploading document %s to %s", os.path.basename(file_path), url)

        with open(file_path, "rb") as file_handle:
            files = {"content": (os.path.basename(file_path), file_handle, guessed_mime)}
            response = self.session.post(url, headers=self._headers(), files=files, timeout=60)

        if response.status_code >= 400:
            logger.error(
                "AIDA LLM upload failed for %s: %s %s", organization_slug, response.status_code, response.text
            )
            raise AidaLLMClientError(
                f"Upload failed with status {response.status_code}: {response.text}"
            )

        try:
            payload = response.json()
        except ValueError as exc:  # pragma: no cover - defensive
            logger.error("Invalid JSON response from AIDA LLM: %s", response.text)
            raise AidaLLMClientError("Invalid JSON response from AIDA LLM") from exc

        document_info = payload.get("document")
        if not document_info:
            raise AidaLLMClientError("Upload succeeded but response missing 'document'")

        return document_info

    def delete_document(self, organization_slug: str, document_id: int) -> None:
        """Delete a document from the specified organization."""
        if not organization_slug:
            raise ValueError("organization_slug is required")
        if document_id is None:
            raise ValueError("document_id is required")

        url = f"{self.base_url}organization/{organization_slug}/document"
        logger.info(
            "Deleting document %s from organization %s via %s", document_id, organization_slug, url
        )

        response = self.session.delete(
            url,
            headers={**self._headers(), "Content-Type": "application/json"},
            json={"document_id": document_id},
            timeout=30,
        )

        if response.status_code >= 400:
            logger.error(
                "AIDA LLM delete failed for %s/%s: %s %s",
                organization_slug,
                document_id,
                response.status_code,
                response.text,
            )
            raise AidaLLMClientError(
                f"Delete failed with status {response.status_code}: {response.text}"
            )
