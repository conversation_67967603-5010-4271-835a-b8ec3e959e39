"""
Utility functions for normalizing business names and addresses
to improve duplicate detection
"""

import re
import difflib


def normalize_business_name(name):
    """
    Normalizes a business name by:
    - Converting to lowercase
    - Removing leading/trailing whitespace
    - Removing special characters
    - Standardizing spaces
    - Removing common business suffixes
    - Standardizing common abbreviations
    - Removing articles (a, an, the)

    Args:
        name (str): The business name to normalize

    Returns:
        str: Normalized business name
    """
    if not name:
        return ""

    # Convert to lowercase and trim
    normalized = name.lower().strip()
    
    # Remove special characters except hyphens and ampersands
    normalized = re.sub(r'[^\w\s\-&]', '', normalized)
    
    # Standardize spaces
    normalized = re.sub(r'\s+', ' ', normalized)
    
    # Remove common business suffixes
    normalized = re.sub(r'\b(inc|llc|ltd|corp|corporation|incorporated|company|co|inc\.|llc\.|ltd\.|corp\.|co\.|pllc|pc|lp|llp|gmbh)\b', '', normalized)
    
    # Replace common abbreviations
    replacements = {
        r'\b&\b': 'and',
        r'\bintl\b': 'international',
        r'\bsvcs\b': 'services',
        r'\bmfg\b': 'manufacturing',
        r'\btech\b': 'technology'
    }
    
    for pattern, replacement in replacements.items():
        normalized = re.sub(pattern, replacement, normalized)
    
    # Remove articles
    normalized = re.sub(r'^the\s+', '', normalized)
    normalized = re.sub(r'^a\s+', '', normalized)
    normalized = re.sub(r'^an\s+', '', normalized)
    
    # Final cleanup
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    return normalized


def calculate_string_similarity(str1, str2):
    """
    Calculates string similarity using SequenceMatcher

    Args:
        str1 (str): First string
        str2 (str): Second string

    Returns:
        float: Similarity score between 0 and 1 (1 = exact match)
    """
    if not str1 and not str2:
        return 1.0
    if not str1 or not str2:
        return 0.0
    
    return difflib.SequenceMatcher(None, str1, str2).ratio()


def levenshtein_distance(str1, str2):
    """
    Calculates the Levenshtein distance between two strings

    Args:
        str1 (str): First string
        str2 (str): Second string

    Returns:
        int: The Levenshtein distance
    """
    if not str1 and not str2:
        return 0
    if not str1:
        return len(str2)
    if not str2:
        return len(str1)
    
    # Create a matrix of size (len(str1)+1) x (len(str2)+1)
    matrix = [[0 for _ in range(len(str2) + 1)] for _ in range(len(str1) + 1)]
    
    # Fill the first row and column
    for i in range(len(str1) + 1):
        matrix[i][0] = i
    for j in range(len(str2) + 1):
        matrix[0][j] = j
    
    # Fill the rest of the matrix
    for i in range(1, len(str1) + 1):
        for j in range(1, len(str2) + 1):
            cost = 0 if str1[i-1] == str2[j-1] else 1
            matrix[i][j] = min(
                matrix[i-1][j] + 1,  # deletion
                matrix[i][j-1] + 1,  # insertion
                matrix[i-1][j-1] + cost  # substitution
            )
    
    return matrix[len(str1)][len(str2)]


def levenshtein_similarity(str1, str2):
    """
    Calculates the Levenshtein similarity ratio between two strings

    Args:
        str1 (str): First string
        str2 (str): Second string

    Returns:
        float: Similarity ratio between 0 and 1 (1 = exact match)
    """
    if not str1 and not str2:
        return 1.0
    if not str1 or not str2:
        return 0.0
    
    distance = levenshtein_distance(str1, str2)
    max_len = max(len(str1), len(str2))
    
    if max_len == 0:
        return 1.0
    
    return 1.0 - (distance / max_len)


def normalize_address(address):
    """
    Normalizes an address by:
    - Converting to lowercase
    - Removing leading/trailing whitespace
    - Standardizing spaces
    - Removing special characters except commas
    - Removing unit/suite/apartment numbers
    - Standardizing common address abbreviations

    Args:
        address (str): The address to normalize

    Returns:
        str: Normalized address
    """
    if not address:
        return ""

    # Convert to lowercase and trim
    normalized = address.lower().strip()
    
    # Standardize spaces
    normalized = re.sub(r'\s+', ' ', normalized)
    
    # Standardize address abbreviations
    abbreviations = {
        r'\bstreet\b': 'st',
        r'\bst\.\b': 'st',
        r'\bavenue\b': 'ave',
        r'\bave\.\b': 'ave',
        r'\bboulevard\b': 'blvd',
        r'\bblvd\.\b': 'blvd',
        r'\broad\b': 'rd',
        r'\brd\.\b': 'rd',
        r'\bdrive\b': 'dr',
        r'\bdr\.\b': 'dr',
        r'\blane\b': 'ln',
        r'\bln\.\b': 'ln',
        r'\bcourt\b': 'ct',
        r'\bct\.\b': 'ct',
        r'\bcircle\b': 'cir',
        r'\bcir\.\b': 'cir',
        r'\bhighway\b': 'hwy',
        r'\bhwy\.\b': 'hwy',
        r'\bparkway\b': 'pkwy',
        r'\bpkwy\.\b': 'pkwy',
        r'\bplace\b': 'pl',
        r'\bpl\.\b': 'pl',
        r'\bsquare\b': 'sq',
        r'\bsq\.\b': 'sq',
        r'\bnorth\b': 'n',
        r'\bn\.\b': 'n',
        r'\bsouth\b': 's',
        r'\bs\.\b': 's',
        r'\beast\b': 'e',
        r'\be\.\b': 'e',
        r'\bwest\b': 'w',
        r'\bw\.\b': 'w',
        r'\bnortheast\b': 'ne',
        r'\bne\.\b': 'ne',
        r'\bnorthwest\b': 'nw',
        r'\bnw\.\b': 'nw',
        r'\bsoutheast\b': 'se',
        r'\bse\.\b': 'se',
        r'\bsouthwest\b': 'sw',
        r'\bsw\.\b': 'sw'
    }
    
    for pattern, replacement in abbreviations.items():
        normalized = re.sub(pattern, replacement, normalized)
    
    # Remove unit/apt numbers and special characters
    normalized = re.sub(r'\b(suite|ste|apt|apartment|unit|#|no|number)\s*[\w\-]+\b', '', normalized, flags=re.IGNORECASE)
    normalized = re.sub(r'[^\w\s,]', '', normalized)
    
    # Final cleanup
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    return normalized


def is_similar_business_name(name1, name2, threshold=0.8):
    """
    Checks if two business names are similar using both exact matching of normalized forms
    and fuzzy matching with Levenshtein distance

    Args:
        name1 (str): First business name
        name2 (str): Second business name
        threshold (float): Similarity threshold (0-1)

    Returns:
        bool: True if names are similar
    """
    normalized1 = normalize_business_name(name1)
    normalized2 = normalize_business_name(name2)
    
    # Exact match on normalized strings
    if normalized1 == normalized2:
        return True
    
    # Check if one name is contained within the other (to prevent false positives)
    # For example, "Acme" and "Beta" should not match even if they have some words in common
    tokens1 = set(normalized1.split())
    tokens2 = set(normalized2.split())
    
    # If the key business name tokens are different, require higher similarity
    common_tokens = tokens1.intersection(tokens2)
    if not common_tokens:
        # No common tokens means these are likely different businesses
        return False
    
    # If there are some common words but significant differences, apply stricter threshold
    different_tokens1 = tokens1 - tokens2
    different_tokens2 = tokens2 - tokens1
    
    # If both names have unique identifying words, they're likely different
    if different_tokens1 and different_tokens2:
        # Extract the first word (typically the main business name) and compare
        first_word1 = normalized1.split()[0] if normalized1 else ""
        first_word2 = normalized2.split()[0] if normalized2 else ""
        
        # If first words are completely different, they're different businesses
        if first_word1 != first_word2 and len(first_word1) > 3 and len(first_word2) > 3:
            word_similarity = levenshtein_similarity(first_word1, first_word2)
            if word_similarity < 0.7:  # Stricter threshold for key business identifiers
                return False
    
    # Fuzzy match using Levenshtein distance with adjusted threshold
    similarity = levenshtein_similarity(normalized1, normalized2)
    return similarity >= threshold


def is_similar_address(address1, address2, threshold=0.7):
    """
    Checks if two addresses are similar using both exact matching of normalized forms
    and fuzzy matching with Levenshtein distance

    Args:
        address1 (str): First address
        address2 (str): Second address
        threshold (float): Similarity threshold (0-1), lowered to catch more typos

    Returns:
        bool: True if addresses are similar
    """
    normalized1 = normalize_address(address1)
    normalized2 = normalize_address(address2)
    
    # Exact match on normalized strings
    if normalized1 == normalized2:
        return True
    
    # Handle common typos in street names
    common_typos = {
        'mian': 'main',
        'stret': 'street',
        'streat': 'street',
        'avenu': 'avenue',
        'aveue': 'avenue',
        'boulevrd': 'boulevard',
        'blvd': 'boulevard',
        'pkwy': 'parkway',
        'prkway': 'parkway'
    }
    
    # Apply typo corrections to both addresses
    corrected1 = normalized1
    corrected2 = normalized2
    
    for typo, correction in common_typos.items():
        corrected1 = re.sub(r'\b' + typo + r'\b', correction, corrected1, flags=re.IGNORECASE)
        corrected2 = re.sub(r'\b' + typo + r'\b', correction, corrected2, flags=re.IGNORECASE)
    
    # Check if the corrected addresses match
    if corrected1 == corrected2:
        return True
    
    # Extract street number (if present) and compare
    num_pattern = r'^\d+'
    num1 = re.search(num_pattern, normalized1)
    num2 = re.search(num_pattern, normalized2)
    
    # If street numbers are present and different, addresses are different
    if num1 and num2 and num1.group() != num2.group():
        return False
    
    # If street numbers are the same, this is a strong indicator of similarity
    if num1 and num2 and num1.group() == num2.group():
        # Extract the street name (words after the number)
        street1 = ' '.join(normalized1.split()[1:])
        street2 = ' '.join(normalized2.split()[1:])
        
        # Use a lower threshold for street name comparison when numbers match
        street_similarity = levenshtein_similarity(street1, street2)
        if street_similarity >= 0.6:  # Lower threshold for street names when numbers match
            return True
    
    # Fuzzy match using Levenshtein distance with lower threshold for typos
    similarity = levenshtein_similarity(normalized1, normalized2)
    if similarity >= threshold:
        return True
        
    # As a last resort, check similarity between corrected addresses
    corrected_similarity = levenshtein_similarity(corrected1, corrected2)
    return corrected_similarity >= threshold 