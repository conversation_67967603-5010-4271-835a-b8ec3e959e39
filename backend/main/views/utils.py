from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import secrets
from django.middleware.csrf import get_token
from django.http import JsonResponse
from urllib.parse import urlparse
from main.models import Account

def extract_domain_from_referer(referer: str) -> str:
    """Extract and normalize domain from referer header"""
    if not referer:
        return None

    domain = referer.lower()
    if '://' in domain:
        domain = domain.split('://')[1]
    domain = domain.split('/')[0]

    # Remove port if present
    if ':' in domain:
        domain = domain.split(':')[0]

    # Remove www prefix if present
    if domain.startswith('www.'):
        domain = domain[4:]

    return domain

class CsrfTokenView(APIView):
    def get(self, request):
        try:
            # Generate a secure CSRF token
            csrf_token = secrets.token_hex(32)
            return Response({'csrfToken': csrf_token}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

csrf_token_view = CsrfTokenView.as_view()

def get_account_from_referer(referer):
    """
    Extract account from a referer URL by matching domain with account websites.
    
    Args:
        referer (str): The referer URL
        
    Returns:
        Account or None: The account if found, None otherwise
    """
    if not referer:
        return None
        
    domain = urlparse(referer).netloc
    # Remove port if present
    if ':' in domain:
        domain = domain.split(':')[0]
    
    # Use both urlparse and custom extractor
    extracted_domain = extract_domain_from_referer(referer)
    
    # Try different domain variations for better matching
    domains_to_try = [domain, extracted_domain]
    if domain.startswith('www.'):
        domains_to_try.append(domain[4:])
    else:
        domains_to_try.append(f'www.{domain}')
    
    # Try to find by domain exactly
    account = None
    for test_domain in domains_to_try:
        if not test_domain:
            continue
        account = Account.objects.filter(websites__domain=test_domain).first()
        if account:
            break
    
    # If not found, try to match by patterns
    if not account:
        accounts = Account.objects.all()
        for acc in accounts:
            websites = acc.websites.all()
            for website in websites:
                patterns = website.url_patterns.split('\n') if website.url_patterns else []
                for pattern in patterns:
                    pattern = pattern.strip()
                    if pattern and (
                        domain == pattern or 
                        (pattern.startswith('*.') and domain.endswith(pattern[1:]))
                    ):
                        account = acc
                        break
                if account:
                    break
                
    return account
