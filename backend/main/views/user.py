from rest_framework import viewsets, status
from rest_framework.permissions import IsAdminUser
from django.contrib.auth import get_user_model
from main.serializers import UserListSerializer, UserUpdateSerializer
from rest_framework.response import Response

User = get_user_model()

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    permission_classes = [IsAdminUser]

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserListSerializer

    def destroy(self, request, *args, **kwargs):
        user = self.get_object()
        # Prevent self-deletion
        if user == request.user:
            return Response(
                {'detail': 'Cannot delete your own account'},
                status=status.HTTP_400_BAD_REQUEST
            )
        return super().destroy(request, *args, **kwargs) 