from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q
from ..models import Account
from ..utils.normalization import (
    normalize_business_name, 
    normalize_address, 
    is_similar_business_name, 
    is_similar_address
)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_duplicate_business(request):
    """
    Check if a business with the given name and address already exists
    This endpoint uses enhanced fuzzy matching for better duplicate detection
    """
    business_name = request.query_params.get('business_name', '')
    address_line1 = request.query_params.get('address_line1', '')
    business_id = request.query_params.get('business_id', None)  # Optional - to exclude current business when editing
    
    if not business_name or not address_line1:
        return Response(
            {'detail': 'Business name and address are required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Normalize the business name and address
    normalized_name = normalize_business_name(business_name)
    normalized_address = normalize_address(address_line1)
    
    # Initialize the duplicate flag
    is_duplicate = False
    duplicate_businesses = []
    
    # Check for exact matches on normalized fields
    exact_match_query = Q(
        Q(normalized_name__iexact=normalized_name) if hasattr(Account, 'normalized_name') else 
        Q(account_name__iexact=business_name)
    )
    
    # Add address matching criteria
    address_query = (
        Q(normalized_address__iexact=normalized_address) if hasattr(Account, 'normalized_address') else
        Q(address_line1__iexact=address_line1)
    )
    
    # If editing an existing business, exclude it from the check
    if business_id and business_id.isdigit():
        exact_match_query = exact_match_query & ~Q(id=int(business_id))
    
    # Check for exact matches
    exact_matches = Account.objects.filter(exact_match_query & address_query)
    if exact_matches.exists():
        is_duplicate = True
        duplicate_businesses = list(exact_matches.values('id', 'account_name', 'address_line1', 'city', 'state'))
    else:
        # If no exact matches, check for similar businesses using fuzzy matching
        # This is a more intensive operation, so we only do it if there are no exact matches
        all_businesses = Account.objects.all()
        if business_id and business_id.isdigit():
            all_businesses = all_businesses.exclude(id=int(business_id))
        
        for business in all_businesses:
            if (is_similar_business_name(business_name, business.account_name) and 
                is_similar_address(address_line1, business.address_line1)):
                is_duplicate = True
                duplicate_businesses.append({
                    'id': business.id,
                    'account_name': business.account_name,
                    'address_line1': business.address_line1,
                    'city': business.city,
                    'state': business.state
                })
                # Limit the number of similar businesses returned
                if len(duplicate_businesses) >= 5:
                    break
    
    return Response({
        'duplicate': is_duplicate,
        'duplicate_businesses': duplicate_businesses
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def delete_test_businesses(request):
    """
    Delete test businesses created during testing.
    For testing purposes only - should be disabled in production.
    """
    # Only allow this in development/testing environments
    from django.conf import settings
    if not settings.DEBUG:
        return Response(
            {"detail": "This endpoint is only available in testing/development environments"},
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Find businesses that match our test pattern
    test_businesses = Account.objects.filter(
        Q(account_name__startswith='Acme Technology') | 
        Q(account_name__startswith='Beta Technology') |
        Q(account_name__startswith='Zenith Consulting')
    )
    
    count = test_businesses.count()
    test_businesses.delete()
    
    return Response({
        "detail": f"Successfully deleted {count} test businesses", 
        "count": count
    }) 