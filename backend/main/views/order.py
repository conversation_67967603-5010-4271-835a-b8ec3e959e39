from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from main.models import Account, Order, Conversation, Message
from main.serializers.order import OrderSerializer
from main.serializers.conversation import ConversationSerializer
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from urllib.parse import urlparse
from main.views.utils import get_account_from_referer
import json
import re
import uuid
import logging
from decimal import Decimal
from django.utils import timezone
from rest_framework.decorators import api_view
import uuid
from django.utils import timezone
from datetime import datetime
import logging
import json
import requests
from main.utils.sms_service import SMSService

logger = logging.getLogger(__name__)

class OrderView(APIView):
    def post(self, request):
        try:
            # Extract basic data
            # Try camelCase first, then snake_case as fallback
            phone_number = request.data.get('phoneNumber') or request.data.get('phone_number')
            messages = request.data.get('messages', [])
            # Support both formats - camelCase and snake_case
            first_name = request.data.get('firstName') or request.data.get('first_name', '')
            last_name = request.data.get('lastName') or request.data.get('last_name', '')
            # Ensure we have non-None values for name fields
            first_name = first_name if first_name else ''
            last_name = last_name if last_name else ''
            delivery_method = request.data.get('deliveryMethod') or request.data.get('delivery_method', 'pickup')
            payment_method = request.data.get('paymentMethod') or request.data.get('payment_method', 'pay_later')

            # Extract delivery information if delivery method is selected - support both formats
            delivery_address_line1 = request.data.get('deliveryAddressLine1') or request.data.get('delivery_address_line1')
            delivery_address_line2 = request.data.get('deliveryAddressLine2') or request.data.get('delivery_address_line2')
            delivery_city = request.data.get('deliveryCity') or request.data.get('delivery_city')
            delivery_state = request.data.get('deliveryState') or request.data.get('delivery_state')
            delivery_zip_code = request.data.get('deliveryZipCode') or request.data.get('delivery_zip_code')
            delivery_charge = request.data.get('deliveryCharge') or request.data.get('delivery_charge', 0.00)
            tip_amount = request.data.get('tipAmount') or request.data.get('tip_amount', 0.00)

            # Extract payment information - support both formats
            payment_intent_id = request.data.get('paymentIntentId') or request.data.get('payment_intent_id')
            payment_status = request.data.get('paymentStatus') or request.data.get('payment_status', 'pending')

            # Get account from referer
            account = get_account_from_referer(request.headers.get('Referer', ''))
            if not account:
                return Response(
                    {'error': 'Invalid account'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Log incoming messages for debugging
            logger.debug(f"Incoming messages: {messages}")

            # Get the conversation_id from the request (support both formats)
            conversation_id = request.data.get('conversationId') or request.data.get('conversation_id')

            # Require a valid conversation_id
            if not conversation_id:
                logger.error("No conversation_id provided in the request")
                return Response(
                    {'error': 'A valid conversation_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            logger.info(f"Using conversation_id: {conversation_id}")

            # Get or create the conversation
            try:
                conversation = Conversation.objects.get(id=conversation_id)
                logger.info(f"Found existing conversation: {conversation}")
            except Conversation.DoesNotExist:
                # Create new conversation with the ID from the request
                conversation = Conversation.objects.create(
                    id=conversation_id,
                    account=account
                )
                logger.info(f"Created new conversation with ID: {conversation_id}")

            # Create message records with proper role detection
            for idx, msg in enumerate(messages):
                # Debug message content
                logger.debug(f"Processing message {idx}: {msg}")

                # Determine role based on content and position
                if 'sender' in msg:
                    # If sender is specified, use it
                    role = 'assistant' if msg['sender'] == 'bot' else 'user'
                elif 'role' in msg:
                    # If role is explicitly specified, use it
                    role = msg['role']
                else:
                    # The first message (idx=0) is typically from the assistant (greeting)
                    # So even indices (0, 2, 4...) should be assistant, odd (1, 3, 5...) should be user
                    role = 'assistant' if idx % 2 == 0 else 'user'

                logger.debug(f"Assigned role: {role}")

                Message.objects.create(
                    conversation=conversation,
                    role=role,
                    content=msg.get('text', msg.get('message', ''))
                )

            # Generate order summary and extract items using LLM
            order_summary, extracted_order_items = self.generate_order_summary(messages)

            # Create order with UUID
            order = Order.objects.create(
                id=str(uuid.uuid4()),
                account=account,
                phone_number=phone_number,
                conversation=conversation,
                first_name=first_name,
                last_name=last_name,
                order_summary=order_summary,
                delivery_method=delivery_method,
                payment_method=payment_method,
                payment_status=payment_status,
                payment_intent_id=payment_intent_id,
                delivery_address_line1=delivery_address_line1,
                delivery_address_line2=delivery_address_line2,
                delivery_city=delivery_city,
                delivery_state=delivery_state,
                delivery_zip_code=delivery_zip_code,
                delivery_charge=delivery_charge,
                tip_amount=tip_amount
            )

            # Log the raw serialized data for debugging
            conversation_data = ConversationSerializer(conversation, context={'for_email': True}).data
            logger.debug(f"Conversation data for email: {conversation_data}")

            # Extract order details from request if available and ensure proper types
            try:
                # Try to get order_items from request
                order_items = request.data.get('order_items', [])
                # Log the raw order_items data for debugging
                logger.info(f"Raw order_items data from request: {order_items}")

                # If order_items are empty, try to use extracted items from the conversation
                if not order_items and extracted_order_items:
                    order_items = extracted_order_items
                    logger.info(f"Using extracted order items: {extracted_order_items}")

                # Make sure order_items is actually a list even if we received something else
                if not isinstance(order_items, list):
                    order_items = []  # Reset to empty list if it's not already a list

                # Parse numeric values with sensible defaults
                try:
                    subtotal = float(request.data.get('subtotal', 0.00))
                except (ValueError, TypeError):
                    subtotal = 0.00

                try:
                    tax = float(request.data.get('tax', 0.00))
                except (ValueError, TypeError):
                    tax = 0.00

                try:
                    total = float(request.data.get('total', 0.00))
                except (ValueError, TypeError):
                    total = 0.00

                # Ensure delivery_charge and tip_amount are numeric
                try:
                    delivery_charge = float(delivery_charge) if delivery_charge else 0.00
                except (ValueError, TypeError):
                    delivery_charge = 0.00

                try:
                    tip_amount = float(tip_amount) if tip_amount else 0.00
                except (ValueError, TypeError):
                    tip_amount = 0.00

                # If we don't have structured order items, try the AnythingLLM service first (same as GetOrderView)
                if not order_items:
                    logger.info("No order_items provided, attempting to get items from AnythingLLM")
                    
                    try:
                        # For AnythingLLM, we need to get or create a workspace_slug and conversation_id
                        # For simplicity, we'll use the account name as workspace_slug like in GetOrderView
                        workspace_slug = account.account_name.lower().replace(' ', '-')
                        
                        # Migrate or remove: AnythingLLM order summarization pending migration
                        dishes_json = None
                        
                        # If we get a valid response from AnythingLLM
                        if dishes_json and 'Dishes' in dishes_json:
                            # Calculate totals
                            items = []
                            llm_subtotal = 0.0
                            
                            # Convert the dishes format to the expected order_items format
                            for dish in dishes_json['Dishes']:
                                item = {
                                    'name': dish.get('name', 'Unknown item'),
                                    'quantity': int(dish.get('order', 1)),
                                    'price': float(dish.get('total', 0.0)) / max(1, int(dish.get('order', 1))),
                                    'total': float(dish.get('total', 0.0))
                                }
                                items.append(item)
                                llm_subtotal += float(dish.get('total', 0.0))
                            
                            # Use the items from AnythingLLM
                            if items:
                                logger.info(f"Successfully got {len(items)} items from AnythingLLM")
                                order_items = items
                                
                                # Update subtotal if it's not already set
                                if subtotal == 0:
                                    subtotal = llm_subtotal
                                    # Recalculate tax and total
                                    tax = round(subtotal * 0.06, 2)  # 6% tax rate as used in GetOrderView
                                    total = subtotal + tax + delivery_charge + tip_amount
                    except Exception as e:
                        logger.error(f"Error getting items from AnythingLLM: {str(e)}")
                    
                    # If we still don't have order items, fall back to regex extraction
                    if not order_items:
                        logger.info("Falling back to regex extraction from order summary")
                        # Try to extract order items from the LLM-generated order summary
                        extracted_items = []
                    if order_summary:
                        # Look for patterns like quantities, item names, and prices in the order summary
                        # For example: "1x Pepperoni Pizza ($12.99)"
                        import re

                        # Try to extract items in several formats
                        # Match patterns like: "2 large pizzas - $24.99" or "Chicken wings (8pc) - $10.99"
                        item_patterns = [
                            r'(\d+)\s*x\s*([\w\s\-]+)\s*\(?\$?(\d+\.?\d*)\)?',  # "2 x Pizza $12.99"
                            r'([\w\s\-]+)\s*\(?(\d+)\)?\s*(?:pc|pieces|count)?\s*[-:]\s*\$?(\d+\.?\d*)',  # "Pizza (2) - $12.99"
                            r'([\w\s\-]+)\s*[-:]\s*\$?(\d+\.?\d*)',  # "Pizza - $12.99"
                        ]

                        # Split order summary into lines to process each line
                        lines = order_summary.split('\n')

                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # Try each pattern
                            item_found = False
                            for pattern in item_patterns:
                                matches = re.findall(pattern, line)
                                if matches:
                                    for match in matches:
                                        if len(match) == 3:  # Quantity, name, price
                                            try:
                                                qty = int(match[0])
                                                name = match[1].strip()
                                                price = float(match[2])
                                                extracted_items.append({
                                                    'name': name,
                                                    'quantity': qty,
                                                    'price': price,
                                                    'total': qty * price
                                                })
                                                item_found = True
                                            except (ValueError, IndexError):
                                                pass
                                        elif len(match) == 2:  # Name, price
                                            try:
                                                name = match[0].strip()
                                                price = float(match[1])
                                                extracted_items.append({
                                                    'name': name,
                                                    'quantity': 1,
                                                    'price': price,
                                                    'total': price
                                                })
                                                item_found = True
                                            except (ValueError, IndexError):
                                                pass

                            # If we couldn't match a pattern but the line has a dollar sign,
                            # it might be an item with price
                            if not item_found and '$' in line:
                                # Try to extract just the item name and price
                                parts = line.split('$')
                                if len(parts) == 2:
                                    name = parts[0].strip()
                                    # Remove any non-numeric characters except decimal point
                                    price_str = ''.join(c for c in parts[1] if c.isdigit() or c == '.')
                                    try:
                                        price = float(price_str)
                                        extracted_items.append({
                                            'name': name,
                                            'quantity': 1,
                                            'price': price,
                                            'total': price
                                        })
                                    except (ValueError, IndexError):
                                        pass

                    # If regex extraction found items, use them
                    if extracted_items:
                        logger.info(f"Successfully extracted {len(extracted_items)} items from order summary")
                        order_items = extracted_items
                        
                # If we still don't have any items after trying both methods, add a generic placeholder
                if not order_items:
                    logger.info("No items could be extracted, creating a generic item")
                    # Create a generic order item
                    item_price = max(0, total - delivery_charge - tip_amount)
                    order_items = [{
                        'name': 'Order items (see summary below)', 
                        'quantity': 1,
                        'price': item_price,
                        'total': item_price
                    }]
                else:
                    # Ensure each order item has the required fields
                    for i, item in enumerate(order_items):
                        if not isinstance(item, dict):
                            order_items[i] = {
                                'name': str(item),
                                'quantity': 1,
                                'price': 0.00,
                                'total': 0.00
                            }
                            continue

                        # Ensure required fields exist
                        item['name'] = item.get('name', f"Item {i+1}")

                        try:
                            item['quantity'] = int(item.get('quantity', 1))
                        except (ValueError, TypeError):
                            item['quantity'] = 1

                        try:
                            item['price'] = float(item.get('price', 0.00))
                        except (ValueError, TypeError):
                            item['price'] = 0.00

                        try:
                            item['total'] = float(item.get('total', item['price'] * item['quantity']))
                        except (ValueError, TypeError):
                            item['total'] = item['price'] * item['quantity']

                # Calculate total if not provided
                if not total:
                    # Calculate from items and add delivery + tip
                    item_total = sum(item.get('total', 0) for item in order_items)
                    total = item_total + tax + delivery_charge + tip_amount

                # Calculate subtotal if it's zero but we have items
                if subtotal == 0 and order_items:
                    subtotal = sum(item.get('total', 0) for item in order_items)

                # Log the processed order_items for debugging
                logger.info(f"Processed order_items: {order_items}")
                logger.info(f"Order totals: subtotal={subtotal}, tax={tax}, delivery={delivery_charge}, tip={tip_amount}, total={total}")

            except Exception as e:
                logger.error(f"Error processing order items: {str(e)}", exc_info=True)
                # Fallback to safe defaults
                order_items = [{
                    'name': 'Order details unavailable',
                    'quantity': 1,
                    'price': 0.00,
                    'total': 0.00
                }]
                subtotal = 0.00
                tax = 0.00
                total = 0.00

            # Log whether we have order items
            if not order_items or len(order_items) == 0:
                logger.info("No order items detected")

            # Log the final order items being used for the email
            logger.info(f"Final order items for email: {order_items}")
            logger.info(f"Final order totals: subtotal={subtotal}, tax={tax}, total={total}")

            # Prepare email context
            context = {
                'account_name': account.account_name,
                'phone_number': phone_number,
                'first_name': first_name,
                'last_name': last_name,
                'order_summary': order_summary,
                'order_items': order_items,
                'subtotal': subtotal,
                'tax': tax,
                'total': total,
                'delivery_method': order.get_delivery_method_display(),
                'payment_method': order.get_payment_method_display(),
                'delivery_address': {
                    'line1': delivery_address_line1,
                    'line2': delivery_address_line2,
                    'city': delivery_city,
                    'state': delivery_state,
                    'zip_code': delivery_zip_code
                } if delivery_method == 'delivery' else None,
                'delivery_charge': delivery_charge,
                'tip_amount': tip_amount,
                'conversation': conversation_data,
                'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Convert order items to a list if it's not already (ensuring it's the correct format)
            if not isinstance(order_items, list):
                order_items = list(order_items)
                context['order_items'] = order_items

            # Add detailed debug logging
            # Define a custom serializer that handles Decimal and other non-JSON types
            def json_serializer(obj):
                if isinstance(obj, Decimal):
                    return float(obj)
                return str(obj)

            # Validate order items structure - ensure each item has the required fields
            for item in order_items:
                if not isinstance(item, dict):
                    logger.error(f"Invalid order item format: {item}")
                    continue

                # Ensure all required fields exist with proper types
                if 'name' not in item or not item['name']:
                    item['name'] = 'Unnamed Item'
                if 'quantity' not in item or not isinstance(item['quantity'], (int, float, Decimal)):
                    item['quantity'] = 1
                if 'price' not in item or not isinstance(item['price'], (int, float, Decimal)):
                    item['price'] = 0.0
                if 'total' not in item or not isinstance(item['total'], (int, float, Decimal)):
                    item['total'] = float(item['price']) * float(item['quantity'])

                # Convert to proper types if needed
                item['quantity'] = int(item['quantity'])
                item['price'] = float(item['price'])
                item['total'] = float(item['total'])

            # Update context with validated items
            context['order_items'] = order_items

            logger.info(f"Order items before render: {json.dumps(order_items, default=json_serializer)}")

            # Send email notification
            try:
                html_content = render_to_string('emails/new_order.html', context)
                text_content = render_to_string('emails/new_order.txt', context)

                # Log that templates were rendered successfully
                logger.info("Successfully rendered email templates")
            except Exception as e:
                logger.error(f"Error rendering email templates: {str(e)}")
                raise

            # Get recipient email
            recipient_email = (account.config.order_taking_email
                             if hasattr(account, 'config') and account.config.order_taking_email
                             else account.email)

            # Create a customer name representation for the subject
            customer_name = f"{first_name} {last_name}".strip()
            customer_name = customer_name if customer_name else "Customer"

            # Log HTML content for debugging
            logger.debug(f"HTML Email Content: {html_content[:1000]}...") # Log first 1000 chars

            try:
                send_mail(
                    subject=f'New Order - {customer_name} ({phone_number})',
                    message=text_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[recipient_email],
                    html_message=html_content
                )
                logger.info(f"Email sent successfully to {recipient_email}")
            except Exception as e:
                logger.error(f"Error sending email: {str(e)}")
                # Continue - don't fail the API if just the email fails

            # Prepare order data for SMS
            order_data = OrderSerializer(order).data

            # Send SMS notification to customer if phone number is provided
            sms_sent = False
            if phone_number:
                sms_sent = SMSService.send_order_confirmation(phone_number, order_data)
                logger.info(f"SMS notification {'sent' if sms_sent else 'failed'} for order {order.id}")

            return Response({
                'status': 'success',
                'detail': 'Order submitted successfully!',
                'order': order_data,
                'notifications': {
                    'email': True,
                    'sms': sms_sent
                }
            })

        except Exception as e:
            logger.error(f"Error submitting order: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=500)

    def generate_order_summary(self, messages):
        """Generate a summary of the order using a dummy LLM implementation"""
        try:
            # Extract all user messages and attempt to find order items
            user_messages = []
            extracted_items = []

            for msg in messages:
                role = None
                if 'sender' in msg:
                    role = 'user' if msg['sender'] == 'user' else 'assistant'
                elif 'role' in msg:
                    role = msg['role']

                if role == 'user':
                    content = msg.get('text', msg.get('message', ''))
                    user_messages.append(content)

                    # Look for items and prices in the message
                    if '$' in content or 'price' in content.lower():
                        # Try to extract item-price pairs
                        import re
                        # Simple patterns to match items with prices
                        patterns = [
                            r'(\d+)\s*x\s*([\w\s\-,\.\(\)]+)\s*[\$]\s*(\d+\.?\d*)',  # 2 x Pizza $10.99
                            r'([\w\s\-,\.\(\)]+)\s*[\-:]\s*[\$]\s*(\d+\.?\d*)'  # Pizza - $10.99
                        ]

                        for pattern in patterns:
                            matches = re.findall(pattern, content)
                            for match in matches:
                                try:
                                    if len(match) == 3:  # qty, name, price
                                        extracted_items.append({
                                            'name': match[1].strip(),
                                            'quantity': int(match[0]),
                                            'price': float(match[2]),
                                            'total': int(match[0]) * float(match[2])
                                        })
                                    elif len(match) == 2:  # name, price
                                        extracted_items.append({
                                            'name': match[0].strip(),
                                            'quantity': 1,
                                            'price': float(match[1]),
                                            'total': float(match[1])
                                        })
                                except (ValueError, IndexError):
                                    pass

            # In a real implementation, you would call an LLM API here
            # For now, we'll use a dummy implementation
            combined_messages = "\n".join(user_messages)

            # Try to call an actual LLM if configured
            try:
                # This is a placeholder for an actual LLM API call
                # You would replace this with your actual LLM integration
                prompt = f"""Please summarize the following conversation into a concise order summary:
                {combined_messages}

                Provide a clear, organized summary of the items ordered, any special requests, and any other relevant details.
                """

                # Dummy LLM API call - replace with actual implementation
                # response = requests.post(
                #     "https://api.openai.com/v1/chat/completions",
                #     headers={
                #         "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
                #         "Content-Type": "application/json"
                #     },
                #     json={
                #         "model": "gpt-3.5-turbo",
                #         "messages": [{"role": "system", "content": prompt}],
                #         "max_tokens": 500
                #     }
                # )
                #
                # if response.status_code == 200:
                #     result = response.json()
                #     return result["choices"][0]["message"]["content"], extracted_items

                # Fallback to dummy implementation if LLM call fails
                summary = f"Order Summary: {combined_messages[:200]}..."
                
                # If we have no extracted items, add a dummy one for testing
                if not extracted_items:
                    extracted_items = [{
                        'name': 'Sample item from conversation', 
                        'quantity': 1,
                        'price': 15.99,
                        'total': 15.99
                    }]
                    
                return summary, extracted_items

            except Exception as e:
                logger.error(f"Error calling LLM API: {str(e)}")
                # Fallback to a simple summary if LLM call fails
                summary = f"Order Summary: {combined_messages[:200]}..."
                return summary, extracted_items

        except Exception as e:
            logger.error(f"Error generating order summary: {str(e)}")
            return "Order summary not available", []

class GetOrderView(APIView):
    """View to get an order for a conversation"""
    def get(self, request):
        try:
            # Get conversation_id from request parameters
            conversation_id = request.query_params.get('conversation_id')
            if not conversation_id:
                return Response({'error': 'Missing conversation_id parameter'}, status=400)

            # Get account from referer
            account = get_account_from_referer(request.headers.get('Referer', ''))
            if not account:
                # Try to get account from session if available
                if hasattr(request, 'session') and 'account_id' in request.session:
                    try:
                        account = Account.objects.get(id=request.session['account_id'])
                    except Account.DoesNotExist:
                        pass

            # If still no account, return error
            if not account:
                return Response({'error': 'Authentication required'}, status=401)

            # Get the conversation for the provided ID
            try:
                conversation = Conversation.objects.get(id=conversation_id)
            except Conversation.DoesNotExist:
                return Response({'error': 'Conversation not found'}, status=404)

            # For AnythingLLM, we need to get or create a workspace_slug and conversation_id
            # For simplicity, we'll use the account name as workspace_slug
            # Migrate or remove: AnythingLLM order summarization pending migration
            dishes_json = None

            # If we get a valid response from AnythingLLM
            if dishes_json and 'dishes' in dishes_json:
                # Calculate totals
                items = []
                subtotal = 0.0

                # Convert the dishes format to the expected frontend format
                for dish in dishes_json['dishes']:
                    quantity = dish.get('order', 1)
                    price = dish.get('price', 0.0)
                    mytotal = quantity * price
                    item = {
                        'name': dish.get('name', 'Unknown item'),
                        'quantity': quantity,
                        'price': price,
                        'subtotal': mytotal
                    }
                    items.append(item)
                    subtotal += mytotal

                # Calculate tax (6% of subtotal)
                tax = round(subtotal * 0.0725, 2)
                
                # Get any existing order for this conversation
                try:
                    existing_order = Order.objects.get(conversation_id=conversation_id)
                    delivery_charge = existing_order.delivery_charge or 0.0
                    tip_amount = existing_order.tip_amount or 0.0
                    status = existing_order.status
                    order_id = existing_order.id
                except Order.DoesNotExist:
                    delivery_charge = 0.0
                    tip_amount = 0.0
                    status = 'pending'
                    order_id = str(uuid.uuid4())

                # Calculate total
                total = subtotal + tax + delivery_charge + tip_amount

                # Create a summary
                order_summary = ', '.join([f"{item['quantity']} {item['name']}" for item in items])

                # Create the order response in the expected structure
                order_response = {
                    'id': order_id,
                    'conversation_id': conversation_id,
                    'status': status,
                    'order_summary': order_summary,
                    'items': items,
                    'subtotal': round(subtotal, 2),
                    'tax': tax,
                    'delivery_charge': delivery_charge,
                    'tip_amount': tip_amount,
                    'total': round(total, 2),
                    'created_at': datetime.now().isoformat()
                }

                return Response(order_response)
            else:
                # If AnythingLLM didn't return valid data, create a fallback response
                logger.warning(f"AnythingLLM did not return valid order data: {dishes_json}")

                # Create a fallback order
                fallback_order = {
                    'id': str(uuid.uuid4()),
                    'conversation_id': conversation_id,
                    'status': 'pending',
                    'order_summary': 'Unable to process order items',
                    'items': [],
                    'subtotal': 0.0,
                    'tax': 0.0,
                    'delivery_charge': 0.0,
                    'tip_amount': 0.0,
                    'total': 0.0,
                    'created_at': datetime.now().isoformat()
                }

                return Response(fallback_order)

        except Exception as e:
            logger.error(f"Error getting order: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=500)

@api_view(['POST'])
def submit_order(request):
    try:
        phone_number = request.data.get('phoneNumber')
        messages = request.data.get('messages', [])

        # Validate messages format and ensure role is set
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                'role': msg.get('role', 'user'),  # Default to 'user' if role not specified
                'text': msg.get('text', msg.get('message', ''))  # Handle both text and message keys
            })

        # Get account from referer
        account = get_account_from_referer(request.headers.get('Referer', ''))
        if not account:
            return Response({'error': 'Invalid account'}, status=400)

        # Create conversation record
        conversation = Conversation.objects.create(
            account=account,
            id=str(uuid.uuid4())
        )

        # Create message records with proper roles
        for msg in formatted_messages:
            Message.objects.create(
                conversation=conversation,
                role=msg['role'],
                text=msg['text']
            )

        # Create order with conversation
        order = Order.objects.create(
            account=account,
            phone_number=phone_number,
            conversation=conversation
        )

        # Send email notification
        context = {
            'account_name': account.account_name,
            'phone_number': phone_number,
            'conversation': ConversationSerializer(conversation, context={'for_email': True}).data,
            'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        html_content = render_to_string('emails/new_order.html', context)
        text_content = render_to_string('emails/new_order.txt', context)

        send_mail(
            subject=f'New Order from {account.account_name}',
            message=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[account.email],
            html_message=html_content
        )

        # Prepare order data for SMS
        order_data = OrderSerializer(order).data

        # Send SMS notification to customer if phone number is provided
        sms_sent = False
        if phone_number:
            sms_sent = SMSService.send_order_confirmation(phone_number, order_data)
            logger.info(f"SMS notification {'sent' if sms_sent else 'failed'} for order {order.id}")

        return Response({
            'status': 'success',
            'order_id': str(order.id),
            'notifications': {
                'email': True,
                'sms': sms_sent
            }
        })

    except Exception as e:
        logger.error(f"Error submitting order: {str(e)}")
        return Response({'error': str(e)}, status=500)
