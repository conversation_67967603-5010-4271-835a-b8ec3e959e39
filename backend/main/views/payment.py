import os
import logging
import stripe
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import api_view
from django.conf import settings
from main.models import Account
from main.views.utils import get_account_from_referer

logger = logging.getLogger(__name__)

# Initialize Stripe with the API key from environment variable
stripe.api_key = os.environ.get('STRIPE_SECRET_KEY')

class CreatePaymentIntentView(APIView):
    """View to create a payment intent for Stripe"""
    def post(self, request):
        try:
            # Check if this is a chat path request (which should bypass authentication)
            is_chat_path = 'chat/payment' in request.path
            
            # Get account from referer
            account = get_account_from_referer(request.headers.get('Referer', ''))
            if not account:
                # Try to get account from session if available
                if hasattr(request, 'session') and 'account_id' in request.session:
                    try:
                        account = Account.objects.get(id=request.session['account_id'])
                    except Account.DoesNotExist:
                        pass
            
            # If no account and not a chat path request, return error
            if not account and not is_chat_path:
                return Response({'error': 'Authentication required'}, status=401)
                
            # For chat path requests without account, use a default or first account
            if not account and is_chat_path:
                # Get the first account or create a default one for processing
                account = Account.objects.first()
            
            # Get amount from request (in dollars)
            amount = request.data.get('amount')
            if not amount:
                return Response({'error': 'Amount is required'}, status=400)
            
            # Convert amount to cents for Stripe
            amount_cents = int(float(amount) * 100)
            
            # Create a PaymentIntent with the order amount and currency
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                payment_method_types=['card'],
                metadata={
                    'account_id': str(account.id),
                    'account_name': account.account_name
                }
            )
            
            # Return the client secret to the frontend
            return Response({
                'clientSecret': payment_intent.client_secret,
                'paymentIntentId': payment_intent.id
            })
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error: {str(e)}")
            return Response({'error': str(e)}, status=400)
        except Exception as e:
            logger.error(f"Error creating payment intent: {str(e)}")
            return Response({'error': str(e)}, status=500)

class ProcessPaymentMethodView(APIView):
    """View to process a payment method for Stripe"""
    def post(self, request):
        try:
            # Check if this is a chat path request (which should bypass authentication)
            is_chat_path = 'chat/payment' in request.path
            
            # Get account from referer
            account = get_account_from_referer(request.headers.get('Referer', ''))
            if not account:
                # Try to get account from session if available
                if hasattr(request, 'session') and 'account_id' in request.session:
                    try:
                        account = Account.objects.get(id=request.session['account_id'])
                    except Account.DoesNotExist:
                        pass
            
            # If no account and not a chat path request, return error
            if not account and not is_chat_path:
                return Response({'error': 'Authentication required'}, status=401)
                
            # For chat path requests without account, use a default or first account
            if not account and is_chat_path:
                # Get the first account or create a default one for processing
                account = Account.objects.first()
            
            # Get payment method ID from request
            payment_method_id = request.data.get('paymentMethodId')
            if not payment_method_id:
                return Response({'error': 'Payment method ID is required'}, status=400)
            
            # Get amount from request (in dollars)
            amount = request.data.get('amount')
            if not amount:
                return Response({'error': 'Amount is required'}, status=400)
            
            # Convert amount to cents for Stripe
            amount_cents = int(float(amount) * 100)
            
            # Create a PaymentIntent
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                payment_method=payment_method_id,
                confirm=True,  # Confirm the payment immediately
                return_url=request.data.get('returnUrl', ''),  # URL to redirect after payment
                metadata={
                    'account_id': str(account.id),
                    'account_name': account.account_name
                }
            )
            
            # Return the payment intent status
            return Response({
                'paymentIntentId': payment_intent.id,
                'status': payment_intent.status,
                'clientSecret': payment_intent.client_secret
            })
            
        except stripe.error.CardError as e:
            # Since it's a decline, stripe.error.CardError will be caught
            logger.error(f"Card error: {str(e)}")
            return Response({
                'error': e.user_message,
                'code': e.code
            }, status=400)
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error: {str(e)}")
            return Response({'error': str(e)}, status=400)
        except Exception as e:
            logger.error(f"Error processing payment: {str(e)}")
            return Response({'error': str(e)}, status=500)
