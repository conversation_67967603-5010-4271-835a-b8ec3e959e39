import os
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from main.models import Account, AccountUser, AccountConfig
from main.serializers.account import AccountSerializer
from main.serializers.account_config import AccountConfigSerializer
from django.http import JsonResponse
from rest_framework.decorators import action
# Migrate or remove: from ..services.LLMServices.AnythingLLMService import AnythingLLMService
from rest_framework.decorators import api_view, permission_classes
from django.db.models import Q
from django.utils.text import slugify
import string
import random

class AccountViewSet(viewsets.ModelViewSet):
    queryset = Account.objects.all()
    serializer_class = AccountSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Return filtered accounts based on query parameters.

        Special cases:
        - If account_name and address parameters are provided, filter for possible duplicates
        - For admin/staff users, return all accounts
        - For regular users, return only their accounts
        """
        user = self.request.user
        queryset = Account.objects.select_related('config').all()

        # Handle filtering for duplicate check
        account_name = self.request.query_params.get('account_name')
        address = self.request.query_params.get('address')

        if account_name and address:
            # This is a duplicate check query - build the filter
            query = Q(account_name__iexact=account_name)

            # If address is provided as a normalized string, parse it to components
            if ',' in address:
                try:
                    parts = address.split(',')
                    if len(parts) >= 3:
                        # Extract address components: street, city, state zip
                        street = parts[0].strip()
                        city = parts[1].strip()
                        state_zip = parts[2].strip().split()
                        state = state_zip[0] if state_zip else ''

                        # Add address components to query
                        query = query & (
                            Q(address_line1__iexact=street) &
                            Q(city__iexact=city) &
                            Q(state__iexact=state)
                        )
                except Exception as e:
                    # If parsing fails, fall back to simple filtering
                    pass

            queryset = queryset.filter(query)
            return queryset

        # Handle standard user-based filtering
        if user.is_staff or user.is_superuser:
            return queryset

        return queryset.filter(users=user)

    def perform_create(self, serializer):
        """When creating an account, automatically create config"""
        account = serializer.save()
        AccountUser.objects.create(
            account=account,
            user=self.request.user,
            role='admin'
        )
        # Create default config
        AccountConfig.objects.get_or_create(account=account)

        # Create widget directory for account
        try:
            widget_dir = os.path.join('frontend', 'widget', str(account.uuid))
            os.makedirs(widget_dir, exist_ok=True)
        except Exception as e:
            # Log error but don't fail account creation
            print(f"Error creating widget directory: {str(e)}")

    def create(self, request, *args, **kwargs):
        """Override create to handle the response"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        # Re-fetch the account to include the users data in response
        account = Account.objects.get(id=serializer.data['id'])
        response_serializer = self.get_serializer(account)

        # Migrate or remove: AnythingLLM workspace bootstrap pending migration

        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )

    @action(detail=True, methods=['post'])
    def create_workspace(self, request, pk=None):
        """
        Create AnythingLLM workspace for the account
        """
        account = self.get_object()

        return Response(
            {
                'error': 'Workspace creation via AnythingLLM pending migration',
            },
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )

    @action(detail=True, methods=['post'])
    def upload_chat_icon(self, request, pk=None):
        """
        Upload a chat icon for the account
        """
        account = self.get_object()

        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        file = request.FILES['file']

        # Validate file type
        allowed_types = ['image/svg+xml', 'image/jpeg', 'image/png']
        if file.content_type not in allowed_types:
            return Response(
                {'error': 'Invalid file type. Only SVG, JPG, and PNG are allowed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate file size (max 2MB)
        if file.size > 2 * 1024 * 1024:
            return Response(
                {'error': 'File size too large. Maximum 2MB allowed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Save the file
        account.chat_icon = file
        account.save()

        return Response({
            'message': 'Chat icon uploaded successfully',
            'icon_url': f'/media/{account.chat_icon.name}',
            'chat_icon': f'/media/{account.chat_icon.name}'
        })

    @action(detail=True, methods=['post'])
    def remove_chat_icon(self, request, pk=None):
        """
        Remove the chat icon for the account
        """
        account = self.get_object()

        if not account.chat_icon:
            return Response(
                {'error': 'No chat icon to remove'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Delete the file from storage
        account.chat_icon.delete(save=False)
        account.chat_icon = None
        account.save()

        return Response({
            'message': 'Chat icon removed successfully'
        })

    @action(detail=False, methods=['get'], url_path='by-uuid/(?P<uuid>[^/.]+)')
    def by_uuid(self, request, uuid=None):
        """Get account by UUID"""
        try:
            account = Account.objects.get(uuid=uuid)
            serializer = self.get_serializer(account)
            return Response(serializer.data)
        except Account.DoesNotExist:
            return Response(
                {'error': 'Account not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'], url_path='by-uuid/(?P<uuid>[^/.]+)/config')
    def get_config(self, request, uuid=None):
        """Get account configuration for widget"""
        try:
            account = Account.objects.get(uuid=uuid)
            config = account.config if hasattr(account, 'config') else None
            return Response({
                'enabled': True,
                'account_uuid': str(account.uuid),
                'llm_slug': account.llm_slug,
                'initial_message': config.greeting_message if config else "Hello! How can I help you today?",
                'chat_title': config.chat_title if config else "AiDA",
                'theme': account.config.theme,
                'chat_icon': account.chat_icon.url if account.chat_icon else '/images/chat.png',
                'order_taking_enabled': config.order_taking_enabled if config else False,
            })
        except Account.DoesNotExist:
            return Response(
                {'error': 'Account not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['patch'])
    def update_account_config(self, request, pk=None):
        try:
            account = self.get_object()

            # Check if user has permission to update this account
            if not request.user.is_superuser and not account.users.filter(id=request.user.id).exists():
                return Response({"detail": "You do not have permission to update this account."},
                            status=status.HTTP_403_FORBIDDEN)

            # Get existing config or create new one
            config, created = AccountConfig.objects.get_or_create(account=account)

            # Update fields that are present in the request
            if 'greeting_message' in request.data:
                config.greeting_message = request.data['greeting_message']

            if 'chat_title' in request.data:
                config.chat_title = request.data['chat_title']

            if 'primary_color' in request.data:
                config.primary_color = request.data['primary_color']

            if 'secondary_color' in request.data:
                config.secondary_color = request.data['secondary_color']

            # Add new order taking fields
            if 'order_taking_enabled' in request.data:
                config.order_taking_enabled = request.data['order_taking_enabled']

            if 'order_taking_email' in request.data:
                config.order_taking_email = request.data['order_taking_email']

            if 'theme' in request.data:
                config.theme = request.data['theme']

            config.save()

            return Response({
                'success': True,
                'config': {
                    'greeting_message': config.greeting_message,
                    'chat_title': config.chat_title,
                    'order_taking_enabled': config.order_taking_enabled,
                    'order_taking_email': config.order_taking_email,
                    'theme': config.theme,
                }
            })

        except Account.DoesNotExist:
            return Response({"detail": "Account not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='check-duplicate')
    def check_duplicate(self, request):
        """
        Check if a business with the same name and address already exists.

        Query parameters:
        - business_name: The business name to check
        - address_line1: The address line 1 to check

        Returns:
        - duplicate: Boolean indicating whether a duplicate exists
        - account_id: The ID of the duplicate account (if duplicate=True)
        """
        business_name = request.query_params.get('business_name', '').strip().lower()
        address_line1 = request.query_params.get('address_line1', '').strip().lower()

        if not business_name or not address_line1:
            return Response({
                'duplicate': False,
                'message': 'Business name and address are required for duplicate check'
            })

        # Exclude the current user's account if they're updating their own
        exclude_id = None
        if not request.user.is_staff and not request.user.is_superuser:
            try:
                user_account = Account.objects.filter(users=request.user).first()
                if user_account:
                    exclude_id = user_account.id
            except Exception:
                pass

        # Query for accounts with matching name and address
        query = Q(account_name__iexact=business_name) & Q(address_line1__iexact=address_line1)

        # Exclude current account if applicable
        if exclude_id:
            queryset = Account.objects.filter(query).exclude(id=exclude_id)
        else:
            queryset = Account.objects.filter(query)

        duplicate_exists = queryset.exists()
        duplicate_id = queryset.first().id if duplicate_exists else None

        return Response({
            'duplicate': duplicate_exists,
            'account_id': duplicate_id
        })
