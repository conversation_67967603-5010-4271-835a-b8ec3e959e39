from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from ..models.password_reset import PasswordResetToken
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError

User = get_user_model()

@api_view(['POST'])
@permission_classes([AllowAny])
def request_password_reset(request):
    """
    Request a password reset. Generates a token and sends an email.
    """
    email = request.data.get('email')
    if not email:
        return Response(
            {'error': 'Email is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        # Return success even if user doesn't exist to prevent email enumeration
        return Response({'message': 'If an account exists with this email, you will receive password reset instructions.'})

    # Create reset token
    token = PasswordResetToken.objects.create(user=user)

    # Build reset URL
    reset_url = f"{settings.FRONTEND_URL}/reset-password/{token.token}"

    # Send email
    try:
        context = {
            'user': user,
            'reset_url': reset_url,
            'valid_hours': 24
        }
        html_message = render_to_string('password_reset_email.html', context)
        text_message = render_to_string('password_reset_email.txt', context)

        send_mail(
            subject='Password Reset Request',
            message=text_message,
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
    except Exception as e:
        # Log the error but don't expose it to the user
        print(f"Error sending password reset email: {str(e)}")
        return Response(
            {'error': 'Failed to send reset email'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    return Response({'message': 'If an account exists with this email, you will receive password reset instructions.'})

@api_view(['POST'])
@permission_classes([AllowAny])
def verify_reset_token(request):
    """
    Verify if a reset token is valid.
    """
    token = request.data.get('token')
    if not token:
        return Response(
            {'error': 'Token is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        reset_token = PasswordResetToken.objects.get(token=token)
        if not reset_token.is_valid():
            return Response(
                {'error': 'Token is invalid or expired'},
                status=status.HTTP_400_BAD_REQUEST
            )
    except PasswordResetToken.DoesNotExist:
        return Response(
            {'error': 'Token is invalid or expired'},
            status=status.HTTP_400_BAD_REQUEST
        )

    return Response({'message': 'Token is valid'})

@api_view(['POST'])
@permission_classes([AllowAny])
def reset_password(request):
    """
    Reset password using a valid token.
    """
    token = request.data.get('token')
    new_password = request.data.get('password')

    if not token or not new_password:
        return Response(
            {'error': 'Token and new password are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        reset_token = PasswordResetToken.objects.get(token=token)
        if not reset_token.is_valid():
            return Response(
                {'error': 'Token is invalid or expired'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate new password
        try:
            validate_password(new_password, reset_token.user)
        except ValidationError as e:
            return Response(
                {'error': list(e.messages)},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set new password
        user = reset_token.user
        user.set_password(new_password)
        user.save()

        # Mark token as used
        reset_token.used = True
        reset_token.save()

        return Response({'message': 'Password has been reset successfully'})

    except PasswordResetToken.DoesNotExist:
        return Response(
            {'error': 'Token is invalid or expired'},
            status=status.HTTP_400_BAD_REQUEST
        ) 