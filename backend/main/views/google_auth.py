from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from django.contrib.auth.models import User
from rest_framework.decorators import api_view
from rest_framework.response import Response
from google.oauth2 import id_token
from google.auth.transport import requests
from django.conf import settings
from .auth import get_tokens_for_user

User = get_user_model()

@api_view(['POST'])
def google_login(request):
    try:
        token = request.data.get('token')
        email = request.data.get('email')
        name = request.data.get('name')
        picture = request.data.get('picture')

        # Create or get the user
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # Create a new user
            username = email.split('@')[0]  # Use email prefix as username
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=name.split()[0] if name else '',
                last_name=' '.join(name.split()[1:]) if name and len(name.split()) > 1 else ''
            )

        # Generate JWT token
        return Response(get_tokens_for_user(user))

    except Exception as e:
        return Response({'error': str(e)}, status=400) 