from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.exceptions import PermissionDenied, ValidationError
from django.core.files.uploadedfile import UploadedFile
from ..models.account import Account
from ..models.document import Document
from ..models.account_user import AccountUser
from ..serializers.document_serializer import DocumentSerializer
from main.services.tasks import process_document
from worker.app import app
from celery.exceptions import OperationalError
# Migrate or remove: from main.services.LLMServices.AnythingLLMService import AnythingLLMService
from main.services.aida_llm_client import AidaLLMClient, AidaLLMClientError
import logging
import os

logger = logging.getLogger('main')

class DocumentViewSet(viewsets.ModelViewSet):
    serializer_class = DocumentSerializer
    parser_classes = (MultiPartParser, FormParser)

    def get_queryset(self):
        account_id = self.request.query_params.get('account')
        if account_id:
            # Check if user has access to this account
            try:
                if not (self.request.user.is_staff or self.request.user.is_superuser):
                    account_user = AccountUser.objects.get(
                        user=self.request.user,
                        account_id=account_id
                    )
                return Document.objects.filter(account_id=account_id)
            except AccountUser.DoesNotExist:
                return Document.objects.none()

        # If no account specified, return documents from all accounts user has access to
        account_ids = AccountUser.objects.filter(
            user=self.request.user
        ).values_list('account_id', flat=True)
        return Document.objects.filter(account_id__in=account_ids)

    def perform_create(self, serializer):
        account_id = self.request.data.get('account')
        if not account_id:
            raise ValidationError({"account": ["Account ID is required"]})

        # Admin and staff can upload to any account
        if not (self.request.user.is_staff or self.request.user.is_superuser):
            # Verify user has access to this account
            try:
                account_user = AccountUser.objects.get(
                    user=self.request.user,
                    account_id=account_id
                )
            except AccountUser.DoesNotExist:
                raise PermissionDenied("You don't have access to this account")

        file_obj = self.request.FILES.get('file')
        if not file_obj:
            raise ValidationError({"file": ["No file provided"]})

        # Validate file type
        file_extension = file_obj.name.split('.')[-1].lower()
        allowed_types = ['pdf', 'txt', 'md', 'doc', 'docx', 'html']
        if file_extension not in allowed_types:
            raise ValidationError({
                "file": [f"Unsupported file type: {file_extension}. Supported types: {', '.join(allowed_types)}"]
            })

        # Save document
        document = serializer.save(
            account_id=account_id,
            file_type=file_extension,
            original_filename=file_obj.name,
            file=file_obj
        )

        # Process with LLM service
        try:
            account = Account.objects.get(id=account_id)
        except Account.DoesNotExist as exc:
            document.llm_processing_error = "Account not found"
            document.save(update_fields=['llm_processing_error'])
            raise ValidationError({"account": ["Account not found"]}) from exc

        if not account.llm_slug:
            document.llm_processing_error = "Account missing aida-llm slug"
            document.save(update_fields=['llm_processing_error'])
            logger.warning("Account %s missing llm_slug; skipping aida-llm upload", account_id)
            return

        try:
            process_document.delay(document.id)
        except OperationalError as exc:
            logger.error("Celery unavailable for document %s: %s", document.id, exc)
            document.llm_processing_error = "Processing temporarily unavailable"
            document.save(update_fields=['llm_processing_error'])
        except Exception as exc:
            document.llm_processing_error = str(exc)
            document.save(update_fields=['llm_processing_error'])

    @action(detail=True, methods=['post'])
    def retry_llm_processing(self, request, pk=None):
        logger.info(f"Starting retry_llm_processing for document: {pk}")

        try:
            # Get account_id from query params
            account_id = request.query_params.get('account')
            if not account_id:
                return Response(
                    {'error': 'Account ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get document directly with account filter
            try:
                document = Document.objects.get(id=pk, account_id=account_id)
            except Document.DoesNotExist:
                logger.error(f"Document {pk} not found for account {account_id}")
                return Response(
                    {'error': 'Document not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            logger.info(f"Retrieved document: {document.id}")

            # Admin and staff can retry processing for any document
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                # Verify user has access to this document's account
                try:
                    account_user = AccountUser.objects.get(
                        user=self.request.user,
                        account_id=account_id
                    )
                except AccountUser.DoesNotExist:
                    logger.warning(f"Access denied for user {request.user.id} to document {document.id}")
                    raise PermissionDenied("You don't have access to this document")

            try:
                logger.info("Attempting to queue document for processing")
                task = process_document.delay(document.id)
                logger.info(f"Task queued successfully with id: {task.id if task else 'unknown'}")
                return Response({'status': 'Processing started'})

            except OperationalError as e:
                logger.error(f"Celery Operational Error: {str(e)}", exc_info=True)
                return Response(
                    {'error': 'Task queueing system temporarily unavailable. Please try again later.'},
                    status=status.HTTP_503_SERVICE_UNAVAILABLE
                )
            except Exception as e:
                logger.error(f"Unexpected error queueing task: {str(e)}", exc_info=True)
                return Response(
                    {'error': 'An unexpected error occurred. Please try again later.'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except PermissionDenied as e:
            logger.error(f"Permission denied: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Top-level error in retry_llm_processing: {str(e)}", exc_info=True)
            return Response(
                {'error': 'An unexpected error occurred. Please try again later.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def replace_document(self, request, pk=None):
        logger.info(f"Starting replace_document for document: {pk}")
        try:
            # Get account_id from query params
            account_id = request.query_params.get('account')
            if not account_id:
                return Response(
                    {'error': 'Account ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get document directly with account filter
            try:
                document = Document.objects.get(id=pk, account_id=account_id)
            except Document.DoesNotExist:
                logger.error(f"Document {pk} not found for account {account_id}")
                return Response(
                    {'error': 'Document not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            logger.info(f"Retrieved document: {document.id}")

            # Admin and staff can replace any document
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                try:
                    account_user = AccountUser.objects.get(
                        user=self.request.user,
                        account_id=account_id
                    )
                except AccountUser.DoesNotExist:
                    logger.warning(f"Access denied for user {request.user.id} to document {document.id}")
                    raise PermissionDenied("You don't have access to this document")

            if 'file' not in request.FILES:
                return Response(
                    {'error': 'No file provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            new_file = request.FILES['file']

            # Save old file path to delete later
            old_file_path = document.file.path if document.file else None

            # Get file extension for file_type
            file_extension = new_file.name.split('.')[-1].lower()

            # Update document with new file
            document.file = new_file
            document.original_filename = new_file.name
            document.file_type = file_extension  # Use file extension instead of content_type
            document.file_size = new_file.size
            document.llm_processed = False  # Reset processing status
            document.llm_processing_error = ''  # Clear any previous errors
            document.save()

            # Delete old file
            if old_file_path and os.path.exists(old_file_path):
                os.remove(old_file_path)

            # Queue document for processing
            try:
                process_document.delay(document.id)
            except OperationalError as exc:
                logger.error(f"Celery Operational Error: {str(exc)}", exc_info=True)
                return Response(
                    {'error': 'Task queueing system temporarily unavailable. Please try again later.'},
                    status=status.HTTP_503_SERVICE_UNAVAILABLE
                )
            except Exception as exc:
                logger.error(f"Unexpected error queueing task: {str(exc)}", exc_info=True)
                return Response(
                    {'error': 'An unexpected error occurred while queueing the document.'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return Response({
                'status': 'Document replaced successfully',
                'document': DocumentSerializer(document).data
            })

        except PermissionDenied as e:
            logger.error(f"Permission denied: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Error replacing document: {str(e)}", exc_info=True)
            return Response(
                {'error': 'An error occurred while replacing the document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, *args, **kwargs):
        logger.info(f"Starting document deletion for document: {kwargs.get('pk')}")
        try:
            # Get account_id from query params
            account_id = request.query_params.get('account')
            if not account_id:
                return Response(
                    {'error': 'Account ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get document directly with account filter
            try:
                document = Document.objects.get(id=kwargs.get('pk'), account_id=account_id)
            except Document.DoesNotExist:
                logger.error(f"Document {kwargs.get('pk')} not found for account {account_id}")
                return Response(
                    {'error': 'Document not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            logger.info(f"Retrieved document: {document.id}")

            # Admin and staff can delete any document
            if not (request.user.is_staff or request.user.is_superuser):
                try:
                    account_user = AccountUser.objects.get(
                        user=request.user,
                        account_id=account_id
                    )
                    logger.info(f"User {request.user.id} has permission to delete document {document.id}")
                except AccountUser.DoesNotExist:
                    logger.warning(f"Access denied for user {request.user.id} to document {document.id}")
                    raise PermissionDenied("You don't have access to this document")

            # Delete from aida-llm if llm_id exists
            if document.llm_id and document.account and document.account.llm_slug:
                try:
                    client = AidaLLMClient()
                    try:
                        remote_id = int(document.llm_id)
                    except (TypeError, ValueError):
                        logger.warning(
                            "Document %s has non-numeric llm_id '%s'; skipping remote delete",
                            document.id,
                            document.llm_id,
                        )
                    else:
                        client.delete_document(
                            organization_slug=document.account.llm_slug,
                            document_id=remote_id,
                        )
                except (AidaLLMClientError, ValueError) as exc:
                    logger.error(f"Error deleting document from aida-llm: {str(exc)}")
                    # Continue with deletion even if remote deletion fails

            # Save file path to delete after document is deleted
            file_path = document.file.path if document.file else None
            logger.info(f"File path to delete: {file_path}")

            # Delete document from database
            document.delete()

            # Delete the actual file
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"Successfully deleted file: {file_path}")
                except Exception as e:
                    logger.error(f"Error deleting file {file_path}: {str(e)}")

            return Response(status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error(f"Error in document deletion: {str(e)}")
            raise
