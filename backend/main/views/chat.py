from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime
import logging
# Migrate or remove: from main.services.LLMServices.AnythingLLMService import AnythingLLMService
from main.services.chat_config_service import ChatConfigService
from rest_framework.permissions import AllowAny
from urllib.parse import urlparse
from main.views.utils import get_account_from_referer
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from rest_framework.decorators import api_view
from main.models.conversation import Conversation, Message
from main.serializers.conversation import ConversationSerializer, MessageSerializer
import uuid

logger = logging.getLogger(__name__)

class ChatConfigView(APIView):
    def get(self, request):
        referer = request.headers.get('Referer')
        if not referer:
            return Response({'error': 'Referer header is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Use utility function to find account
        account = get_account_from_referer(referer)

        if not account:
            return Response({'error': 'No account found for this domain'}, status=status.HTTP_404_NOT_FOUND)

        # Get domain from referer for config
        domain = urlparse(referer).netloc

        # Get configuration
        config = ChatConfigService.get_config_for_domain(domain)

        if not config:
            return Response(
                {'error': f'No configuration found for domain: {domain}'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Make sure theme is always included in the response
        if hasattr(account, 'config'):
            config['theme'] = account.config.theme
        else:
            config['theme'] = 'light'  # Provide default if no config exists

        return Response(config, status=status.HTTP_200_OK)

class ChatThreadView(APIView):
    permission_classes = [AllowAny]  # Allow unauthenticated access

    def post(self, request):
        try:
            # Debug request info
            print("Request method:", request.method)
            print("Request path:", request.path)
            print("Request data:", request.data)
            print("Request headers:", {k: v for k, v in request.headers.items()})

            # Create thread with timestamped name
            thread_name = f"Chat-{datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}"
            print(f"Created thread name: {thread_name}")

            # Extract domain from referer
            referer = request.headers.get('Referer', '')
            print(f"Referer header: {referer}")

            if not referer:
                print("No referer header found")
                return Response(
                    {'error': 'Referer header is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            from .utils import extract_domain_from_referer
            domain = extract_domain_from_referer(referer)
            print(f"Extracted domain: {domain}")

            if not domain:
                print("Could not extract domain from referer")
                return Response(
                    {'error': 'Could not extract domain from referer'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get configuration
            print(f"Getting config for domain: {domain}")
            config = ChatConfigService.get_config_for_domain(domain)
            print(f"Got config: {config}")

            if not config:
                print(f"No config found for domain: {domain}")
                return Response(
                    {'error': 'No configuration found for domain'},
                    status=status.HTTP_404_NOT_FOUND
                )

            if not config.get('llm_slug'):
                print("No llm_slug in config")
                return Response(
                    {'error': 'Invalid LLM configuration'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Migrate or remove: AnythingLLM thread creation pending migration
            return Response(
                {'error': 'Chat thread creation pending migration to aida-llm'},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )

        except Exception as e:
            logger.error(f"Error creating chat thread: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ChatMessageView(APIView):
    def post(self, request):
        try:
            thread_id = request.data.get('thread_id')
            message = request.data.get('message')
            llm_slug = request.data.get('llm_slug')

            if not all([thread_id, message, llm_slug]):
                return Response(
                    {'error': 'Missing required parameters'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            from .utils import extract_domain_from_referer
            domain = extract_domain_from_referer(request.headers.get('Referer', ''))
            if not domain:
                return Response(
                    {'error': 'Could not extract domain from referer'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get account from referer
            account = get_account_from_referer(request.headers.get('Referer', ''))
            if not account:
                return Response(
                    {'error': 'Invalid account'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            config = ChatConfigService.get_config_for_domain(domain)
            if not config:
                return Response(
                    {'error': 'No configuration found for domain'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get or create the conversation using the thread_id from AnythingLLM
            try:
                logger.info(f"Looking for existing conversation with ID: {thread_id}")
                conversation = Conversation.objects.get(id=thread_id)
                logger.info(f"Found existing conversation: {conversation}")
            except Conversation.DoesNotExist:
                # Create new conversation with the ID from the request
                # This ensures we use the same ID generated by AnythingLLM consistently
                logger.info(f"Creating new conversation with ID: {thread_id}")
                conversation = Conversation.objects.create(
                    id=thread_id,
                    account=account
                )
                logger.info(f"Created new conversation with ID: {thread_id}")

            # Create user message
            Message.objects.create(
                conversation=conversation,
                role='user',
                content=message
            )

            # Migrate or remove: AnythingLLM chat submission pending migration
            return Response(
                {'error': 'Chat submission pending migration to aida-llm'},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )

        except Exception as e:
            logger.error(f"Error submitting chat message: {str(e)}", exc_info=True)
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@api_view(['POST'])
def email_chat_history(request):
    """Send chat history to user's email"""
    try:
        email = request.data.get('email')
        messages = request.data.get('messages', [])

        if not email or not messages:
            return Response(
                {'detail': 'Email and messages are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get account from referer
        account = get_account_from_referer(request.headers.get('Referer', ''))
        if not account:
            return Response(
                {'detail': 'Invalid account'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare context directly with the provided messages
        # No need to create a new conversation record
        context = {
            'account_name': account.account_name,
            'messages': messages,  # Use messages directly from the request
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Render and send email
        html_content = render_to_string('emails/chat_history.html', context)
        text_content = render_to_string('emails/chat_history.txt', context)

        # Format date for email subject
        formatted_date = datetime.now().strftime('%m/%d/%Y')

        send_mail(
            subject=f'Chat History - {formatted_date} - {account.account_name}',
            message=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[email],
            html_message=html_content,
            fail_silently=False,
        )

        return Response({
            'detail': 'Chat history sent successfully'
        })

    except Exception as e:
        logger.error(f"Error sending chat history email: {str(e)}", exc_info=True)
        return Response(
            {'detail': 'Failed to send email'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
