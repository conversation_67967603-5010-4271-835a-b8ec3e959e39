from django.contrib.auth import authenticate, get_user_model
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework import status
from rest_framework.permissions import AllowAny
from main.serializers import UserSerializer
from django.contrib.auth.models import User, Group, update_last_login
from ..models import Account
from ..serializers import AccountSerializer
from django.db.models import Q
from ..utils.normalization import (
    normalize_business_name, 
    normalize_address, 
    is_similar_business_name, 
    is_similar_address
)

User = get_user_model()

def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    refresh['name'] = user.get_full_name()
    refresh['email'] = user.email
    refresh['is_staff'] = user.is_staff
    refresh['is_superuser'] = user.is_superuser
    refresh['groups'] = list(user.groups.values_list('name', flat=True))
    return {
        'token': str(refresh.access_token),
    }

@api_view(['POST'])
def logout(request):
    return Response({'message': 'Logged out successfully'})

@api_view(['POST'])
@permission_classes([AllowAny])
def register_user(request):
    data = request.data.copy()
    if 'password2' not in data:
        data['password2'] = data.get('password', '')

    serializer = UserSerializer(data=data)
    if serializer.is_valid():
        user = serializer.save()
        
        # Add user to Business Regular Users group
        business_user_group, _ = Group.objects.get_or_create(name='Business Regular Users')
        user.groups.add(business_user_group)
        
        return Response({
            'message': 'Registration successful',
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name
            }
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def login_user(request):
    email = request.data.get('username')  # Frontend sends email as username
    password = request.data.get('password')

    if not email or not password:
        return Response({'error': 'Please provide both email and password'},
                      status=status.HTTP_400_BAD_REQUEST)

    user = authenticate(username=email, password=password)

    if not user:
        return Response({'error': 'Invalid credentials'},
                      status=status.HTTP_401_UNAUTHORIZED)

    token_data = get_tokens_for_user(user)
    return Response({
        'token': token_data['token'],
        'user': {
            'id': user.id,
            'email': user.email,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name
        }
    })

@api_view(['POST'])
@permission_classes([AllowAny])
def register_business(request):
    """
    Handle optional business information registration
    Enhanced with better duplicate detection using fuzzy matching
    """
    try:
        # Get user data
        user_data = request.data
        
        # Extract business data
        business_data = {
            'account_name': user_data.get('business_name'),
            'industry_name': user_data.get('industry'),
            'primary_phone': user_data.get('company_phone'),
            'primary_contact': f"{user_data.get('first_name')} {user_data.get('last_name')}",
            'address_line1': user_data.get('address_line1'),
            'address_line2': user_data.get('address_line2'),
            'city': user_data.get('city'),
            'state': user_data.get('state'),
            'zip_code': user_data.get('zip_code'),
            'status': 'active'
        }

        # Enhanced duplicate business detection using normalization
        normalized_name = normalize_business_name(business_data['account_name'])
        normalized_address = normalize_address(business_data['address_line1'])
        
        # Store normalized values for future use
        business_data['normalized_name'] = normalized_name
        business_data['normalized_address'] = normalized_address
        
        # Initialize duplicate check result
        is_duplicate = False
        duplicate_business = None
        
        # First check for exact matches on normalized fields
        exact_match_query = Q(
            Q(normalized_name__iexact=normalized_name) if hasattr(Account, 'normalized_name') else 
            Q(account_name__iexact=business_data['account_name'])
        )
        
        # Add address matching criteria for exact matches
        address_query = (
            Q(normalized_address__iexact=normalized_address) if hasattr(Account, 'normalized_address') else
            Q(address_line1__iexact=business_data['address_line1'])
        ) & Q(city__iexact=business_data['city']) & Q(state__iexact=business_data['state'])
        
        # Check for exact matches
        exact_match = Account.objects.filter(exact_match_query & address_query).first()
        
        if exact_match:
            is_duplicate = True
            duplicate_business = exact_match
        else:
            # If no exact match, check for similar businesses using fuzzy matching
            all_businesses = Account.objects.all()
            
            for business in all_businesses:
                if (is_similar_business_name(business_data['account_name'], business.account_name) and 
                    is_similar_address(business_data['address_line1'], business.address_line1) and
                    business_data['city'].lower() == business.city.lower() and
                    business_data['state'].lower() == business.state.lower()):
                    is_duplicate = True
                    duplicate_business = business
                    break

        if is_duplicate:
            return Response(
                {
                    'detail': 'A business with this name and address already exists',
                    'duplicate_business': {
                        'id': duplicate_business.id,
                        'name': duplicate_business.account_name,
                        'address': duplicate_business.address_line1,
                        'city': duplicate_business.city,
                        'state': duplicate_business.state
                    }
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Remove normalized fields if they don't exist in the model
        if not hasattr(Account, 'normalized_name'):
            business_data.pop('normalized_name', None)
        if not hasattr(Account, 'normalized_address'):
            business_data.pop('normalized_address', None)
            
        # Create the business account
        account = Account.objects.create(**business_data)
        
        # Get the user based on email
        try:
            user = User.objects.get(email=user_data.get('email'))
            
            # Add user to Business Admins group if not already a member
            business_admin_group, _ = Group.objects.get_or_create(name='Business Admins')
            if not user.groups.filter(name='Business Admins').exists():
                user.groups.add(business_admin_group)
            
            # Associate user with business account
            account.users.add(user)
            
            return Response({
                'message': 'Business information added successfully',
                'account_id': account.id
            }, status=status.HTTP_201_CREATED)
            
        except User.DoesNotExist:
            # If user doesn't exist (which shouldn't happen), delete the business account
            account.delete()
            return Response(
                {'detail': 'User not found. Please register first.'},
                status=status.HTTP_404_NOT_FOUND
            )

    except Exception as e:
        return Response(
            {'detail': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )