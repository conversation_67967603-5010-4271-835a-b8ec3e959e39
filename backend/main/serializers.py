from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Account
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from main.serializers.account import (
    AccountSerializer,
    AccountUserSerializer,
    AccountWebsiteSerializer,
    AccountConfigSerializer,
)

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True)
    password2 = serializers.CharField(write_only=True, required=True)
    email = serializers.EmailField(required=True)

    class Meta:
        model = User
        fields = ('email', 'password', 'password2', 'first_name', 'last_name')
        extra_kwargs = {
            'first_name': {'required': True},
            'last_name': {'required': True}
        }

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_password(self, value):
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value

    def validate(self, data):
        if data['password'] != data['password2']:
            raise serializers.ValidationError({
                "password": "Password fields didn't match."
            })
        return data

    def create(self, validated_data):
        validated_data.pop('password2')
        email = validated_data['email']
        user = User.objects.create_user(
            username=email,
            email=email,
            password=validated_data['password'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name']
        )
        return user

class AccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = Account
        fields = '__all__'

__all__ = [
    "AccountSerializer",
    "AccountUserSerializer",
    "AccountWebsiteSerializer",
    "AccountConfigSerializer",
] 