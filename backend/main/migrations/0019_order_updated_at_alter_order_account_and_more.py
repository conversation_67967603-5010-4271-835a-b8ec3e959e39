# Generated by Django 5.1 on 2025-02-26 18:53

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0018_conversation_account_alter_conversation_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.account'),
        ),
        migrations.AlterField(
            model_name='order',
            name='conversation',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='order', to='main.conversation'),
        ),
        migrations.AlterField(
            model_name='order',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='order',
            name='id',
            field=models.CharField(max_length=100, primary_key=True, serialize=False),
        ),
    ]
