from django.db import migrations

def fix_group_mappings(apps, schema_editor):
    AccountUser = apps.get_model('main', 'AccountUser')
    Group = apps.get_model('auth', 'Group')
    User = apps.get_model('auth', 'User')
    
    # Get groups
    business_admin_group = Group.objects.get(name='Business Admins')
    business_user_group = Group.objects.get(name='Business Regular Users')
    
    # Track changes
    fixes = {
        'admin_added': 0,
        'user_added': 0,
        'already_correct': 0,
        'errors': 0
    }
    
    # Process all AccountUser entries
    for account_user in AccountUser.objects.select_related('user').all():
        try:
            user = account_user.user
            current_groups = list(user.groups.values_list('name', flat=True))
            
            if account_user.role == 'admin':
                if 'Business Admins' not in current_groups:
                    user.groups.add(business_admin_group)
                    fixes['admin_added'] += 1
                else:
                    fixes['already_correct'] += 1
                    
            elif account_user.role == 'user':
                if 'Business Regular Users' not in current_groups:
                    user.groups.add(business_user_group)
                    fixes['user_added'] += 1
                else:
                    fixes['already_correct'] += 1
                    
        except User.DoesNotExist:
            fixes['errors'] += 1
            print(f"Error: User not found for AccountUser {account_user.id}")
        except Exception as e:
            fixes['errors'] += 1
            print(f"Error processing AccountUser {account_user.id}: {str(e)}")
    
    print("\nGroup mapping fixes complete:")
    print(f"- Added {fixes['admin_added']} users to Business Admins group")
    print(f"- Added {fixes['user_added']} users to Business Regular Users group")
    print(f"- {fixes['already_correct']} users already had correct groups")
    print(f"- {fixes['errors']} errors encountered")

def reverse_fix(apps, schema_editor):
    # No reverse operation needed since this is a fixing migration
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('main', '0003_migrate_user_roles'),
    ]

    operations = [
        migrations.RunPython(fix_group_mappings, reverse_fix),
    ] 