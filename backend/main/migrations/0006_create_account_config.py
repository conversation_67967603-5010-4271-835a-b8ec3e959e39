# Generated by Django 5.1 on 2025-01-23 14:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0005_add_chat_icon_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountConfig',
            fields=[
                ('account', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='config', serialize=False, to='main.account')),
                ('widget_version', models.CharField(default='1.0', help_text='Version of the widget to serve for this account', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Account Configuration',
                'verbose_name_plural': 'Account Configurations',
                'db_table': 'main_account_config',
            },
        ),
    ]
