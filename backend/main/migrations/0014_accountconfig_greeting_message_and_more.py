# Generated by Django 5.1 on 2025-02-19 15:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0013_account_llm_folder'),
    ]

    operations = [
        migrations.AddField(
            model_name='accountconfig',
            name='greeting_message',
            field=models.CharField(default='Hello! How can I help you today?', help_text='Initial greeting message shown in chat', max_length=200),
        ),
        migrations.AddField(
            model_name='accountconfig',
            name='primary_color',
            field=models.Char<PERSON>ield(default='#007bff', max_length=7),
        ),
        migrations.AddField(
            model_name='accountconfig',
            name='secondary_color',
            field=models.Char<PERSON><PERSON>(default='#e3f2fd', max_length=7),
        ),
    ]
