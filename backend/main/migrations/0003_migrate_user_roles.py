from django.db import migrations

def migrate_roles(apps, schema_editor):
    AccountUser = apps.get_model('main', 'AccountUser')
    Group = apps.get_model('auth', 'Group')
    User = apps.get_model('auth', 'User')
    
    # Get or create groups
    business_admin_group = Group.objects.get(name='Business Admins')
    business_user_group = Group.objects.get(name='Business Regular Users')
    
    # Track changes for logging
    role_changes = {
        'admin': 0,
        'user': 0,
        'skipped': 0
    }
    
    # Map AccountUser roles to groups
    for account_user in AccountUser.objects.all():
        user = User.objects.get(id=account_user.user_id)
        
        if account_user.role == 'admin':
            if not user.groups.filter(id=business_admin_group.id).exists():
                user.groups.add(business_admin_group)
                role_changes['admin'] += 1
        
        elif account_user.role == 'user':
            if not user.groups.filter(id=business_user_group.id).exists():
                user.groups.add(business_user_group)
                role_changes['user'] += 1
        
        else:
            role_changes['skipped'] += 1
    
    print(f"Migration complete:")
    print(f"- Added {role_changes['admin']} users to Business Admins group")
    print(f"- Added {role_changes['user']} users to Business Regular Users group")
    print(f"- Skipped {role_changes['skipped']} users with other roles")

def reverse_roles(apps, schema_editor):
    Group = apps.get_model('auth', 'Group')
    User = apps.get_model('auth', 'User')
    
    # Get groups
    business_admin_group = Group.objects.get(name='Business Admins')
    business_user_group = Group.objects.get(name='Business Regular Users')
    
    # Remove all users from business groups
    users_removed = 0
    for user in User.objects.filter(groups__in=[business_admin_group, business_user_group]):
        user.groups.remove(business_admin_group)
        user.groups.remove(business_user_group)
        users_removed += 1
    
    print(f"Removed {users_removed} users from business groups")

class Migration(migrations.Migration):
    dependencies = [
        ('main', '0002_create_user_groups'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.RunPython(migrate_roles, reverse_roles),
    ] 