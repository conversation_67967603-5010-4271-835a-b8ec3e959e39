from django.db import migrations

def create_groups(apps, schema_editor):
    Group = apps.get_model('auth', 'Group')
    
    # Create Business Admin group
    business_admin, created = Group.objects.get_or_create(name='Business Admins')
    if created:
        print('Created Business Admins group')
    
    # Create Business Regular Users group
    business_user, created = Group.objects.get_or_create(name='Business Regular Users')
    if created:
        print('Created Business Regular Users group')

def reverse_groups(apps, schema_editor):
    Group = apps.get_model('auth', 'Group')
    Group.objects.filter(name__in=['Business Admins', 'Business Regular Users']).delete()
    print('Removed business groups')

class Migration(migrations.Migration):
    dependencies = [
        ('main', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.RunPython(create_groups, reverse_groups),
    ] 