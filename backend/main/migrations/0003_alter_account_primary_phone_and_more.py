# Generated by Django 5.1 on 2024-12-31 01:54

import django.core.validators
import main.models.account_website
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0002_accountwebsite_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='account',
            name='primary_phone',
            field=models.CharField(help_text='Format: +****************', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number can only contain digits, spaces, parentheses, plus, and hyphens', regex='^[0-9()+\\- ]*$')]),
        ),
        migrations.AlterField(
            model_name='account',
            name='secondary_phone',
            field=models.CharField(blank=True, help_text='Format: +****************', max_length=20, null=True, validators=[django.core.validators.RegexValidator(message='Phone number can only contain digits, spaces, parentheses, plus, and hyphens', regex='^[0-9()+\\- ]*$')]),
        ),
        migrations.AlterField(
            model_name='accountwebsite',
            name='domain',
            field=models.CharField(help_text='Website domain (e.g., example.com)', max_length=255, validators=[main.models.account_website.validate_domain]),
        ),
    ]
