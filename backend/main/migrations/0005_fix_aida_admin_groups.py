from django.db import migrations

def fix_all_group_mappings(apps, schema_editor):
    AccountUser = apps.get_model('main', 'AccountUser')
    Group = apps.get_model('auth', 'Group')
    User = apps.get_model('auth', 'User')
    
    # Get or create groups
    business_admin_group = Group.objects.get(name='Business Admins')
    business_user_group = Group.objects.get(name='Business Regular Users')
    
    # AIDA admin emails
    aida_admin_emails = [
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    # Track changes
    fixes = {
        'aida_admin_added': 0,
        'business_admin_added': 0,
        'business_user_added': 0,
        'already_correct': 0,
        'errors': 0
    }
    
    # First handle AIDA admins
    for email in aida_admin_emails:
        try:
            user = User.objects.get(email=email)
            # Set as superuser if not already
            if not user.is_superuser:
                user.is_superuser = True
                user.is_staff = True
                user.save()
            fixes['aida_admin_added'] += 1
        except User.DoesNotExist:
            fixes['errors'] += 1
            print(f"Error: AIDA admin user not found: {email}")
    
    # Then handle AccountUser roles
    for account_user in AccountUser.objects.select_related('user').all():
        try:
            user = account_user.user
            # Skip if user is an AIDA admin
            if user.email in aida_admin_emails:
                continue
                
            current_groups = list(user.groups.values_list('name', flat=True))
            
            if account_user.role == 'admin':
                if 'Business Admins' not in current_groups:
                    user.groups.add(business_admin_group)
                    fixes['business_admin_added'] += 1
                else:
                    fixes['already_correct'] += 1
                    
            elif account_user.role == 'user':
                if 'Business Regular Users' not in current_groups:
                    user.groups.add(business_user_group)
                    fixes['business_user_added'] += 1
                else:
                    fixes['already_correct'] += 1
                    
        except User.DoesNotExist:
            fixes['errors'] += 1
            print(f"Error: User not found for AccountUser {account_user.id}")
        except Exception as e:
            fixes['errors'] += 1
            print(f"Error processing AccountUser {account_user.id}: {str(e)}")
    
    print("\nGroup mapping fixes complete:")
    print(f"- Set {fixes['aida_admin_added']} AIDA admin permissions")
    print(f"- Added {fixes['business_admin_added']} users to Business Admins group")
    print(f"- Added {fixes['business_user_added']} users to Business Regular Users group")
    print(f"- {fixes['already_correct']} users already had correct groups")
    print(f"- {fixes['errors']} errors encountered")

def reverse_fix(apps, schema_editor):
    # No reverse operation needed since this is a fixing migration
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('main', '0004_fix_user_group_mappings'),
    ]

    operations = [
        migrations.RunPython(fix_all_group_mappings, reverse_fix),
    ] 