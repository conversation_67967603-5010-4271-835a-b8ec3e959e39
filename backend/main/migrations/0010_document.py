# Generated by Django 5.1 on 2025-02-02 23:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0009_make_uuid_non_nullable'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='documents/%Y/%m/%d/')),
                ('file_type', models.CharField(choices=[('pdf', 'PDF'), ('doc', 'DOC'), ('txt', 'TXT'), ('xls', 'XLS')], max_length=3)),
                ('description', models.TextField(blank=True)),
                ('original_filename', models.Char<PERSON>ield(max_length=255)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('llm_processed', models.BooleanField(default=False)),
                ('llm_processing_error', models.TextField(blank=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='main.account')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
