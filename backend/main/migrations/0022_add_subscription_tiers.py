# Generated by Django 5.1 on 2025-03-02 16:03

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0021_merge_20250302_1603'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='subscription_renewal_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='subscription_start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='subscription_status',
            field=models.CharField(blank=True, choices=[('active', 'Active'), ('trial', 'Trial'), ('expired', 'Expired'), ('canceled', 'Canceled')], default='active', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='subscription_tier',
            field=models.Char<PERSON>ield(choices=[('basic', 'Basic'), ('pro', 'Pro'), ('premium', 'Premium')], default='basic', help_text='The subscription tier for this business account', max_length=20),
        ),
        migrations.AddIndex(
            model_name='account',
            index=models.Index(fields=['subscription_tier'], name='main_accoun_subscri_d13cde_idx'),
        ),
    ]
