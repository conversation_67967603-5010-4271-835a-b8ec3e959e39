# Generated by Django 5.1 on 2024-12-25 18:33

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountWebsite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(help_text='Website domain (e.g., example.com)', max_length=255, validators=[django.core.validators.URLValidator(schemes=['http', 'https'])])),
                ('url_patterns', models.TextField(blank=True, help_text='URL patterns where <PERSON><PERSON> should show. One pattern per line.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Account Website',
                'verbose_name_plural': 'Account Websites',
                'ordering': ['domain'],
            },
        ),
        migrations.RemoveIndex(
            model_name='account',
            name='main_accoun_domain_67f8d6_idx',
        ),
        migrations.RemoveField(
            model_name='account',
            name='domain',
        ),
        migrations.RemoveField(
            model_name='account',
            name='url_patterns',
        ),
        migrations.AddField(
            model_name='accountwebsite',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='websites', to='main.account'),
        ),
    ]
