from django.db import migrations
import json

def debug_and_fix_mappings(apps, schema_editor):
    AccountUser = apps.get_model('main', 'AccountUser')
    Group = apps.get_model('auth', 'Group')
    User = apps.get_model('auth', 'User')
    
    print("\nStarting debug migration...")
    
    # Debug existing state
    print("\nCurrent Users:")
    for user in User.objects.all():
        print(f"- {user.email}: superuser={user.is_superuser}, staff={user.is_staff}, groups={list(user.groups.values_list('name', flat=True))}")
    
    print("\nCurrent AccountUsers:")
    for au in AccountUser.objects.select_related('user').all():
        print(f"- AccountUser(id={au.id}, user={au.user.email}, role={au.role})")
    
    # Get or create groups
    business_admin_group = Group.objects.get(name='Business Admins')
    business_user_group = Group.objects.get(name='Business Regular Users')
    
    print("\nVerified Groups:")
    print(f"- Business Admins (id={business_admin_group.id})")
    print(f"- Business Regular Users (id={business_user_group.id})")
    
    # AIDA admin emails
    aida_admin_emails = [
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    # Track changes
    fixes = {
        'aida_admin_added': 0,
        'business_admin_added': 0,
        'business_user_added': 0,
        'already_correct': 0,
        'errors': 0,
        'debug_info': []
    }
    
    # First handle AIDA admins
    print("\nProcessing AIDA admins:")
    for email in aida_admin_emails:
        try:
            user = User.objects.get(email=email)
            print(f"Found AIDA admin: {email}")
            
            # Set as superuser if not already
            if not user.is_superuser:
                print(f"Setting superuser for {email}")
                user.is_superuser = True
                user.is_staff = True
                user.save()
                fixes['debug_info'].append(f"Set superuser for {email}")
            
            fixes['aida_admin_added'] += 1
            
        except User.DoesNotExist:
            error_msg = f"Error: AIDA admin user not found: {email}"
            print(error_msg)
            fixes['errors'] += 1
            fixes['debug_info'].append(error_msg)
    
    # Then handle AccountUser roles
    print("\nProcessing AccountUser roles:")
    for account_user in AccountUser.objects.select_related('user').all():
        try:
            user = account_user.user
            print(f"\nProcessing user: {user.email}")
            
            # Skip if user is an AIDA admin
            if user.email in aida_admin_emails:
                print(f"Skipping AIDA admin: {user.email}")
                fixes['debug_info'].append(f"Skipped AIDA admin: {user.email}")
                continue
                
            current_groups = list(user.groups.values_list('name', flat=True))
            print(f"Current groups for {user.email}: {current_groups}")
            
            if account_user.role == 'admin':
                if 'Business Admins' not in current_groups:
                    print(f"Adding {user.email} to Business Admins")
                    user.groups.add(business_admin_group)
                    fixes['business_admin_added'] += 1
                    fixes['debug_info'].append(f"Added {user.email} to Business Admins")
                else:
                    fixes['already_correct'] += 1
                    
            elif account_user.role == 'user':
                if 'Business Regular Users' not in current_groups:
                    print(f"Adding {user.email} to Business Regular Users")
                    user.groups.add(business_user_group)
                    fixes['business_user_added'] += 1
                    fixes['debug_info'].append(f"Added {user.email} to Business Regular Users")
                else:
                    fixes['already_correct'] += 1
                    
        except User.DoesNotExist:
            error_msg = f"Error: User not found for AccountUser {account_user.id}"
            print(error_msg)
            fixes['errors'] += 1
            fixes['debug_info'].append(error_msg)
        except Exception as e:
            error_msg = f"Error processing AccountUser {account_user.id}: {str(e)}"
            print(error_msg)
            fixes['errors'] += 1
            fixes['debug_info'].append(error_msg)
    
    print("\nVerifying final state:")
    for user in User.objects.all():
        print(f"- {user.email}: superuser={user.is_superuser}, staff={user.is_staff}, groups={list(user.groups.values_list('name', flat=True))}")
    
    print("\nGroup mapping fixes complete:")
    print(f"- Set {fixes['aida_admin_added']} AIDA admin permissions")
    print(f"- Added {fixes['business_admin_added']} users to Business Admins group")
    print(f"- Added {fixes['business_user_added']} users to Business Regular Users group")
    print(f"- {fixes['already_correct']} users already had correct groups")
    print(f"- {fixes['errors']} errors encountered")
    print("\nDebug Info:")
    for info in fixes['debug_info']:
        print(f"- {info}")

def reverse_fix(apps, schema_editor):
    # No reverse operation needed since this is a fixing migration
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('main', '0005_fix_aida_admin_groups'),
    ]

    operations = [
        migrations.RunPython(debug_and_fix_mappings, reverse_fix),
    ] 