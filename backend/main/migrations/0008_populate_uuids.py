import uuid
from django.db import migrations


def populate_uuids(apps, schema_editor):
    Account = apps.get_model('main', 'Account')
    for account in Account.objects.all():
        account.uuid = uuid.uuid4()
        account.save()


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0007_add_uuid_field'),
    ]

    operations = [
        migrations.RunPython(populate_uuids),
    ]
