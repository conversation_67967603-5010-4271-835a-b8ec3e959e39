# Generated by Django 5.1 on 2025-02-25 22:17

import main.models.account_config
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0016_accountconfig_order_taking_email'),
    ]

    operations = [
        migrations.AddField(
            model_name='accountconfig',
            name='order_taking_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='accountconfig',
            name='order_taking_email',
            field=models.EmailField(blank=True, help_text='Email address where order notifications will be sent', max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='accountconfig',
            name='primary_color',
            field=models.Char<PERSON><PERSON>(default='#007bff', max_length=7, validators=[main.models.account_config.validate_hex_color]),
        ),
        migrations.AlterField(
            model_name='accountconfig',
            name='secondary_color',
            field=models.Char<PERSON><PERSON>(default='#e3f2fd', max_length=7, validators=[main.models.account_config.validate_hex_color]),
        ),
    ]
