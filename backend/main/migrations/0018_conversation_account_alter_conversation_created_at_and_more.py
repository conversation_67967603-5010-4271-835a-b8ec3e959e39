# Generated by Django 5.1 on 2025-02-26 18:50

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0017_accountconfig_order_taking_enabled_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='conversation',
            name='account',
            field=models.ForeignKey(default=4, on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to='main.account'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='conversation',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='conversation',
            name='id',
            field=models.CharField(max_length=100, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='message',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='message',
            name='role',
            field=models.CharField(choices=[('user', 'User'), ('assistant', 'Assistant')], max_length=20),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['id'], name='main_conver_id_067f60_idx'),
        ),
        migrations.AddIndex(
            model_name='conversation',
            index=models.Index(fields=['account', 'created_at'], name='main_conver_account_53df67_idx'),
        ),
    ]
