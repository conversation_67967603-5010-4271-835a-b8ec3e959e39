from django.urls import path, include, get_resolver
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import AccountViewSet, chat
from .views.auth import logout, register_user, login_user, register_business
from .views.google_auth import google_login
from .views.chat import ChatConfigView, ChatThreadView, ChatMessageView
from .views.user import UserViewSet
from .views.utils import csrf_token_view, get_account_from_referer
from .views.document_views import DocumentViewSet
from .views.order import OrderView, GetOrderView
from .views.payment import CreatePaymentIntentView, ProcessPaymentMethodView
from django.http import HttpResponse
from .views.password_reset import request_password_reset, verify_reset_token, reset_password
from .views.accounts import check_duplicate_business, delete_test_businesses
import logging

# Setup logger
logger = logging.getLogger(__name__)

# DRF router for account viewset
router = DefaultRouter()
router.register(r'accounts', AccountViewSet, basename='account')
router.register(r'users', UserViewSet, basename='user')
router.register(r'documents', DocumentViewSet, basename='document')

# Debug function to print available URLs
def debug_print_urls():
    logger.info("Available URLs in main.urls:")
    for pattern in urlpatterns:
        logger.info(f"- {pattern.pattern}")
    return HttpResponse("URL debugging complete")

app_name = 'main'

urlpatterns = [
    # Debug endpoint
    path('debug-urls/', debug_print_urls, name='debug-urls'),
    
    # Auth endpoints
    path('auth/login/', login_user, name='login'),
    path('auth/logout/', logout, name='logout'),
    path('auth/google/', google_login, name='google_login'),

    # New auth endpoints
    path('auth/register/', register_user, name='register'),

    # Password reset endpoints
    path('auth/reset-password/request/', request_password_reset, name='request-password-reset'),
    path('auth/reset-password/verify/', verify_reset_token, name='verify-reset-token'),
    path('auth/reset-password/reset/', reset_password, name='reset-password'),

    # Chat endpoints
    path('chat/config/', ChatConfigView.as_view(), name='get_chat_config'),
    path('chat/thread/', ChatThreadView.as_view(), name='create_chat_thread'),
    path('chat/message/', ChatMessageView.as_view(), name='send_chat_message'),
    path('chat/submit-order/', OrderView.as_view(), name='submit-order'),
    path('chat/order/', GetOrderView.as_view(), name='get-order'),
    path('chat/email_to_user', chat.email_chat_history, name='email_chat_history'),
    
    # Payment endpoints under chat path (to bypass authentication)
    path('chat/payment/create-intent/', CreatePaymentIntentView.as_view(), name='chat-create-payment-intent'),
    path('chat/payment/process/', ProcessPaymentMethodView.as_view(), name='chat-process-payment'),

    # CSRF endpoint
    path('csrf/', csrf_token_view, name='csrf-token'),
    
    # Payment endpoints
    path('payment/create-intent/', CreatePaymentIntentView.as_view(), name='create-payment-intent'),
    path('payment/process/', ProcessPaymentMethodView.as_view(), name='process-payment'),

    # Account API endpoints
    path('accounts/<int:pk>/workspace/create/', AccountViewSet.as_view({'post': 'create_workspace'}), name='account-create-workspace'),
    path('accounts/<int:pk>/workspace/update/', AccountViewSet.as_view({'post': 'update_workspace'}), name='account-update-workspace'),
    path('accounts/<int:pk>/upload-chat-icon/', AccountViewSet.as_view({'post': 'upload_chat_icon'}), name='account-upload-chat-icon'),
    path('accounts/<int:pk>/remove-chat-icon/', AccountViewSet.as_view({'post': 'remove_chat_icon'}), name='account-remove-chat-icon'),
    path('accounts/<int:pk>/config/',           AccountViewSet.as_view({'patch': 'update_account_config'}), name='update_account_config'),

    # Debug endpoint
    path('debug/account-lookup/', lambda request: HttpResponse(
        f"Testing referer: {request.headers.get('Referer')}<br>"
        f"Result: {get_account_from_referer(request.headers.get('Referer'))}"
    )),

    # Include the router URLs
    path('', include(router.urls)),  # Changed from 'api/' to '' since we're already under /api
    
    # Business check endpoints
    path('accounts/check-duplicate/', check_duplicate_business, name='check-duplicate-business'),
    path('accounts/delete-test-businesses/', delete_test_businesses, name='delete-test-businesses'),
    
    # Business registration endpoints
    path('auth/register-business/', register_business, name='register-business'),
    path('auth/register/business/', register_business, name='register-business-alt'),  # Alternative URL format for register-business
    path('api/auth/register/business/', register_business, name='register-business-api'),  # API prefixed route
    path('api/auth/register-business/', register_business, name='register-business-api-alt'),  # API prefixed route alternative
]

# Debug - print all available URLs at module load time
print("\n=== AVAILABLE URLS ===")
for pattern in urlpatterns:
    print(f"- {pattern.pattern}")
print("=====================\n")
