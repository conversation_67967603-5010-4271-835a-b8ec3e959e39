# AIDA - AI-Powered Chat Widget Platform

AIDA is a multi-tenant SaaS platform that enables businesses to embed AI-powered chat widgets on their websites. The platform provides a complete solution for managing AI chatbots, including user authentication, subscription management, document processing, and customizable chat interfaces.

## Key Features

- **Multi-tenant Architecture**: Supports multiple business accounts with isolated data
- **Subscription Management**: Three tiers - Basic (free), Pro ($79/month), Premium ($149/month)
- **AI-Powered Chat**: Integration with AnythingLLM for intelligent conversations
- **Document Processing**: Upload and process documents to train your chatbot
- **Embeddable Widget**: Lightweight Svelte-based widget that can be embedded on any website
- **User Management**: Role-based access control with AIDA Admin, Business Admin, and Business User roles
- **Payment Integration**: Stripe integration for subscription payments

## Project Structure

```
aida/
├── frontend/          # React admin dashboard
├── backend/           # Django REST API server
├── js_component/      # Svelte embeddable widget
├── api_gateway/       # Kong API gateway configuration
├── docker/            # Docker configurations
├── demo_sites/        # Example widget integrations
└── docs/              # Project documentation
```

## Development Setup

### Prerequisites
- Node.js 16+
- Python 3.8+
- Docker and Docker Compose
- Redis (for Celery message broker)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aida
   ```

2. **Set up environment variables**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**
   ```bash
   npm run install:all
   ```

4. **Start Redis** (required for Celery)
   ```bash
   # macOS
   brew install redis && brew services start redis
   
   # Ubuntu/Debian
   sudo apt-get install redis-server && sudo systemctl start redis
   ```

5. **Run database migrations**
   ```bash
   cd backend
   python manage.py migrate
   ```

6. **Start all services**
   ```bash
   npm run dev
   ```

   This starts:
   - Frontend on http://localhost:3000
   - Backend on http://localhost:8000
   - JS Component dev server
   - Celery worker for async tasks

### API Gateway Setup (Optional)

The API Gateway handles routing between services:

```bash
cd api_gateway
./start.sh  # Starts Kong on port 8080
```

## Docker Setup

The project uses Docker for consistent development and deployment environments.

### Docker Components
- Frontend container: Serves the React admin interface
- Backend container: Runs the Django API server
- Database container: PostgreSQL for development and production
- Nginx container: Handles routing and static file serving

### Running with Docker
```bash
# Build and start all services
docker-compose up --build

# Run in detached mode
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

### Environment Variables
Create a `.env` file in the backend directory by copying `.env.example`:
```bash
cd backend
cp .env.example .env
```

Required environment variables:

1. Django Core Settings:
   - `DJANGO_SECRET_KEY`: Secret key for Django (50+ characters)
   - `JWT_SECRET_KEY`: Secret key for JWT tokens (32+ characters)

2. Database Configuration:
   - `POSTGRES_DB`: Database name
   - `POSTGRES_USER`: Database user
   - `POSTGRES_PASSWORD`: Database password
   - `POSTGRES_HOST`: Database host
   - `POSTGRES_PORT`: Database port (default: 5432)

3. Email Configuration:
   - `EMAIL_HOST`: SMTP server host (default: smtp.gmail.com)
   - `EMAIL_PORT`: SMTP server port (default: 587)
   - `EMAIL_HOST_USER`: SMTP username
   - `EMAIL_HOST_PASSWORD`: SMTP password or app-specific password
   - `DEFAULT_FROM_EMAIL`: Default sender email
   - `FRONTEND_URL`: Frontend URL for password reset links

4. Google OAuth2:
   - `GOOGLE_CLIENT_ID`: Google OAuth client ID
   - `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

5. Redis Configuration:
   - `REDIS_HOST`: Redis host (default: localhost)
   - `REDIS_PORT`: Redis port (default: 6379)
   - `REDIS_DB`: Redis database number (default: 0)

6. AnythingLLM Configuration:
   - `ANYTHING_LLM_URL`: AnythingLLM API URL
   - `ANYTHING_LLM_API_KEY`: AnythingLLM API key

7. Widget Configuration:
   - `WIDGET_DEFAULT_VERSION`: Default widget version

For development, you can use the example values in `.env.example`. For production, ensure all sensitive values are properly secured.

## Widget Integration

### Embedding the Chat Widget

To add the AIDA chat widget to your website:

```html
<!-- Add to your website's HTML -->
<div id="aida-chat-widget"></div>
<script src="https://your-domain.com/widget/aida-widget.js"></script>
<script>
  window.AidaWidget.init({
    containerId: 'aida-chat-widget',
    apiKey: 'your-business-api-key',
    theme: 'light', // or 'dark'
    position: 'bottom-right' // or 'bottom-left'
  });
</script>
```

### Example Integrations

Check the `/demo_sites` directory for examples:
- Basic HTML website
- WordPress plugin
- React application
- Vue.js integration

## Testing

```bash
# Run all tests
npm run test

# Component-specific tests
cd frontend && npm test    # Cucumber E2E tests
cd backend && python manage.py test  # Django unit tests
```

## API Documentation

- **Admin API**: `/api/v1/admin/` - JWT authenticated endpoints for admin dashboard
- **Public API**: `/api/v1/public/` - API key authenticated endpoints for widgets
- **API Docs**: `/api/docs/` - Interactive API documentation

## Security Features

- JWT authentication for admin users
- API key authentication for widget requests
- CORS configuration for cross-origin requests
- Rate limiting on public endpoints
- Data isolation between business accounts

## Troubleshooting

Common issues and solutions:

1. Database Connection Issues
```bash
docker-compose down -v  # Remove volumes
docker-compose up      # Recreate containers
```

2. Frontend Build Errors
```bash
cd frontend
rm -rf node_modules
npm clean-install
```

3. Backend Migration Issues
```bash
docker-compose exec backend python manage.py migrate --fake-initial
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`npm run test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and feature requests, please use the GitHub Issues page.
