{"name": "admin-platform", "version": "1.0.0", "private": true, "workspaces": ["frontend", "js_component"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:celery\" \"npm run dev:js-component\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "bash -c 'cd backend && source venv/bin/activate && python3 manage.py runserver 0.0.0.0:8000'", "dev:js-component": "cd js_component && npm run dev", "dev:celery": "cd backend && source venv/bin/activate && celery -A worker worker -l info", "install:all": "concurrently \"npm run install:frontend\" \"npm run install:js-component\" \"npm run install:backend\"", "install:frontend": "cd frontend && npm install", "install:js-component": "cd js_component && npm install", "install:backend": "cd backend && source venv/bin/activate && pip install -r requirements.txt", "build": "concurrently \"npm run build:frontend\" \"npm run build:js-component\"", "build:frontend": "cd frontend && npm run build", "build:js-component": "cd js_component && npm run build", "test": "concurrently \"npm run test:frontend\" \"npm run test:js-component\" \"npm run test:backend\"", "test:frontend": "cd frontend && npm test", "test:js-component": "cd js_component && npm test", "test:backend": "cd backend && python3 manage.py test", "clean": "concurrently \"npm run clean:frontend\" \"npm run clean:js-component\"", "clean:frontend": "cd frontend && rm -rf node_modules", "clean:js-component": "cd js_component && rm -rf node_modules", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down", "docker:clean": "docker-compose down -v"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "wait-on": "^7.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "lucide-react": "^0.477.0", "puppeteer": "^24.1.0", "react-router-dom": "^7.1.5", "tailwind-merge": "^3.0.2"}}