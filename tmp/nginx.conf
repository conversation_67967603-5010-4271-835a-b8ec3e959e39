server {
    server_name aida.research-triangle.ai; # managed by Certbot

    # Media files
    location /media/ {
        alias /var/www/aida/backend/media/;
        try_files $uri =404;
    }

    # JS Component
    location /aida/ {
        alias /var/www/aida/js_component/dist/;
        try_files $uri $uri/index.html =404;
        index index.html;
        add_header Cache-Control "public";
    }

    location /aida_releases/ {
        alias /var/www/aida/js_component/releases/;
        try_files $uri =404;
        index index.html;
        add_header Cache-Control "public";
    }

    location /widget/ {
        alias /var/www/aida/frontend/widget/;
        try_files $uri =404;
        index index.html;
        add_header Cache-Control "public";
    }

    # API gateway
    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /llm-api/ {
        proxy_pass http://127.0.0.1:8080/llm-api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Frontend
    location / {
        root /var/www/aida/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
    }

    # Handle static assets
    location ~ ^/(assets|images)/ {
        root /var/www/aida/frontend/dist;
        try_files $uri =404;
        add_header Cache-Control "public, max-age=31536000";
    }


    listen [::]:443 ssl ipv6only=on; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/aida.research-triangle.ai/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/aida.research-triangle.ai/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = aida.research-triangle.ai) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80 ;
    listen [::]:80 ;
    server_name aida.research-triangle.ai;
    return 404; # managed by Certbot


}

