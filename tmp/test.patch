diff --git a/frontend/src/App.tsx b/frontend/src/App.tsx
index 71b66c6..7f26586 100644
--- a/frontend/src/App.tsx
+++ b/frontend/src/App.tsx
@@ -1,5 +1,5 @@
-import React from 'react';
-import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
+import React, { useState } from 'react';
+import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
 import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
 import { AuthProvider } from './contexts/AuthContext';
 import { Toaster } from '@/components/ui/toaster';
@@ -18,6 +18,9 @@ import AidaSetup from './pages/setup/AidaSetup';
 import DocumentList from './pages/documents/DocumentList';
 import ProtectedRoute from './components/auth/ProtectedRoute';
 import AdminRoute from './components/auth/AdminRoute';
+import { BaseUser } from './types/user';
+import { allSampleUsers } from './data/sampleUsers';
+import AccountsView from './components/views/AccountsView';
 
 function App() {
   const queryClient = new QueryClient({
@@ -29,22 +32,91 @@ function App() {
     },
   });
 
+  // Demo mode state
+  const [isDemoMode, setIsDemoMode] = useState(false);
+  const [demoUser, setDemoUser] = useState<BaseUser>(allSampleUsers[0]);
+
+  // Development-only routes
+  const devRoutes = process.env.NODE_ENV === 'development' && (
+    <>
+      <Route 
+        path="/type-demo" 
+        element={
+          <div className="space-y-6">
+            <div className="p-6 bg-white rounded-lg shadow">
+              <h1 className="text-2xl font-bold text-gray-900 mb-4">
+                User Type Demonstration
+              </h1>
+              <p className="text-gray-600 mb-6">
+                This page demonstrates how the interface changes based on user type.
+                Use the selector in the top right to switch between different user types.
+              </p>
+            </div>
+            
+            <AccountsView user={demoUser} />
+          </div>
+        } 
+      />
+      <Route path="/demo-exit" element={
+        <div className="p-6 text-center">
+          <h2 className="text-xl font-semibold mb-4">Exiting Demo Mode</h2>
+          <p className="mb-4">You are now returning to the normal application.</p>
+          <button
+            onClick={() => {
+              setIsDemoMode(false);
+              window.location.href = '/';
+            }}
+            className="px-4 py-2 bg-blue-600 text-white rounded-md"
+          >
+            Continue
+          </button>
+        </div>
+      } />
+    </>
+  );
+
+  // Handle demo mode entry
+  const handleDemoEntry = () => {
+    setIsDemoMode(true);
+    setDemoUser(allSampleUsers[0]); // Reset to first user
+    window.location.href = '/type-demo';
+  };
+
   return (
     <QueryClientProvider client={queryClient}>
       <Router>
         <AuthProvider>
-          <MainLayout>
+          <MainLayout 
+            currentUser={isDemoMode ? demoUser : undefined}
+            showUserTypeSelector={isDemoMode}
+            onUserChange={isDemoMode ? setDemoUser : undefined}
+          >
             <Routes>
-              <Route path="/login" element={<Login />} />
-              <Route path="/register" element={<Register />} />
-              <Route path="/" element={<Home />} />
+              {/* Auth Routes */}
+              <Route path="/login" element={isDemoMode ? <Navigate to="/type-demo" replace /> : <Login />} />
+              <Route path="/register" element={isDemoMode ? <Navigate to="/type-demo" replace /> : <Register />} />
+              
+              {/* Protected Routes */}
+              <Route path="/" element={
+                isDemoMode ? (
+                  <Navigate to="/type-demo" replace />
+                ) : (
+                  <ProtectedRoute><Home /></ProtectedRoute>
+                )
+              } />
               <Route path="/about" element={<About />} />
               <Route path="/subscription" element={<SubscriptionPage />} />
               
-              {/* Protected Account Routes */}
+              {/* Account Routes */}
               <Route
                 path="/accounts"
-                element={<ProtectedRoute><AccountsPage /></ProtectedRoute>}
+                element={
+                  isDemoMode ? (
+                    <AccountsView user={demoUser} />
+                  ) : (
+                    <ProtectedRoute><AccountsPage /></ProtectedRoute>
+                  )
+                }
               />
               <Route
                 path="/accounts/add"
@@ -55,13 +127,13 @@ function App() {
                 element={<ProtectedRoute><EditAccount /></ProtectedRoute>}
               />
               
-              {/* User Settings Route */}
+              {/* User Settings */}
               <Route
                 path="/settings"
                 element={<ProtectedRoute><AccountSettings /></ProtectedRoute>}
               />
               
-              {/* Aida Setup Route */}
+              {/* Aida Setup */}
               <Route
                 path="/setup"
                 element={<ProtectedRoute><AidaSetup /></ProtectedRoute>}
@@ -78,9 +150,22 @@ function App() {
                 path="/accounts/:accountId/documents"
                 element={<ProtectedRoute><DocumentList /></ProtectedRoute>}
               />
+
+              {/* Development Routes */}
+              {devRoutes}
             </Routes>
           </MainLayout>
           <Toaster />
+
+          {/* Demo Mode Entry Button (Development Only) */}
+          {process.env.NODE_ENV === 'development' && !isDemoMode && (
+            <button
+              onClick={handleDemoEntry}
+              className="fixed bottom-4 right-4 px-4 py-2 bg-blue-600 text-white rounded-md shadow-lg hover:bg-blue-700 transition-colors"
+            >
+              Enter Demo Mode
+            </button>
+          )}
         </AuthProvider>
       </Router>
     </QueryClientProvider>
diff --git a/frontend/src/components/Layout/MainLayout.tsx b/frontend/src/components/Layout/MainLayout.tsx
index a552634..13f7538 100644
--- a/frontend/src/components/Layout/MainLayout.tsx
+++ b/frontend/src/components/Layout/MainLayout.tsx
@@ -1,47 +1,101 @@
 import { useLocation, useNavigate } from 'react-router-dom';
 import { useAuth } from '../../contexts/AuthContext';
 import { Button } from "@/components/ui/button";
+import { BaseUser, UserType } from '../../types/user';
+import UserTypeDemo from '../demo/UserTypeDemo';
 
 interface MainLayoutProps {
   children: React.ReactNode;
+  currentUser?: BaseUser;  // Optional for demo mode
+  showUserTypeSelector?: boolean;
+  onUserChange?: (user: BaseUser) => void;
 }
 
-const MainLayout = ({ children }: MainLayoutProps) => {
+const MainLayout = ({ 
+  children,
+  currentUser,
+  showUserTypeSelector = false,
+  onUserChange,
+}: MainLayoutProps) => {
   const location = useLocation();
   const navigate = useNavigate();
-  const { isAuthenticated, user, logout } = useAuth();
+  const { isAuthenticated, user, userType, logout } = useAuth();
   const year = new Date().getFullYear();
 
+  // Define menu items with user type and group restrictions
   const menuItems = [
     {
       key: '/',
       label: 'Home',
-      path: '/'
+      path: '/',
+      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER, UserType.DEMO_USER],
+      allowedGroups: ['Business Admins', 'Business Regular Users'],
     },
-    ...(isAuthenticated ? [{
+    {
       key: '/accounts',
       label: 'Accounts',
-      path: '/accounts'
-    }] : []),
-    ...(isAuthenticated && (user?.is_staff || user?.is_superuser) ? [{
+      path: '/accounts',
+      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN],
+      allowedGroups: ['Business Admins'],
+      requireAdmin: true,
+    },
+    {
       key: '/users',
       label: 'Users',
-      path: '/users'
-    }] : []),
-    ...(isAuthenticated ? [{
+      path: '/users',
+      allowedTypes: [UserType.AIDA_ADMIN],
+      requireSuperuser: true,
+    },
+    {
       key: '/subscription',
       label: 'Subscription',
-      path: '/subscription'
-    }] : []),
+      path: '/subscription',
+      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER],
+      allowedGroups: ['Business Admins', 'Business Regular Users'],
+    },
     {
       key: '/about',
       label: 'About',
-      path: '/about'
+      path: '/about',
+      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER, UserType.DEMO_USER],
+      allowedGroups: ['Business Admins', 'Business Regular Users'],
+      publicRoute: true,
     },
   ];
 
+  // Filter menu items based on user type in demo mode, or based on auth and groups in normal mode
+  const visibleMenuItems = menuItems.filter(item => {
+    if (currentUser) {
+      // Demo mode - filter by user type
+      return item.allowedTypes.includes(currentUser.userType);
+    } else {
+      // Normal mode - filter by auth state and groups
+      if (!isAuthenticated && !item.publicRoute) return false;
+      if (item.requireSuperuser && !user?.is_superuser) return false;
+      if (!user) return item.publicRoute;
+      if (user.is_superuser) return true;
+      
+      // Check admin-only routes
+      if (item.requireAdmin && !user.groups.includes('Business Admins')) return false;
+      
+      return item.allowedGroups?.some(group => user.groups.includes(group)) || item.publicRoute;
+    }
+  });
+
   return (
     <div className="min-h-full flex flex-col">
+      {/* User Type Selector (Development Only) */}
+      {showUserTypeSelector && onUserChange && currentUser && (
+        <div className="bg-white border-b shadow-sm">
+          <div className="container mx-auto px-4">
+            <UserTypeDemo
+              currentUser={currentUser}
+              onUserChange={onUserChange}
+            />
+          </div>
+        </div>
+      )}
+
       <header className="border-b">
         <div className="container mx-auto px-4 h-16 flex items-center justify-between">
           <Button 
@@ -56,43 +110,63 @@ const MainLayout = ({ children }: MainLayoutProps) => {
             />
           </Button>
           <div className="flex items-center gap-4">
-            {isAuthenticated ? (
-              <>
-                <Button 
-                  variant="ghost"
-                  className="text-muted-foreground hover:text-primary"
-                  onClick={() => navigate('/settings')}
-                >
-                  Welcome, {user?.name || 'User'}
-                </Button>
-                <Button 
-                  variant="ghost"
-                  onClick={logout}
-                >
-                  Logout
-                </Button>
-              </>
+            {currentUser ? (
+              // Demo mode user display
+              <div className="flex items-center gap-2">
+                <span className="text-sm text-gray-700">
+                  {currentUser.firstName} {currentUser.lastName}
+                </span>
+                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
+                  {currentUser.userType}
+                </span>
+              </div>
             ) : (
-              <>
-                <Button 
-                  variant="ghost"
-                  onClick={() => navigate('/login')}
-                >
-                  Login
-                </Button>
-                <Button
-                  onClick={() => navigate('/register')}
-                >
-                  Register
-                </Button>
-              </>
+              // Normal auth mode
+              isAuthenticated ? (
+                <>
+                  <div className="flex items-center gap-2">
+                    <Button 
+                      variant="ghost"
+                      className="text-muted-foreground hover:text-primary"
+                      onClick={() => navigate('/settings')}
+                    >
+                      Welcome, {user?.name || 'User'}
+                    </Button>
+                    {userType && (
+                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
+                        {userType}
+                      </span>
+                    )}
+                  </div>
+                  <Button 
+                    variant="ghost"
+                    onClick={logout}
+                  >
+                    Logout
+                  </Button>
+                </>
+              ) : (
+                <>
+                  <Button 
+                    variant="ghost"
+                    onClick={() => navigate('/login')}
+                  >
+                    Login
+                  </Button>
+                  <Button
+                    onClick={() => navigate('/register')}
+                  >
+                    Register
+                  </Button>
+                </>
+              )
             )}
           </div>
         </div>
         <nav className="border-t bg-muted">
           <div className="container mx-auto px-4">
             <div className="flex space-x-4 h-12">
-              {menuItems.map((item) => (
+              {visibleMenuItems.map((item) => (
                 <Button
                   key={item.key}
                   variant="ghost"
diff --git a/frontend/src/components/demo/UserTypeDemo.tsx b/frontend/src/components/demo/UserTypeDemo.tsx
new file mode 100644
index 0000000..2cef2c7
--- /dev/null
+++ b/frontend/src/components/demo/UserTypeDemo.tsx
@@ -0,0 +1,67 @@
+import React, { useState } from 'react';
+import { BaseUser } from '../../types/user';
+import { allSampleUsers } from '../../data/sampleUsers';
+
+interface UserTypeDemoProps {
+  onUserChange: (user: BaseUser) => void;
+  currentUser: BaseUser;
+}
+
+export const UserTypeDemo: React.FC<UserTypeDemoProps> = ({
+  onUserChange,
+  currentUser,
+}) => {
+  const [isExpanded, setIsExpanded] = useState(false);
+
+  return (
+    <div 
+      className={`fixed top-20 right-4 z-50 transition-all duration-200 ease-in-out ${
+        isExpanded ? 'w-64' : 'w-12'
+      }`}
+    >
+      {/* Toggle Button */}
+      <button
+        onClick={() => setIsExpanded(!isExpanded)}
+        className={`absolute right-0 top-0 p-2 bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 ${
+          isExpanded ? 'w-8' : 'w-12'
+        }`}
+        title="Toggle user selector"
+      >
+        {isExpanded ? '×' : '👤'}
+      </button>
+
+      {/* Selection Panel */}
+      <div className={`
+        mt-12 bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-200
+        ${isExpanded ? 'opacity-100 visible' : 'opacity-0 invisible'}
+      `}>
+        <div className="p-4">
+          <h3 className="text-sm font-medium text-gray-700 mb-2">Select User Type</h3>
+          <div className="space-y-2">
+            {allSampleUsers.map(user => (
+              <button
+                key={user.id}
+                onClick={() => {
+                  onUserChange(user);
+                  setIsExpanded(false);
+                }}
+                className={`w-full px-4 py-2 text-sm font-medium rounded-md text-left transition-colors ${
+                  currentUser.id === user.id
+                    ? 'bg-blue-100 text-blue-700'
+                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
+                }`}
+              >
+                {user.firstName} {user.lastName}
+                <span className="ml-2 text-xs text-gray-500">
+                  ({user.userType})
+                </span>
+              </button>
+            ))}
+          </div>
+        </div>
+      </div>
+    </div>
+  );
+};
+
+export default UserTypeDemo; 
\ No newline at end of file
diff --git a/frontend/src/components/navigation/Header.tsx b/frontend/src/components/navigation/Header.tsx
deleted file mode 100644
index 65a47dc..0000000
--- a/frontend/src/components/navigation/Header.tsx
+++ /dev/null
@@ -1,37 +0,0 @@
-import React from 'react';
-import { UserType } from '../../types/user';
-
-interface HeaderProps {
-  userType: UserType;
-  userName: string;
-  onLogout: () => void;
-}
-
-export const Header: React.FC<HeaderProps> = ({ userType, userName, onLogout }) => {
-  return (
-    <header className="w-full h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 fixed top-0 z-50">
-      <div className="flex items-center">
-        <span className="text-xl font-semibold text-gray-800">AIDA</span>
-        {/* User type badge */}
-        <span className="ml-4 px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
-          {userType}
-        </span>
-      </div>
-
-      <div className="flex items-center space-x-4">
-        {/* User menu */}
-        <div className="flex items-center">
-          <span className="text-gray-700 mr-4">{userName}</span>
-          <button
-            onClick={onLogout}
-            className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
-          >
-            Logout
-          </button>
-        </div>
-      </div>
-    </header>
-  );
-};
-
-export default Header; 
\ No newline at end of file
diff --git a/frontend/src/components/navigation/Sidebar.tsx b/frontend/src/components/navigation/Sidebar.tsx
deleted file mode 100644
index 6422204..0000000
--- a/frontend/src/components/navigation/Sidebar.tsx
+++ /dev/null
@@ -1,90 +0,0 @@
-import React, { useState } from 'react';
-import { Link, useLocation } from 'react-router-dom';
-import { UserType } from '../../types/user';
-import { NavigationItem, navigationConfig } from './navigationConfig';
-
-interface SidebarProps {
-  userType: UserType;
-}
-
-export const Sidebar: React.FC<SidebarProps> = ({ userType }) => {
-  const location = useLocation();
-  const [expandedItems, setExpandedItems] = useState<string[]>([]);
-
-  const toggleExpand = (path: string) => {
-    setExpandedItems(prev =>
-      prev.includes(path)
-        ? prev.filter(item => item !== path)
-        : [...prev, path]
-    );
-  };
-
-  const renderNavigationItem = (item: NavigationItem) => {
-    const isActive = location.pathname === item.path;
-    const isExpanded = expandedItems.includes(item.path);
-    const hasChildren = item.children && item.children.length > 0;
-
-    return (
-      <div key={item.path} className="w-full">
-        <div
-          className={`flex items-center w-full px-4 py-2 text-sm font-medium rounded-md cursor-pointer
-            ${isActive ? 'bg-blue-100 text-blue-900' : 'text-gray-700 hover:bg-gray-100'}`}
-          onClick={() => hasChildren ? toggleExpand(item.path) : null}
-        >
-          <Link
-            to={item.path}
-            className="flex-1"
-            onClick={(e) => hasChildren && e.preventDefault()}
-          >
-            {item.label}
-          </Link>
-          {hasChildren && (
-            <span className="ml-2">
-              {isExpanded ? '▼' : '▶'}
-            </span>
-          )}
-        </div>
-
-        {hasChildren && isExpanded && (
-          <div className="ml-4 mt-1 space-y-1">
-            {item.children!.map(child => (
-              <Link
-                key={child.path}
-                to={child.path}
-                className={`block px-4 py-2 text-sm font-medium rounded-md
-                  ${location.pathname === child.path
-                    ? 'bg-blue-50 text-blue-900'
-                    : 'text-gray-600 hover:bg-gray-50'
-                  }`}
-              >
-                {child.label}
-              </Link>
-            ))}
-          </div>
-        )}
-      </div>
-    );
-  };
-
-  return (
-    <aside className="w-64 h-full bg-white border-r border-gray-200 fixed left-0 top-16 overflow-y-auto">
-      <nav className="px-2 py-4 space-y-1">
-        {navigationConfig[userType].map(renderNavigationItem)}
-      </nav>
-
-      {/* Trial badge for demo users */}
-      {userType === UserType.DEMO_USER && (
-        <div className="absolute bottom-0 left-0 right-0 p-4 bg-yellow-50 border-t border-yellow-100">
-          <p className="text-sm text-yellow-800 font-medium">
-            Demo Account
-          </p>
-          <p className="text-xs text-yellow-600 mt-1">
-            Contact sales for full access
-          </p>
-        </div>
-      )}
-    </aside>
-  );
-};
-
-export default Sidebar; 
\ No newline at end of file
diff --git a/frontend/src/components/navigation/navigationConfig.ts b/frontend/src/components/navigation/navigationConfig.ts
deleted file mode 100644
index 62ad55a..0000000
--- a/frontend/src/components/navigation/navigationConfig.ts
+++ /dev/null
@@ -1,102 +0,0 @@
-import { UserType } from '../../types/user';
-
-export interface NavigationItem {
-  label: string;
-  path: string;
-  icon?: string;
-  children?: NavigationItem[];
-}
-
-type NavigationConfig = {
-  [key in UserType]: NavigationItem[];
-};
-
-export const navigationConfig: NavigationConfig = {
-  [UserType.AIDA_ADMIN]: [
-    {
-      label: 'Dashboard',
-      path: '/admin/dashboard',
-    },
-    {
-      label: 'Business Management',
-      path: '/admin/businesses',
-      children: [
-        { label: 'All Businesses', path: '/admin/businesses/list' },
-        { label: 'Add Business', path: '/admin/businesses/add' },
-      ],
-    },
-    {
-      label: 'User Management',
-      path: '/admin/users',
-      children: [
-        { label: 'All Users', path: '/admin/users/list' },
-        { label: 'Add User', path: '/admin/users/add' },
-      ],
-    },
-    {
-      label: 'System Settings',
-      path: '/admin/settings',
-    },
-  ],
-
-  [UserType.BUSINESS_ADMIN]: [
-    {
-      label: 'Dashboard',
-      path: '/business/dashboard',
-    },
-    {
-      label: 'User Management',
-      path: '/business/users',
-      children: [
-        { label: 'All Users', path: '/business/users/list' },
-        { label: 'Add User', path: '/business/users/add' },
-      ],
-    },
-    {
-      label: 'Business Settings',
-      path: '/business/settings',
-    },
-    {
-      label: 'Analytics',
-      path: '/business/analytics',
-    },
-  ],
-
-  [UserType.BUSINESS_USER]: [
-    {
-      label: 'Dashboard',
-      path: '/dashboard',
-    },
-    {
-      label: 'Tasks',
-      path: '/tasks',
-    },
-    {
-      label: 'Reports',
-      path: '/reports',
-    },
-    {
-      label: 'Profile',
-      path: '/profile',
-    },
-  ],
-
-  [UserType.DEMO_USER]: [
-    {
-      label: 'Demo Dashboard',
-      path: '/demo/dashboard',
-    },
-    {
-      label: 'Features Overview',
-      path: '/demo/features',
-    },
-    {
-      label: 'Sample Reports',
-      path: '/demo/reports',
-    },
-    {
-      label: 'Get Started',
-      path: '/demo/get-started',
-    },
-  ],
-}; 
\ No newline at end of file
diff --git a/frontend/src/components/views/AccountsView.tsx b/frontend/src/components/views/AccountsView.tsx
new file mode 100644
index 0000000..e689d1b
--- /dev/null
+++ b/frontend/src/components/views/AccountsView.tsx
@@ -0,0 +1,125 @@
+import React from 'react';
+import { BaseUser, isAidaAdmin, isBusinessAdmin, isBusinessUser, isDemoUser } from '../../types/user';
+
+interface AccountsViewProps {
+  user: BaseUser;
+}
+
+export const AccountsView: React.FC<AccountsViewProps> = ({ user }) => {
+  // AIDA Admin View
+  if (isAidaAdmin(user)) {
+    return (
+      <div className="space-y-6">
+        <div className="bg-white rounded-lg shadow p-6">
+          <h2 className="text-xl font-semibold text-gray-800 mb-4">System-wide Accounts</h2>
+          <div className="grid grid-cols-3 gap-4">
+            <div className="bg-blue-50 p-4 rounded-lg">
+              <h3 className="font-medium text-blue-800">Total Businesses</h3>
+              <p className="text-2xl font-bold text-blue-900">247</p>
+            </div>
+            <div className="bg-green-50 p-4 rounded-lg">
+              <h3 className="font-medium text-green-800">Active Users</h3>
+              <p className="text-2xl font-bold text-green-900">1,234</p>
+            </div>
+            <div className="bg-purple-50 p-4 rounded-lg">
+              <h3 className="font-medium text-purple-800">New This Month</h3>
+              <p className="text-2xl font-bold text-purple-900">+23</p>
+            </div>
+          </div>
+        </div>
+      </div>
+    );
+  }
+
+  // Business Admin View
+  if (isBusinessAdmin(user)) {
+    return (
+      <div className="space-y-6">
+        <div className="bg-white rounded-lg shadow p-6">
+          <h2 className="text-xl font-semibold text-gray-800 mb-4">{user.businessName} Accounts</h2>
+          <div className="grid grid-cols-2 gap-4">
+            <div className="bg-blue-50 p-4 rounded-lg">
+              <h3 className="font-medium text-blue-800">Active Users</h3>
+              <p className="text-2xl font-bold text-blue-900">48</p>
+            </div>
+            <div className="bg-green-50 p-4 rounded-lg">
+              <h3 className="font-medium text-green-800">Available Seats</h3>
+              <p className="text-2xl font-bold text-green-900">12</p>
+            </div>
+          </div>
+        </div>
+      </div>
+    );
+  }
+
+  // Business User View
+  if (isBusinessUser(user)) {
+    return (
+      <div className="space-y-6">
+        <div className="bg-white rounded-lg shadow p-6">
+          <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Account</h2>
+          <div className="space-y-4">
+            <div className="bg-blue-50 p-4 rounded-lg">
+              <h3 className="font-medium text-blue-800">Account Status</h3>
+              <p className="text-lg text-blue-900">Active</p>
+            </div>
+            <div className="bg-green-50 p-4 rounded-lg">
+              <h3 className="font-medium text-green-800">Last Login</h3>
+              <p className="text-lg text-green-900">Today at 9:30 AM</p>
+            </div>
+          </div>
+        </div>
+      </div>
+    );
+  }
+
+  // Demo User View
+  if (isDemoUser(user)) {
+    return (
+      <div className="space-y-6">
+        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow p-6 text-white">
+          <h2 className="text-xl font-semibold mb-2">Account Management Demo</h2>
+          <p className="text-blue-100 mb-4">
+            Explore how AIDA helps you manage your business accounts efficiently
+          </p>
+        </div>
+        
+        <div className="grid grid-cols-2 gap-6">
+          <div className="bg-white rounded-lg shadow p-6">
+            <h3 className="text-lg font-semibold text-gray-800 mb-3">Feature Highlights</h3>
+            <ul className="space-y-2">
+              <li className="flex items-center text-green-700">
+                <span className="mr-2">✓</span>
+                Centralized account management
+              </li>
+              <li className="flex items-center text-green-700">
+                <span className="mr-2">✓</span>
+                Role-based access control
+              </li>
+              <li className="flex items-center text-green-700">
+                <span className="mr-2">✓</span>
+                Usage analytics and reporting
+              </li>
+            </ul>
+          </div>
+
+          <div className="bg-white rounded-lg shadow p-6">
+            <h3 className="text-lg font-semibold text-gray-800 mb-3">Demo Actions</h3>
+            <div className="space-y-2">
+              <button className="w-full p-2 bg-blue-50 hover:bg-blue-100 rounded text-blue-700 text-sm font-medium text-left">
+                Try Account Creation
+              </button>
+              <button className="w-full p-2 bg-green-50 hover:bg-green-100 rounded text-green-700 text-sm font-medium text-left">
+                View Sample Reports
+              </button>
+            </div>
+          </div>
+        </div>
+      </div>
+    );
+  }
+
+  return null;
+};
+
+export default AccountsView; 
\ No newline at end of file
diff --git a/frontend/src/contexts/AuthContext.tsx b/frontend/src/contexts/AuthContext.tsx
index 2b0afb4..1896811 100644
--- a/frontend/src/contexts/AuthContext.tsx
+++ b/frontend/src/contexts/AuthContext.tsx
@@ -2,6 +2,7 @@ import React, { createContext, useContext, useState, useEffect, useCallback, use
 import { useNavigate } from 'react-router-dom';
 import * as jwtDecode from 'jwt-decode';
 import api from '../utils/api';
+import { UserType } from '../types/user';
 
 interface User {
   user_id: number;
@@ -9,90 +10,52 @@ interface User {
   name: string;
   is_staff: boolean;
   is_superuser: boolean;
+  groups: string[];
   exp?: number;
 }
 
-interface AuthContextType {
+interface AuthContextValue {
   isAuthenticated: boolean;
   user: User | null;
+  userType: UserType | null;
   login: (token: string) => void;
   logout: () => void;
 }
 
-const AuthContext = createContext<AuthContextType | undefined>(undefined);
+const AuthContext = createContext<AuthContextValue | undefined>(undefined);
+
+export const useAuth = () => {
+  const context = useContext(AuthContext);
+  if (context === undefined) {
+    throw new Error('useAuth must be used within an AuthProvider');
+  }
+  return context;
+};
 
 export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
   const [isAuthenticated, setIsAuthenticated] = useState(false);
   const [user, setUser] = useState<User | null>(null);
-  const [loading, setLoading] = useState(true);
+  const [userType, setUserType] = useState<UserType | null>(null);
   const navigate = useNavigate();
-  const initialAuthCheck = useRef(false);
-
-  const verifyAuth = useCallback(async () => {
-    if (initialAuthCheck.current) return;
-    initialAuthCheck.current = true;
-    setLoading(true);
-    const token = localStorage.getItem('token');
 
-    if (!token) {
-      setIsAuthenticated(false);
-      setUser(null);
-      setLoading(false);
-      return;
+  const determineUserType = (user: User): UserType => {
+    if (user.is_superuser) {
+      return UserType.AIDA_ADMIN;
     }
-
-    try {
-      const decoded = jwtDecode.jwtDecode<User>(token);
-
-      // Check token expiration
-      const currentTime = Date.now() / 1000;
-      if (decoded.exp && decoded.exp < currentTime) {
-        throw new Error('Token expired');
-      }
-
-      setIsAuthenticated(true);
-      setUser(decoded);
-      console.log('Auth restored for user:', decoded.email);
-    } catch (error) {
-      console.error('Authentication error:', error);
-      localStorage.removeItem('token');
-      setIsAuthenticated(false);
-      setUser(null);
-
-      // Only redirect if we're not already on the login page
-      if (window.location.pathname !== '/login') {
-        navigate('/login', { replace: true });
-      }
-    } finally {
-      setLoading(false);
+    if (user.groups.includes('Business Admins')) {
+      return UserType.BUSINESS_ADMIN;
     }
-  }, [navigate]);
-
-  // Check auth state on mount
-  useEffect(() => {
-    let isMounted = true;
-
-    const checkAuth = async () => {
-      if (isMounted) {
-        await verifyAuth();
-      }
-    };
-
-    checkAuth();
-
-    return () => {
-      isMounted = false;
-    };
-  }, [verifyAuth]);
-
-  if (loading) {
-    return null; // Or return a loading spinner
-  }
+    if (user.groups.includes('Business Regular Users')) {
+      return UserType.BUSINESS_USER;
+    }
+    return UserType.DEMO_USER;
+  };
 
   const login = (token: string) => {
     localStorage.setItem('token', token);
     const decoded = jwtDecode.jwtDecode<User>(token);
     setUser(decoded);
+    setUserType(determineUserType(decoded));
     setIsAuthenticated(true);
     console.log('Logged in as:', decoded.email);
   };
@@ -101,21 +64,36 @@ export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children
     localStorage.removeItem('token');
     setIsAuthenticated(false);
     setUser(null);
+    setUserType(null);
     navigate('/login');
     console.log('Logged out');
   };
 
+  // Check token on mount
+  useEffect(() => {
+    const token = localStorage.getItem('token');
+    if (token) {
+      try {
+        const decoded = jwtDecode.jwtDecode<User>(token);
+        const currentTime = Date.now() / 1000;
+        
+        if (decoded.exp && decoded.exp > currentTime) {
+          setUser(decoded);
+          setUserType(determineUserType(decoded));
+          setIsAuthenticated(true);
+        } else {
+          logout();
+        }
+      } catch (error) {
+        console.error('Token validation error:', error);
+        logout();
+      }
+    }
+  }, []);
+
   return (
-    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
+    <AuthContext.Provider value={{ isAuthenticated, user, userType, login, logout }}>
       {children}
     </AuthContext.Provider>
   );
 };
-
-export const useAuth = () => {
-  const context = useContext(AuthContext);
-  if (context === undefined) {
-    throw new Error('useAuth must be used within an AuthProvider');
-  }
-  return context;
-};
diff --git a/frontend/src/data/sampleUsers.ts b/frontend/src/data/sampleUsers.ts
new file mode 100644
index 0000000..1d17a0b
--- /dev/null
+++ b/frontend/src/data/sampleUsers.ts
@@ -0,0 +1,62 @@
+import { UserType, AidaAdmin, BusinessAdmin, BusinessUser, DemoUser } from '../types/user';
+
+// AIDA Admin user with full system access
+export const sampleAidaAdmin: AidaAdmin = {
+  id: 'admin-001',
+  email: '<EMAIL>',
+  firstName: 'Sarah',
+  lastName: 'Chen',
+  userType: UserType.AIDA_ADMIN,
+  systemAccessLevel: 'FULL',
+  createdAt: '2024-01-01T00:00:00Z',
+  updatedAt: '2024-01-01T00:00:00Z',
+};
+
+// Business Admin for TechCorp
+export const sampleBusinessAdmin: BusinessAdmin = {
+  id: 'ba-001',
+  email: '<EMAIL>',
+  firstName: 'Michael',
+  lastName: 'Rodriguez',
+  userType: UserType.BUSINESS_ADMIN,
+  businessId: 'tech-001',
+  businessName: 'TechCorp Solutions',
+  canManageUsers: true,
+  createdAt: '2024-01-15T00:00:00Z',
+  updatedAt: '2024-01-15T00:00:00Z',
+};
+
+// Regular Business User under TechCorp
+export const sampleBusinessUser: BusinessUser = {
+  id: 'bu-001',
+  email: '<EMAIL>',
+  firstName: 'Emma',
+  lastName: 'Thompson',
+  userType: UserType.BUSINESS_USER,
+  businessId: 'tech-001',
+  businessName: 'TechCorp Solutions',
+  adminId: 'ba-001', // References Michael Rodriguez
+  createdAt: '2024-02-01T00:00:00Z',
+  updatedAt: '2024-02-01T00:00:00Z',
+};
+
+// Demo User created by Sales
+export const sampleDemoUser: DemoUser = {
+  id: 'demo-001',
+  email: '<EMAIL>',
+  firstName: 'Alex',
+  lastName: 'Foster',
+  userType: UserType.DEMO_USER,
+  expiryDate: '2024-03-15T00:00:00Z', // 30-day trial
+  createdBy: 'David Kim', // Sales representative
+  createdAt: '2024-02-15T00:00:00Z',
+  updatedAt: '2024-02-15T00:00:00Z',
+};
+
+// Export all sample users in an array for easy iteration
+export const allSampleUsers = [
+  sampleAidaAdmin,
+  sampleBusinessAdmin,
+  sampleBusinessUser,
+  sampleDemoUser,
+]; 
\ No newline at end of file
diff --git a/frontend/src/layouts/AidaAdminLayout.tsx b/frontend/src/layouts/AidaAdminLayout.tsx
deleted file mode 100644
index eb38dcd..0000000
--- a/frontend/src/layouts/AidaAdminLayout.tsx
+++ /dev/null
@@ -1,64 +0,0 @@
-import React from 'react';
-import { UserType } from '../types/user';
-import BaseLayout from './BaseLayout';
-import type { AidaAdmin } from '../types/user';
-
-interface AidaAdminLayoutProps {
-  user: AidaAdmin;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const AidaAdminLayout: React.FC<AidaAdminLayoutProps> = ({
-  user,
-  onLogout,
-  children,
-}) => {
-  const userName = `${user.firstName} ${user.lastName}`;
-
-  return (
-    <BaseLayout
-      userType={UserType.AIDA_ADMIN}
-      userName={userName}
-      onLogout={onLogout}
-    >
-      {/* Admin-specific quick actions */}
-      <div className="mb-6 bg-white rounded-lg shadow p-4">
-        <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
-        <div className="grid grid-cols-3 gap-4">
-          <button className="p-3 bg-blue-50 hover:bg-blue-100 rounded-md text-blue-700 text-sm font-medium">
-            Add New Business
-          </button>
-          <button className="p-3 bg-green-50 hover:bg-green-100 rounded-md text-green-700 text-sm font-medium">
-            Manage Users
-          </button>
-          <button className="p-3 bg-purple-50 hover:bg-purple-100 rounded-md text-purple-700 text-sm font-medium">
-            System Settings
-          </button>
-        </div>
-      </div>
-
-      {/* System Status Banner */}
-      {user.systemAccessLevel === 'FULL' && (
-        <div className="mb-6 bg-white rounded-lg shadow p-4">
-          <div className="flex items-center justify-between">
-            <div>
-              <h3 className="text-sm font-medium text-gray-700">System Status</h3>
-              <p className="text-sm text-gray-500">Full system access enabled</p>
-            </div>
-            <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
-              Active
-            </span>
-          </div>
-        </div>
-      )}
-
-      {/* Main content */}
-      <div className="bg-white rounded-lg shadow">
-        {children}
-      </div>
-    </BaseLayout>
-  );
-};
-
-export default AidaAdminLayout; 
\ No newline at end of file
diff --git a/frontend/src/layouts/BaseLayout.tsx b/frontend/src/layouts/BaseLayout.tsx
deleted file mode 100644
index f060b1b..0000000
--- a/frontend/src/layouts/BaseLayout.tsx
+++ /dev/null
@@ -1,41 +0,0 @@
-import React from 'react';
-import { UserType } from '../types/user';
-import Header from '../components/navigation/Header';
-import Sidebar from '../components/navigation/Sidebar';
-
-interface BaseLayoutProps {
-  userType: UserType;
-  userName: string;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const BaseLayout: React.FC<BaseLayoutProps> = ({
-  userType,
-  userName,
-  onLogout,
-  children,
-}) => {
-  return (
-    <div className="min-h-screen bg-gray-50">
-      {/* Header */}
-      <Header
-        userType={userType}
-        userName={userName}
-        onLogout={onLogout}
-      />
-
-      {/* Sidebar */}
-      <Sidebar userType={userType} />
-
-      {/* Main Content */}
-      <main className="ml-64 pt-16 min-h-screen">
-        <div className="p-6">
-          {children}
-        </div>
-      </main>
-    </div>
-  );
-};
-
-export default BaseLayout; 
\ No newline at end of file
diff --git a/frontend/src/layouts/BusinessAdminLayout.tsx b/frontend/src/layouts/BusinessAdminLayout.tsx
deleted file mode 100644
index 14c8429..0000000
--- a/frontend/src/layouts/BusinessAdminLayout.tsx
+++ /dev/null
@@ -1,66 +0,0 @@
-import React from 'react';
-import { UserType } from '../types/user';
-import BaseLayout from './BaseLayout';
-import type { BusinessAdmin } from '../types/user';
-
-interface BusinessAdminLayoutProps {
-  user: BusinessAdmin;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const BusinessAdminLayout: React.FC<BusinessAdminLayoutProps> = ({
-  user,
-  onLogout,
-  children,
-}) => {
-  const userName = `${user.firstName} ${user.lastName}`;
-
-  return (
-    <BaseLayout
-      userType={UserType.BUSINESS_ADMIN}
-      userName={userName}
-      onLogout={onLogout}
-    >
-      {/* Business Info Banner */}
-      <div className="mb-6 bg-white rounded-lg shadow p-4">
-        <div className="flex items-center justify-between">
-          <div>
-            <h2 className="text-lg font-semibold text-gray-800">{user.businessName}</h2>
-            <p className="text-sm text-gray-500">Business Admin Dashboard</p>
-          </div>
-          {user.canManageUsers && (
-            <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
-              User Management Enabled
-            </span>
-          )}
-        </div>
-      </div>
-
-      {/* Business Quick Actions */}
-      <div className="mb-6 bg-white rounded-lg shadow p-4">
-        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
-        <div className="grid grid-cols-3 gap-4">
-          {user.canManageUsers && (
-            <button className="p-3 bg-blue-50 hover:bg-blue-100 rounded-md text-blue-700 text-sm font-medium">
-              Manage Users
-            </button>
-          )}
-          <button className="p-3 bg-green-50 hover:bg-green-100 rounded-md text-green-700 text-sm font-medium">
-            View Analytics
-          </button>
-          <button className="p-3 bg-purple-50 hover:bg-purple-100 rounded-md text-purple-700 text-sm font-medium">
-            Business Settings
-          </button>
-        </div>
-      </div>
-
-      {/* Main content */}
-      <div className="bg-white rounded-lg shadow">
-        {children}
-      </div>
-    </BaseLayout>
-  );
-};
-
-export default BusinessAdminLayout; 
\ No newline at end of file
diff --git a/frontend/src/layouts/BusinessUserLayout.tsx b/frontend/src/layouts/BusinessUserLayout.tsx
deleted file mode 100644
index 5411320..0000000
--- a/frontend/src/layouts/BusinessUserLayout.tsx
+++ /dev/null
@@ -1,72 +0,0 @@
-import React from 'react';
-import { UserType } from '../types/user';
-import BaseLayout from './BaseLayout';
-import type { BusinessUser } from '../types/user';
-
-interface BusinessUserLayoutProps {
-  user: BusinessUser;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const BusinessUserLayout: React.FC<BusinessUserLayoutProps> = ({
-  user,
-  onLogout,
-  children,
-}) => {
-  const userName = `${user.firstName} ${user.lastName}`;
-
-  return (
-    <BaseLayout
-      userType={UserType.BUSINESS_USER}
-      userName={userName}
-      onLogout={onLogout}
-    >
-      {/* Welcome Banner */}
-      <div className="mb-6 bg-white rounded-lg shadow p-4">
-        <div className="flex items-center justify-between">
-          <div>
-            <h2 className="text-lg font-semibold text-gray-800">Welcome, {user.firstName}!</h2>
-            <p className="text-sm text-gray-500">{user.businessName}</p>
-          </div>
-        </div>
-      </div>
-
-      {/* Quick Access Cards */}
-      <div className="grid grid-cols-2 gap-6 mb-6">
-        {/* Tasks Overview */}
-        <div className="bg-white rounded-lg shadow p-4">
-          <h3 className="text-md font-semibold text-gray-800 mb-3">My Tasks</h3>
-          <div className="space-y-2">
-            <button className="w-full p-3 bg-blue-50 hover:bg-blue-100 rounded-md text-blue-700 text-sm font-medium text-left">
-              View Active Tasks
-            </button>
-            <button className="w-full p-3 bg-green-50 hover:bg-green-100 rounded-md text-green-700 text-sm font-medium text-left">
-              Create New Task
-            </button>
-          </div>
-        </div>
-
-        {/* Reports */}
-        <div className="bg-white rounded-lg shadow p-4">
-          <h3 className="text-md font-semibold text-gray-800 mb-3">Reports</h3>
-          <div className="space-y-2">
-            <button className="w-full p-3 bg-purple-50 hover:bg-purple-100 rounded-md text-purple-700 text-sm font-medium text-left">
-              View Reports
-            </button>
-            <button className="w-full p-3 bg-indigo-50 hover:bg-indigo-100 rounded-md text-indigo-700 text-sm font-medium text-left">
-              Download Reports
-            </button>
-          </div>
-        </div>
-      </div>
-
-      {/* Main content */}
-      <div className="bg-white rounded-lg shadow">
-        {children}
-      </div>
-    </BaseLayout>
-  );
-};
-
-export default BusinessUserLayout; 
\ No newline at end of file
diff --git a/frontend/src/layouts/DemoLayout.tsx b/frontend/src/layouts/DemoLayout.tsx
deleted file mode 100644
index 5494921..0000000
--- a/frontend/src/layouts/DemoLayout.tsx
+++ /dev/null
@@ -1,119 +0,0 @@
-import React from 'react';
-import { UserType } from '../types/user';
-import BaseLayout from './BaseLayout';
-import type { DemoUser } from '../types/user';
-
-interface DemoLayoutProps {
-  user: DemoUser;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const DemoLayout: React.FC<DemoLayoutProps> = ({
-  user,
-  onLogout,
-  children,
-}) => {
-  const userName = `${user.firstName} ${user.lastName}`;
-  const daysLeft = Math.ceil(
-    (new Date(user.expiryDate).getTime() - new Date().getTime()) / (1000 * 3600 * 24)
-  );
-
-  return (
-    <BaseLayout
-      userType={UserType.DEMO_USER}
-      userName={userName}
-      onLogout={onLogout}
-    >
-      {/* Demo Welcome Banner */}
-      <div className="mb-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow p-6 text-white">
-        <h2 className="text-2xl font-bold mb-2">Welcome to AIDA Demo</h2>
-        <p className="text-blue-100 mb-4">
-          Explore our features and discover how AIDA can transform your business
-        </p>
-        <div className="flex items-center space-x-4">
-          <button className="px-4 py-2 bg-white text-blue-600 rounded-md font-medium hover:bg-blue-50">
-            Schedule a Call
-          </button>
-          <button className="px-4 py-2 bg-blue-400 text-white rounded-md font-medium hover:bg-blue-300">
-            View Pricing
-          </button>
-        </div>
-      </div>
-
-      {/* Feature Showcase */}
-      <div className="grid grid-cols-3 gap-6 mb-6">
-        {/* Key Features */}
-        <div className="bg-white rounded-lg shadow p-4">
-          <h3 className="text-lg font-semibold text-gray-800 mb-3">Key Features</h3>
-          <div className="space-y-2">
-            <button className="w-full p-3 bg-green-50 hover:bg-green-100 rounded-md text-green-700 text-sm font-medium text-left">
-              Business Analytics Demo
-            </button>
-            <button className="w-full p-3 bg-blue-50 hover:bg-blue-100 rounded-md text-blue-700 text-sm font-medium text-left">
-              User Management Demo
-            </button>
-            <button className="w-full p-3 bg-purple-50 hover:bg-purple-100 rounded-md text-purple-700 text-sm font-medium text-left">
-              Reporting Tools Demo
-            </button>
-          </div>
-        </div>
-
-        {/* Interactive Tour */}
-        <div className="bg-white rounded-lg shadow p-4">
-          <h3 className="text-lg font-semibold text-gray-800 mb-3">Interactive Tour</h3>
-          <div className="space-y-2">
-            <button className="w-full p-3 bg-yellow-50 hover:bg-yellow-100 rounded-md text-yellow-700 text-sm font-medium text-left">
-              Start Guided Tour
-            </button>
-            <button className="w-full p-3 bg-orange-50 hover:bg-orange-100 rounded-md text-orange-700 text-sm font-medium text-left">
-              Watch Tutorial Videos
-            </button>
-          </div>
-        </div>
-
-        {/* Trial Status */}
-        <div className="bg-white rounded-lg shadow p-4">
-          <h3 className="text-lg font-semibold text-gray-800 mb-3">Trial Status</h3>
-          <div className="space-y-4">
-            <div className="p-3 bg-blue-50 rounded-md">
-              <p className="text-sm text-blue-700 font-medium">
-                {daysLeft} days remaining in trial
-              </p>
-              <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
-                <div
-                  className="bg-blue-600 rounded-full h-2"
-                  style={{ width: `${Math.min((30 - daysLeft) / 30 * 100, 100)}%` }}
-                />
-              </div>
-            </div>
-            <button className="w-full p-3 bg-indigo-50 hover:bg-indigo-100 rounded-md text-indigo-700 text-sm font-medium">
-              Upgrade to Full Version
-            </button>
-          </div>
-        </div>
-      </div>
-
-      {/* Main content */}
-      <div className="bg-white rounded-lg shadow">
-        {children}
-      </div>
-
-      {/* Contact Sales Footer */}
-      <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
-        <div className="flex items-center justify-between">
-          <div>
-            <p className="text-sm text-gray-600">
-              Questions about AIDA? Your dedicated sales representative is {user.createdBy}
-            </p>
-          </div>
-          <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
-            Contact Sales
-          </button>
-        </div>
-      </div>
-    </BaseLayout>
-  );
-};
-
-export default DemoLayout; 
\ No newline at end of file
diff --git a/frontend/src/layouts/LayoutSelector.tsx b/frontend/src/layouts/LayoutSelector.tsx
deleted file mode 100644
index b9d5e92..0000000
--- a/frontend/src/layouts/LayoutSelector.tsx
+++ /dev/null
@@ -1,98 +0,0 @@
-import React from 'react';
-import {
-  UserType,
-  BaseUser,
-  AidaAdmin,
-  BusinessAdmin,
-  BusinessUser,
-  DemoUser,
-  isAidaAdmin,
-  isBusinessAdmin,
-  isBusinessUser,
-  isDemoUser,
-} from '../types/user';
-
-import AidaAdminLayout from './AidaAdminLayout';
-import BusinessAdminLayout from './BusinessAdminLayout';
-import BusinessUserLayout from './BusinessUserLayout';
-import DemoLayout from './DemoLayout';
-
-interface LayoutSelectorProps {
-  user: BaseUser;
-  onLogout: () => void;
-  children: React.ReactNode;
-}
-
-export const LayoutSelector: React.FC<LayoutSelectorProps> = ({
-  user,
-  onLogout,
-  children,
-}) => {
-  // Use type guards to ensure type safety
-  if (isAidaAdmin(user)) {
-    return (
-      <AidaAdminLayout
-        user={user}
-        onLogout={onLogout}
-      >
-        {children}
-      </AidaAdminLayout>
-    );
-  }
-
-  if (isBusinessAdmin(user)) {
-    return (
-      <BusinessAdminLayout
-        user={user}
-        onLogout={onLogout}
-      >
-        {children}
-      </BusinessAdminLayout>
-    );
-  }
-
-  if (isBusinessUser(user)) {
-    return (
-      <BusinessUserLayout
-        user={user}
-        onLogout={onLogout}
-      >
-        {children}
-      </BusinessUserLayout>
-    );
-  }
-
-  if (isDemoUser(user)) {
-    return (
-      <DemoLayout
-        user={user}
-        onLogout={onLogout}
-      >
-        {children}
-      </DemoLayout>
-    );
-  }
-
-  // Handle invalid user type
-  console.error('Invalid user type:', user.userType);
-  return (
-    <div className="min-h-screen flex items-center justify-center bg-gray-50">
-      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow">
-        <div className="text-center">
-          <h2 className="text-xl font-semibold text-red-600">Invalid User Type</h2>
-          <p className="mt-2 text-gray-600">
-            There was an error with your account type. Please contact support.
-          </p>
-          <button
-            onClick={onLogout}
-            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
-          >
-            Return to Login
-          </button>
-        </div>
-      </div>
-    </div>
-  );
-};
-
-export default LayoutSelector; 
\ No newline at end of file
