# Simple Vision Tool Integration (Without MCP)

## Key Insight

You don't need MCP at all! Your backend already has:
- `BaseTool` abstraction for tools
- LLM that can orchestrate tool usage
- Agent framework that executes tools

You just need to create tools that call the aida-llm services. Much simpler!

## Architecture

```
User → WebSocket → Agent/LLM → VisionTool → HTTP API → aida-llm services
                            ↓
                     Other tools (document, etc.)
```

## Option 1: Direct Integration (Simplest)

Just import and use aida-llm components directly in your backend:

```python
# backend/main/agents/tools/vision.py

from backend.main.agents.tools.base import BaseTool
from aida_llm.backend.main.service.agents import OCRAgent, ProductAgent
from aida_llm.backend.main.service.tools.vision_ocr_tools import encode_image

class ExtractTextFromImageTool(BaseTool):
    """Direct integration - uses aida-llm components in-process"""
    
    def __init__(self):
        self.ocr_agent = OCRAgent()
    
    @property
    def name(self) -> str:
        return "extract_text_from_image"
    
    @property
    def description(self) -> str:
        return "Extract text from an image using advanced OCR"
    
    async def run(self, image_path: str) -> str:
        """
        Extract text from image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted text
        """
        # Use aida-llm's OCR directly
        result = await self.ocr_agent.extract_with_retry(
            image_path,
            extraction_type="text"
        )
        
        if result.success:
            return result.extracted_text
        else:
            return f"Failed to extract text: {result.error_message}"


class AnalyzeLaptopImageTool(BaseTool):
    """Comprehensive laptop analysis"""
    
    def __init__(self):
        from aida_llm.backend.main.service.agents import SupervisorAgent
        self.supervisor = SupervisorAgent()
    
    @property
    def name(self) -> str:
        return "analyze_laptop_image"
    
    @property
    def description(self) -> str:
        return "Analyze a laptop image to extract model, warranty, and find products"
    
    async def run(self, image_path: str) -> str:
        """
        Analyze laptop image.
        
        Args:
            image_path: Path to laptop image
            
        Returns:
            JSON with laptop info, warranty, and product matches
        """
        result = await self.supervisor.identify_laptop(image_path)
        
        return json.dumps({
            "laptop_info": result.laptop_info.dict() if result.laptop_info else None,
            "warranty_info": result.warranty_info.dict() if result.warranty_info else None,
            "products": result.products[:3],
            "success": result.success
        }, indent=2)
```

## Option 2: HTTP API Integration (Microservice)

If you want separation, expose aida-llm as a simple HTTP API:

### A. Simple FastAPI Service for aida-llm

```python
# aida-llm/api_service.py

from fastapi import FastAPI, UploadFile, File
from backend.main.service.agents import SupervisorAgent, OCRAgent
import tempfile
import os

app = FastAPI()

ocr_agent = OCRAgent()
supervisor = SupervisorAgent()

@app.post("/ocr/extract_text")
async def extract_text(file: UploadFile = File(...)):
    """Extract text from uploaded image"""
    # Save temp file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as tmp:
        content = await file.read()
        tmp.write(content)
        tmp_path = tmp.name
    
    try:
        result = await ocr_agent.extract_with_retry(tmp_path, "text")
        return {
            "success": result.success,
            "text": result.extracted_text,
            "confidence": result.confidence
        }
    finally:
        os.unlink(tmp_path)

@app.post("/analyze/laptop")
async def analyze_laptop(file: UploadFile = File(...)):
    """Complete laptop analysis"""
    with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as tmp:
        content = await file.read()
        tmp.write(content)
        tmp_path = tmp.name
    
    try:
        result = await supervisor.identify_laptop(tmp_path)
        return {
            "success": result.success,
            "laptop_info": result.laptop_info.dict() if result.laptop_info else None,
            "warranty_info": result.warranty_info.dict() if result.warranty_info else None,
            "products": result.products
        }
    finally:
        os.unlink(tmp_path)

@app.get("/health")
async def health():
    return {"status": "healthy"}
```

### B. Tool that calls the HTTP API

```python
# backend/main/agents/tools/vision_api.py

import aiohttp
import base64
from backend.main.agents.tools.base import BaseTool

class VisionAPITool(BaseTool):
    """Tool that calls aida-llm HTTP API"""
    
    def __init__(self, api_url: str = "http://localhost:8001"):
        self.api_url = api_url
    
    @property
    def name(self) -> str:
        return "analyze_laptop_image"
    
    @property
    def description(self) -> str:
        return "Analyze laptop image for model, warranty, and product info"
    
    async def run(self, image_path: str) -> str:
        """Call aida-llm API"""
        async with aiohttp.ClientSession() as session:
            with open(image_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='image.jpg')
                
                async with session.post(
                    f"{self.api_url}/analyze/laptop",
                    data=data
                ) as response:
                    result = await response.json()
                    return json.dumps(result, indent=2)
```

## Option 3: Shared Database + Message Queue (Production)

For production, use shared PostgreSQL and a message queue:

```python
# backend/main/agents/tools/vision_async.py

from backend.main.agents.tools.base import BaseTool
import asyncio
from celery import Celery

celery_app = Celery('tasks', broker='redis://localhost:6379')

class AsyncVisionTool(BaseTool):
    """Tool that queues vision tasks for background processing"""
    
    @property
    def name(self) -> str:
        return "analyze_image_async"
    
    @property
    def description(self) -> str:
        return "Queue image for vision analysis (async)"
    
    async def run(self, image_path: str, callback_url: str = None) -> str:
        """
        Queue image for processing.
        
        Args:
            image_path: Path to image
            callback_url: Optional webhook for results
            
        Returns:
            Task ID for checking status
        """
        # Queue task
        task = celery_app.send_task(
            'aida_llm.analyze_laptop',
            args=[image_path, callback_url]
        )
        
        return json.dumps({
            "task_id": task.id,
            "status": "queued",
            "check_url": f"/api/tasks/{task.id}"
        })
```

## Integration into Existing Backend

Simply register the tools in your agent initialization:

```python
# backend/main/consumers.py

async def _initialize_agent(self):
    """Initialize agent with vision tools"""
    
    # ... existing setup ...
    
    # Register vision tools
    from .agents.tools.vision import (
        ExtractTextFromImageTool,
        AnalyzeLaptopImageTool
    )
    
    tools.register(ExtractTextFromImageTool(), category="vision")
    tools.register(AnalyzeLaptopImageTool(), category="vision")
    
    # That's it! The agent now has vision capabilities
```

## Comparison of Options

| Approach | Pros | Cons | Best For |
|----------|------|------|----------|
| **Direct Import** | - Simplest<br>- No network calls<br>- Fast | - Tight coupling<br>- Same process resources | Development, small deployments |
| **HTTP API** | - Clean separation<br>- Independent scaling<br>- Language agnostic | - Network overhead<br>- More complexity | Microservices architecture |
| **Message Queue** | - Async processing<br>- Handles long tasks<br>- Scalable | - Most complex<br>- Eventual consistency | Production with heavy loads |

## Recommended Approach

**Start with Option 1 (Direct Import)** because:
1. Simplest to implement
2. No additional services needed
3. Can migrate to HTTP API later if needed
4. Your LLM already orchestrates everything

Just:
1. Install aida-llm dependencies in your backend
2. Create tool classes that wrap aida-llm components
3. Register tools with your ToolRegistry
4. Done!

## Example: Complete Integration

```python
# backend/main/agents/tools/vision_tools.py

from typing import Optional, Dict, Any
import json
import sys
import os

# Add aida-llm to path
sys.path.append('/path/to/aida-llm/backend')

from main.service.agents import SupervisorAgent, OCRAgent
from main.service.tools.vision_ocr_tools import encode_image
from backend.main.agents.tools.base import BaseTool

class VisionOCRTool(BaseTool):
    """Swiss-army knife for image analysis"""
    
    def __init__(self):
        self.supervisor = SupervisorAgent()
        self.ocr = OCRAgent()
    
    @property
    def name(self) -> str:
        return "analyze_image"
    
    @property  
    def description(self) -> str:
        return (
            "Analyze any image: extract text, identify products, "
            "read warranty labels, recognize laptop models. "
            "Returns comprehensive analysis results."
        )
    
    async def run(self, 
                  image_path: str,
                  analysis_type: str = "auto") -> str:
        """
        Analyze image based on type.
        
        Args:
            image_path: Path to image file
            analysis_type: 'auto', 'text', 'laptop', 'warranty'
            
        Returns:
            JSON with analysis results
        """
        if analysis_type == "text":
            result = await self.ocr.extract_with_retry(image_path, "text")
            return json.dumps({
                "type": "text",
                "text": result.extracted_text,
                "success": result.success
            })
            
        elif analysis_type == "laptop" or analysis_type == "auto":
            result = await self.supervisor.identify_laptop(image_path)
            return json.dumps({
                "type": "laptop_analysis", 
                "laptop": result.laptop_info.dict() if result.laptop_info else None,
                "warranty": result.warranty_info.dict() if result.warranty_info else None,
                "products": result.products,
                "success": result.success
            })
        
        return json.dumps({"error": "Unknown analysis type"})

# In your consumer, just register it:
tools.register(VisionOCRTool(), category="vision")

# The LLM can now use it naturally:
# "I see you uploaded an image. Let me analyze it for you..."
# *uses analyze_image tool*
# "This appears to be a Dell Latitude 7420 with warranty until..."
```

## Summary

You were right - you don't need MCP! Just create `BaseTool` implementations that:
1. Import and use aida-llm components directly, OR
2. Call a simple HTTP API, OR  
3. Queue tasks for async processing

The LLM agent will orchestrate everything naturally. Much simpler than MCP!