from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import json
import asyncio
from typing import Dict, Any
import openai
import os
from dotenv import load_dotenv
import base64
import io

load_dotenv()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:5174", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

SYSTEM_PROMPT = """You are a multimodal assistant. Always reply using this XML-like format:

<text>Text to be shown in UI only</text>
<speak>Text to be spoken only</speak>
<both>Text that should be both spoken and shown</both>

- Use <text> for lists, data, and technical details.
- Use <speak> for short feedback not needed in UI.
- Use <both> for key summary lines.

Respond using these tags only."""

class ConversationManager:
    def __init__(self, websocket: WebSocket):
        self.websocket = websocket
        self.audio_buffer = io.BytesIO()
        self.is_processing = False
        self.should_stop = False
        
    async def handle_audio_chunk(self, audio_data: bytes):
        """Buffer incoming audio chunks"""
        self.audio_buffer.write(audio_data)
        
    async def transcribe_audio(self) -> str:
        """Transcribe buffered audio using Whisper"""
        try:
            audio_data = self.audio_buffer.getvalue()
            if not audio_data:
                return ""
            
            print(f"Audio buffer size: {len(audio_data)} bytes")
                
            # Save audio temporarily with appropriate extension
            temp_file = "temp_audio.webm"
            with open(temp_file, "wb") as f:
                f.write(audio_data)
            
            # Transcribe with Whisper
            with open(temp_file, "rb") as audio_file:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text"
                )
            
            # Clean up
            os.remove(temp_file)
            self.audio_buffer = io.BytesIO()
            
            print(f"Transcript: {transcript}")
            return transcript
        except Exception as e:
            print(f"Transcription error: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    async def get_llm_response(self, user_input: str) -> str:
        """Get response from LLM"""
        try:
            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": user_input}
                ],
                stream=True
            )
            
            full_response = ""
            for chunk in response:
                if self.should_stop:
                    break
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    # Stream text chunks
                    await self.websocket.send_json({
                        "type": "text_chunk",
                        "content": content
                    })
            
            return full_response
        except Exception as e:
            print(f"LLM error: {e}")
            return "<both>I'm sorry, I encountered an error processing your request.</both>"
    
    def parse_llm_response(self, response: str) -> Dict[str, str]:
        """Parse XML-like tags from LLM response"""
        import re
        
        result = {
            "text": [],
            "speak": [],
            "both": []
        }
        
        # Extract content from tags
        text_matches = re.findall(r'<text>(.*?)</text>', response, re.DOTALL)
        speak_matches = re.findall(r'<speak>(.*?)</speak>', response, re.DOTALL)
        both_matches = re.findall(r'<both>(.*?)</both>', response, re.DOTALL)
        
        result["text"] = text_matches
        result["speak"] = speak_matches
        result["both"] = both_matches
        
        return result
    
    async def generate_tts(self, text: str) -> bytes:
        """Generate TTS audio"""
        try:
            response = client.audio.speech.create(
                model="tts-1",
                voice="alloy",
                input=text
            )
            
            return response.content
        except Exception as e:
            print(f"TTS error: {e}")
            return b""
    
    async def process_conversation(self):
        """Main conversation processing loop"""
        self.is_processing = True
        
        # Transcribe audio
        transcript = await self.transcribe_audio()
        if not transcript:
            await self.websocket.send_json({
                "type": "error",
                "message": "No speech detected"
            })
            self.is_processing = False
            return
        
        # Send transcript
        await self.websocket.send_json({
            "type": "transcript",
            "content": transcript
        })
        
        # Get LLM response
        llm_response = await self.get_llm_response(transcript)
        
        # Parse response
        parsed = self.parse_llm_response(llm_response)
        
        # Send parsed response
        await self.websocket.send_json({
            "type": "response",
            "content": parsed
        })
        
        # Generate TTS for speak and both content
        tts_content = " ".join(parsed["speak"] + parsed["both"])
        if tts_content and not self.should_stop:
            audio_data = await self.generate_tts(tts_content)
            if audio_data:
                # Send audio as base64
                audio_base64 = base64.b64encode(audio_data).decode()
                await self.websocket.send_json({
                    "type": "audio",
                    "content": audio_base64
                })
        
        await self.websocket.send_json({
            "type": "done"
        })
        
        self.is_processing = False

@app.websocket("/api/conversation")
async def websocket_endpoint(websocket: WebSocket):
    print(f"WebSocket connection attempt from {websocket.client}")
    await websocket.accept()
    print("WebSocket connection accepted")
    manager = ConversationManager(websocket)
    
    try:
        while True:
            data = await websocket.receive_json()
            
            if data["action"] == "STOP":
                manager.should_stop = True
                await websocket.send_json({
                    "type": "stopped"
                })
            
            elif data["action"] == "audio_chunk":
                # Decode base64 audio
                audio_bytes = base64.b64decode(data["content"])
                await manager.handle_audio_chunk(audio_bytes)
            
            elif data["action"] == "process":
                manager.should_stop = False
                asyncio.create_task(manager.process_conversation())
                
    except WebSocketDisconnect:
        print("Client disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close()

@app.get("/")
async def root():
    return {"message": "Multimodal AI Server is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)