**Title: Design Document for a Multimodal LLM Interface with Audio Input and Mixed Audio/Text Output**

---

## Overview

This system enables fluent, natural communication between a user and an AI assistant using **multimodal capabilities**, including voice input, text display, and voice output. The user speaks via microphone, and the AI responds with a combination of spoken and visual feedback. The architecture uses WebRTC or WebSocket for streaming audio and control, OpenAI Whisper for speech-to-text, an LLM for generating structured responses, and TTS for audio output.

The LLM responds using a structured tag-based format:

```xml
<text>...</text>
<speak>...</speak>
<both>...</both>
```

The backend parses this format and routes content accordingly for rendering and playback.

---

## System Architecture

### Components:

1. **Frontend Web Application (React/Next.js)**
2. **Backend Server (FastAPI + WebSocket + WebRTC)**
3. **LLM Provider (e.g., OpenAI GPT-4)**
4. **Speech-to-Text Engine (OpenAI Whisper)**
5. **Text-to-Speech Engine (OpenAI TTS or ElevenLabs)**

---

## Frontend Design

### Features:

- Voice recording with WebRTC or WebSocket
- Real-time audio streaming to backend
- Displaying structured LLM response
- Playing streamed TTS audio
- Interrupt support (e.g., stop button or voice command)

### Components:

1. **MicRecorder.tsx**

   - Audio capture
   - WebRTC or WebSocket stream to backend
   - Push-to-talk or VAD (voice activity detection)

2. **ChatWindow\.tsx**

   - Renders `<text>` and `<both>` outputs
   - Controls and visual indicators for playback

3. **AudioPlayer.tsx**

   - Streams and plays TTS response
   - Supports interrupt/pause

4. **WebSocketManager.tsx**

   - Manages persistent WebSocket connection
   - Sends STT, receives LLM output, handles STOP signals

---

## Backend Design

### Framework: Python + FastAPI + WebSocket + asyncio

### Endpoints:

- `WebSocket /api/conversation`

  - Receives audio stream chunks
  - Transcribes with Whisper
  - Sends text to LLM with system prompt
  - Streams response using tag-based format
  - Routes `<speak>`/`<both>` to TTS
  - Listens for interrupt (`STOP`) commands

- `GET /tts-audio/:id` (optional)

  - Serve cached/generated TTS audio files

### LLM Prompt Template:

```text
You are a multimodal assistant. Always reply using this XML-like format:

<text>Text to be shown in UI only</text>
<speak>Text to be spoken only</speak>
<both>Text that should be both spoken and shown</both>

- Use <text> for lists, data, and technical details.
- Use <speak> for short feedback not needed in UI.
- Use <both> for key summary lines.

Respond using these tags only.
```

---

## Transcription Layer

- Uses OpenAI Whisper (API or local)
- Receives real-time stream or batched audio
- Sends plain text to LLM

---

## TTS Layer

- Receives `<speak>` and `<both>` strings
- Converts to speech using OpenAI TTS or ElevenLabs
- Streams result to frontend over WebSocket

---

## WebSocket Command Support

- **From frontend to backend**:
  - `{"action": "STOP"}` → aborts current LLM response and/or TTS stream
  - `{"audio_chunk": ...}` → streamed mic input
- **From backend to frontend**:
  - `{"text": "..."}`
  - `{"tts_stream": "..."}`
  - `{"done": true}`

---

## Optional Enhancements

- Retry or recover from malformed LLM output
- Real-time VAD control for mic
- Logging and transcript review tools
- Multilingual switching support

---

## Deployment Stack

- **Frontend**: Vite + React + TailwindCSS + Web Audio API
- **Backend**: FastAPI + Python 3.10+ + WebSocket
- **Deployment**: Docker + Nginx, or Vercel + Render combo

---

## Security & Privacy

- Encrypted WebRTC/WebSocket
- Audio not stored unless explicitly enabled
- TTS and LLM APIs managed securely with throttling

---

## Example Interaction

**User (speaks):** "I want to buy a flight to NYC."

**LLM Response:**

```xml
<both>Sure! Here are some flight options.</both>
<text>
Option 1: Delta, 8 AM, $300\nOption 2: United, 9:30 AM, $280\nOption 3: JetBlue, 11 AM, $310
</text>
```

**Frontend behavior:**

- Immediately plays "Sure! Here are some flight options."
- Renders the list below
- If user says "Stop", backend halts further playback

---

## Summary

This design enables seamless multimodal communication between a human and an AI assistant, mixing voice and text input/output in real-time. It supports dynamic interruptions and smooth UX via WebSocket, making it suitable for voice-first AI products and conversational agents.

The architecture and format are ready for direct use in AI coding assistants.

