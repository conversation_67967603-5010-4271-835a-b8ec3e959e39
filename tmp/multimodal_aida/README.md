# Multimodal AI Assistant

A real-time voice-enabled AI assistant with multimodal input/output capabilities. Users can speak naturally via microphone, and the AI responds with both text display and voice output.

## Features

- **Voice Input**: Real-time audio streaming and transcription using OpenAI Whisper
- **Multimodal Output**: AI responses are structured with XML-like tags for different output modes
- **Text-to-Speech**: Spoken responses using OpenAI TTS
- **WebSocket Communication**: Real-time bidirectional communication
- **Interrupt Support**: Stop ongoing responses with a button click

## Architecture

- **Frontend**: React + Vite + TypeScript + Tailwind CSS
- **Backend**: FastAPI + WebSocket + Python
- **AI Services**: OpenAI GPT-4 (LLM), Whisper (STT), TTS

## Setup

### Backend

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a virtual environment and activate it:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

5. Run the backend server:
   ```bash
   python main.py
   ```

### Frontend

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open http://localhost:5173 in your browser

## Usage

1. Click the microphone button to start recording
2. Speak your request naturally
3. Click again to stop recording
4. The AI will process your request and respond with:
   - Text displayed in the chat window
   - Voice output for spoken content
5. Use the "Stop" button to interrupt ongoing responses

## Response Format

The AI uses XML-like tags to structure responses:

- `<text>`: Content only displayed in the UI
- `<speak>`: Content only spoken via TTS
- `<both>`: Content both displayed and spoken

## Development

- Backend runs on http://localhost:8000
- Frontend runs on http://localhost:5173
- WebSocket endpoint: ws://localhost:8000/api/conversation