import React, { useState, useRef, useEffect } from 'react';
import { WebSocketManager } from '../utils/WebSocketManager';

interface MicRecorderProps {
  wsManager: WebSocketManager;
  onRecordingComplete: () => void;
  onRecordingStart: () => void;
}

export const MicRecorder: React.FC<MicRecorderProps> = ({ wsManager, onRecordingComplete, onRecordingStart }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const voiceDetectedRef = useRef(false);
  const isRecordingRef = useRef(false);

  useEffect(() => {
    checkMicrophonePermission();
  }, []);

  const checkMicrophonePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      setPermissionGranted(true);
    } catch (error) {
      console.error('Microphone permission denied:', error);
      setPermissionGranted(false);
    }
  };

  const detectVoiceActivity = () => {
    if (!analyserRef.current || !streamRef.current) return;
    
    const analyser = analyserRef.current;
    const bufferLength = analyser.fftSize;
    const dataArray = new Float32Array(bufferLength);
    let isAnalyzing = true;
    
    const checkAudio = () => {
      if (!isAnalyzing || !streamRef.current) return;
      
      analyser.getFloatTimeDomainData(dataArray);
      
      // Calculate RMS (Root Mean Square) for better voice detection
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i] * dataArray[i];
      }
      const rms = Math.sqrt(sum / bufferLength);
      const db = 20 * Math.log10(rms);
      
      // Threshold in decibels (more intuitive)
      const threshold = -35; // Adjust this value: lower = more sensitive
      
      // Debug logging - remove after testing
      // Only log when recording or when voice might be detected
      if (db > -40 || isRecordingRef.current) {
        console.log('Audio level (dB):', db.toFixed(2), 'Recording:', isRecordingRef.current);
      }
      
      if (db > threshold && !isRecordingRef.current && !voiceDetectedRef.current) {
        // Voice detected, start recording
        console.log('Voice detected at', db.toFixed(2), 'dB, starting recording');
        voiceDetectedRef.current = true;
        startActualRecording();
      } else if (db <= threshold && isRecordingRef.current) {
        // Silence detected while recording
        if (!silenceTimeoutRef.current) {
          console.log('Silence detected, starting timeout');
          silenceTimeoutRef.current = setTimeout(() => {
            // Stop recording after 1.5 seconds of silence
            console.log('Silence timeout reached, stopping recording');
            stopRecording();
            voiceDetectedRef.current = false;
            silenceTimeoutRef.current = null;
          }, 1500);
        }
      } else if (db > threshold && isRecordingRef.current && silenceTimeoutRef.current) {
        // Voice detected again, cancel silence timeout
        console.log('Voice detected again, canceling silence timeout');
        clearTimeout(silenceTimeoutRef.current);
        silenceTimeoutRef.current = null;
      }
      
      requestAnimationFrame(checkAudio);
    };
    
    checkAudio();
    
    return () => {
      isAnalyzing = false;
    };
  };

  const startListening = async () => {
    if (!permissionGranted) {
      await checkMicrophonePermission();
      if (!permissionGranted) return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        } 
      });

      streamRef.current = stream;
      
      // Set up audio analysis for voice detection
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
      analyserRef.current.smoothingTimeConstant = 0.3;
      
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      setIsListening(true);
      
      // Small delay to ensure everything is set up
      setTimeout(() => {
        detectVoiceActivity();
      }, 100);
    } catch (error) {
      console.error('Error starting listening:', error);
    }
  };

  const startActualRecording = () => {
    if (!streamRef.current || isRecordingRef.current) return;
    
    console.log('Starting actual recording');
    onRecordingStart(); // Stop any playing audio
    
    const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
      ? 'audio/webm;codecs=opus' 
      : 'audio/webm';
    
    const mediaRecorder = new MediaRecorder(streamRef.current, {
      mimeType: mimeType
    });

    audioChunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data);
      }
    };

    mediaRecorder.onstop = async () => {
      console.log('Recording stopped, chunks:', audioChunksRef.current.length);
      isRecordingRef.current = false;
      
      if (audioChunksRef.current.length === 0) {
        console.log('No audio data recorded');
        return;
      }
      
      // Combine all chunks into a single blob
      const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
      
      if (audioBlob.size === 0) {
        console.log('Audio blob is empty');
        return;
      }
      
      const arrayBuffer = await audioBlob.arrayBuffer();
      
      // Send the complete audio to server
      wsManager.sendAudioChunk(arrayBuffer);
      wsManager.startProcessing();
      onRecordingComplete();
    };

    mediaRecorderRef.current = mediaRecorder;
    mediaRecorder.start(100); // Collect data every 100ms
    isRecordingRef.current = true;
    setIsRecording(true);
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      console.log('Stopping recording');
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      voiceDetectedRef.current = false;
    }
  };

  const stopListening = () => {
    setIsListening(false);
    setIsRecording(false);
    isRecordingRef.current = false;
    voiceDetectedRef.current = false;
    
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
    
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  };

  const handleToggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopListening();
    };
  }, []);

  return (
    <div className="flex flex-col items-center space-y-4">
      <button
        onClick={handleToggleListening}
        className={`w-24 h-24 rounded-full transition-all duration-300 flex items-center justify-center shadow-lg ${
          isRecording 
            ? 'bg-gradient-to-r from-red-500 to-pink-500 animate-pulse' 
            : isListening
            ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
            : 'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600'
        }`}
        disabled={!wsManager.isConnected}
      >
        {isRecording ? (
          <div className="relative">
            {/* Stop icon - colorful square */}
            <div className="w-8 h-8 bg-gradient-to-br from-pink-400 to-red-500 rounded-lg shadow-lg transform rotate-12 animate-pulse"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
          </div>
        ) : isListening ? (
          <div className="relative">
            {/* Listening microphone with animated waves */}
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="micGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#10b981" />
                  <stop offset="100%" stopColor="#34d399" />
                </linearGradient>
              </defs>
              <path d="M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2S9 3.34 9 5V11C9 12.66 10.34 14 12 14Z" fill="url(#micGradient)"/>
              <path d="M19 11C19 15.4183 15.4183 19 11 19M5 11C5 15.4183 8.58172 19 13 19M13 19V23M9 23H17" stroke="url(#micGradient)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            {/* Animated sound waves */}
            <div className="absolute -right-2 top-1/2 -translate-y-1/2">
              <div className="flex space-x-1">
                <div className="w-1 h-3 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-1 h-5 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-1 h-4 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        ) : (
          /* Start microphone - colorful gradient */
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="startGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" />
                <stop offset="50%" stopColor="#6366f1" />
                <stop offset="100%" stopColor="#ec4899" />
              </linearGradient>
            </defs>
            <path d="M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2S9 3.34 9 5V11C9 12.66 10.34 14 12 14Z" fill="url(#startGradient)"/>
            <path d="M19 11C19 15.4183 15.4183 19 11 19M5 11C5 15.4183 8.58172 19 13 19M13 19V23M9 23H17" stroke="url(#startGradient)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <circle cx="12" cy="8" r="1.5" fill="white" opacity="0.8"/>
          </svg>
        )}
      </button>
      
      <div className="text-center">
        <p className="text-sm font-medium text-gray-700">
          {isRecording ? 'Speaking...' : isListening ? 'Listening for your voice' : 'Click to activate voice mode'}
        </p>
        <p className="text-xs text-gray-500 mt-1">
          {isListening ? 'Start speaking anytime' : 'Voice activation mode'}
        </p>
        {!permissionGranted && (
          <p className="text-xs text-red-500 mt-1">
            Microphone permission required
          </p>
        )}
        {!wsManager.isConnected && (
          <p className="text-xs text-yellow-500 mt-1">
            Connecting to server...
          </p>
        )}
      </div>
    </div>
  );
};