import React, { useEffect, useRef } from 'react';

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatWindowProps {
  messages: Message[];
}

export const ChatWindow: React.FC<ChatWindowProps> = ({ messages }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50">
      {messages.length === 0 && (
        <div className="flex flex-col items-center justify-center h-full text-gray-400">
          <p className="text-lg mb-2">No messages yet</p>
          <p className="text-sm">Start a conversation by clicking the microphone button</p>
        </div>
      )}
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex ${
            message.type === 'user' ? 'justify-end' : 'justify-start'
          }`}
        >
          <div
            className={`max-w-[70%] rounded-2xl px-5 py-3 shadow-sm ${
              message.type === 'user'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-800 border border-gray-200'
            }`}
          >
            <div className={`text-xs font-semibold mb-1 ${
              message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
            }`}>
              {message.type === 'user' ? 'You' : 'Assistant'}
            </div>
            <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>
            <div className={`text-xs mt-2 ${
              message.type === 'user' ? 'text-blue-200' : 'text-gray-400'
            }`}>
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};