import { useState, useEffect, useRef } from 'react';
import { MicRecorder } from './components/MicRecorder';
import { ChatWindow } from './components/ChatWindow';
import type { Message } from './components/ChatWindow';
import { WebSocketManager } from './utils/WebSocketManager';
import './App.css';

function App() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentAudio, setCurrentAudio] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const wsManagerRef = useRef<WebSocketManager | null>(null);

  useEffect(() => {
    // Initialize WebSocket connection
    const wsManager = new WebSocketManager();
    wsManagerRef.current = wsManager;
    
    // Add connection state handler
    const checkConnection = setInterval(() => {
      setIsConnected(wsManager.isConnected);
    }, 1000);

    // Set up message handlers
    wsManager.on('transcript', (data) => {
      setCurrentTranscript(data.content);
      // Add user message
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: data.content,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, userMessage]);
    });

    wsManager.on('response', (data) => {
      // Parse and display the response
      const { text, both } = data.content;
      const displayContent = [...text, ...both].join('\n');
      
      if (displayContent) {
        const assistantMessage: Message = {
          id: Date.now().toString(),
          type: 'assistant',
          content: displayContent,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
      }
    });

    wsManager.on('audio', (data) => {
      setCurrentAudio(data.content);
      // Auto-play the audio
      playAudio(data.content);
    });

    wsManager.on('done', () => {
      setIsProcessing(false);
    });

    wsManager.on('error', (data) => {
      console.error('Error:', data.message);
      setIsProcessing(false);
    });

    // Connect to WebSocket
    wsManager.connect()
      .then(() => setIsConnected(true))
      .catch((error) => {
        console.error('Failed to connect:', error);
        setIsConnected(false);
      });

    return () => {
      clearInterval(checkConnection);
      wsManager.disconnect();
    };
  }, []);

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const playAudio = (audioData: string) => {
    if (!audioData) return;
    
    // Convert base64 to blob
    const byteCharacters = atob(audioData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'audio/mpeg' });
    const url = URL.createObjectURL(blob);

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio();
    }

    audioRef.current.src = url;
    audioRef.current.play().catch(e => console.error('Audio playback failed:', e));
    
    // Clean up when audio ends
    audioRef.current.onended = () => {
      URL.revokeObjectURL(url);
      setCurrentAudio(null);
    };
  };

  const handleRecordingComplete = () => {
    setIsProcessing(true);
    setCurrentTranscript('');
  };

  const handleRecordingStart = () => {
    // Stop any playing audio when user starts speaking
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setCurrentAudio(null);
  };

  const handleStop = () => {
    wsManagerRef.current?.stop();
    setIsProcessing(false);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <header className="bg-white shadow-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-5">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-semibold text-gray-800">Multimodal AI Assistant</h1>
            <div className="flex items-center space-x-2 bg-gray-50 px-3 py-1.5 rounded-full">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'} ${isConnected ? 'animate-pulse' : ''}`}></div>
              <span className="text-sm font-medium text-gray-700">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 overflow-hidden flex flex-col max-w-5xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex-1 bg-white rounded-xl shadow-lg overflow-hidden flex flex-col">
          <ChatWindow messages={messages} />
          
          {currentAudio && (
            <div className="p-4 border-t border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center space-x-3">
                <div className="animate-pulse">
                  <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-700">Playing AI response...</p>
              </div>
            </div>
          )}

          {isProcessing && (
            <div className="p-5 border-t border-gray-200 bg-blue-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="text-blue-800 font-medium">
                    {currentTranscript ? 'Processing your request...' : 'Listening...'}
                  </span>
                </div>
                <button
                  onClick={handleStop}
                  className="px-4 py-2 text-sm font-medium bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Stop
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="mt-4 flex justify-center">
          {wsManagerRef.current && (
            <MicRecorder 
              wsManager={wsManagerRef.current} 
              onRecordingComplete={handleRecordingComplete}
              onRecordingStart={handleRecordingStart}
            />
          )}
        </div>
      </main>
    </div>
  );
}

export default App;