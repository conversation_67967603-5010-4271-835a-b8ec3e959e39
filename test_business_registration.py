#!/usr/bin/env python
"""
Test script for business registration with duplicate detection
"""

import requests
import json
import sys
import time

# Base URL for the API
BASE_URL = "http://localhost:8000/api"

def register_user(email, password, first_name, last_name):
    """Register a new user"""
    url = f"{BASE_URL}/auth/register/"
    data = {
        "email": email,
        "password": password,
        "first_name": first_name,
        "last_name": last_name
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 201:
        print(f"User {email} registered successfully")
        return response.json()
    else:
        print(f"Failed to register user: {response.text}")
        return None

def login_user(email, password):
    """Login a user and get authentication token"""
    url = f"{BASE_URL}/auth/login/"
    data = {
        "username": email,  # Backend expects email as username
        "password": password
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        print(f"User {email} logged in successfully")
        return response.json()
    else:
        print(f"Failed to login: {response.text}")
        return None

def register_business(auth_token, business_data, user_data):
    """Register a business"""
    url = f"{BASE_URL}/auth/register/business/"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    
    # Combine business data with user data
    data = {
        **business_data,
        "email": user_data.get("email"),
        "first_name": user_data.get("first_name"),
        "last_name": user_data.get("last_name")
    }
    
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 201:
        print(f"Business '{business_data['business_name']}' registered successfully")
        return response.json()
    else:
        print(f"Failed to register business: {response.text}")
        return None

def check_duplicate_business(auth_token, business_name, address_line1):
    """Check if a business is a duplicate"""
    url = f"{BASE_URL}/accounts/check-duplicate/"
    headers = {
        "Authorization": f"Bearer {auth_token}"
    }
    params = {
        "business_name": business_name,
        "address_line1": address_line1
    }
    
    response = requests.get(url, params=params, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("duplicate"):
            print(f"Business '{business_name}' at '{address_line1}' is a duplicate")
        else:
            print(f"Business '{business_name}' at '{address_line1}' is not a duplicate")
        return result
    else:
        print(f"Failed to check duplicate: {response.text}")
        return None

def main():
    """Main function to test business registration"""
    # Generate a unique email to avoid conflicts
    timestamp = int(time.time())
    email = f"test{timestamp}@example.com"
    password = "StrongP@ssw0rd"
    first_name = "Test"
    last_name = "User"
    
    # User data for registration
    user_data = {
        "email": email,
        "password": password,
        "first_name": first_name,
        "last_name": last_name
    }
    
    # Register a new user
    user_response = register_user(email, password, first_name, last_name)
    if not user_response:
        sys.exit(1)
    
    # Login with the new user
    login_data = login_user(email, password)
    if not login_data:
        sys.exit(1)
    
    # Extract the token - handle different response formats
    auth_token = None
    if 'token' in login_data:
        auth_token = login_data.get('token')
    elif 'access' in login_data:
        auth_token = login_data.get('access')
    
    if not auth_token:
        print("Could not extract authentication token from login response")
        sys.exit(1)
    
    # Business data for registration
    business_data = {
        "business_name": "Test Business LLC",
        "address_line1": "123 Main St",
        "address_line2": "Suite 100",
        "city": "San Francisco",
        "state": "CA",
        "zip_code": "94105",
        "industry": ["Technology"],
        "company_phone": "************",
        "company_email": email,
        "website": "testbusiness.com"
    }
    
    # Check if business is a duplicate (should be False)
    check_result = check_duplicate_business(auth_token, business_data["business_name"], business_data["address_line1"])
    
    # Register the business
    business_result = register_business(auth_token, business_data, user_data)
    
    # Check again if business is a duplicate (should be True)
    check_result = check_duplicate_business(auth_token, business_data["business_name"], business_data["address_line1"])
    
    # Try registering a similar business with slight variations
    similar_business_data = business_data.copy()
    similar_business_data["business_name"] = "Test Business, LLC"  # Added comma
    similar_business_data["address_line1"] = "123 Main Street"  # Street instead of St
    
    # Check if similar business is detected as duplicate
    check_result = check_duplicate_business(auth_token, similar_business_data["business_name"], similar_business_data["address_line1"])
    
    # Try to register the similar business
    similar_business_result = register_business(auth_token, similar_business_data, user_data)
    
    print("\nTest completed!")

if __name__ == "__main__":
    main() 