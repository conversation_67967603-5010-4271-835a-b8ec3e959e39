#!/usr/bin/env python
"""
Enhanced test script for business duplicate detection with edge cases
"""

import requests
import json
import sys
import time
import random
import string
from colorama import init, Fore, Style

# Initialize colorama
init()

# Constants
API_BASE_URL = "http://localhost:8000/api"
REGISTER_USER_URL = f"{API_BASE_URL}/auth/register/"
LOGIN_URL = f"{API_BASE_URL}/auth/login/"
REGISTER_BUSINESS_URL = f"{API_BASE_URL}/auth/register-business/"
CHECK_DUPLICATE_URL = f"{API_BASE_URL}/accounts/check-duplicate/"
DELETE_TEST_BUSINESSES_URL = f"{API_BASE_URL}/accounts/delete-test-businesses/"  # New endpoint for cleanup

def print_success(message):
    """Print a success message"""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message):
    """Print an error message"""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_info(message):
    """Print an info message"""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def print_header(message):
    """Print a header message"""
    print(f"\n{Fore.YELLOW}=== {message} ==={Style.RESET_ALL}")

def print_section(message):
    """Print a section header"""
    print(f"\n{Fore.CYAN}--- {message} ---{Style.RESET_ALL}")

def get_auth_header(token):
    """
    Get the appropriate authentication header based on token type
    """
    # Check if it's a JWT token (has two dots)
    if token.count('.') == 2:
        return f"Bearer {token}"
    else:
        return f"Token {token}"

def delete_test_businesses(auth_token):
    """
    Delete test businesses before running the tests
    """
    try:
        # This relies on a backend endpoint to delete test businesses
        # Alternative: we could just clean up businesses created during this test run
        headers = {
            "Authorization": get_auth_header(auth_token),
            "Content-Type": "application/json"
        }
        response = requests.post(DELETE_TEST_BUSINESSES_URL, headers=headers)
        
        if response.status_code == 200:
            print_success("Test businesses cleaned up successfully")
            return True
        else:
            print_info("No test cleanup endpoint available - continuing with tests")
            return True
    except Exception as e:
        print_info(f"Cleanup skipped: {str(e)}")
        return True

def register_user(email, password, first_name, last_name):
    """
    Register a user in the system
    """
    try:
        payload = {
            "email": email,
            "password": password,
            "first_name": first_name,
            "last_name": last_name
        }
        response = requests.post(REGISTER_USER_URL, json=payload)
        
        if response.status_code == 201:
            print_success(f"User {email} registered successfully")
            return True
        else:
            error_msg = response.json().get('detail', 'Unknown error')
            print_error(f"Failed to register user: {error_msg}")
            return False
    except Exception as e:
        print_error(f"Error registering user: {str(e)}")
        return False

def login_user(email, password):
    """
    Login and get authentication token
    """
    try:
        payload = {
            "username": email,  # Backend expects email as username
            "password": password
        }
        response = requests.post(LOGIN_URL, json=payload)
        
        if response.status_code == 200:
            # Handle different token formats
            data = response.json()
            token = None
            
            # Check for simple token format
            if 'token' in data:
                token = data.get('token')
            # Check for JWT format
            elif 'access' in data:
                token = data.get('access')
            
            if token:
                print_success(f"User {email} logged in successfully")
                print(f"Token type: {'JWT' if token.count('.') == 2 else 'Simple'}")
                return token
            else:
                print_error("No token found in response")
                return None
        else:
            error_msg = response.json().get('error', 'Unknown error')
            print_error(f"Failed to login: {error_msg}")
            return None
    except Exception as e:
        print_error(f"Error logging in: {str(e)}")
        return None

def register_business(auth_token, business_data, user_email):
    """
    Register a business with the given data
    """
    try:
        headers = {
            "Authorization": get_auth_header(auth_token),
            "Content-Type": "application/json"
        }
        
        # Add user email to business data
        business_data_with_email = {
            **business_data,
            "email": user_email,
            "first_name": "Test",
            "last_name": "User"
        }
        
        # Check if business is duplicate before registering
        is_duplicate, duplicate_businesses = check_duplicate_business(
            auth_token, 
            business_data["business_name"], 
            business_data["address_line1"]
        )
        
        if is_duplicate:
            print_info(f"Business '{business_data['business_name']}' detected as duplicate")
            print_error(f"Failed to register duplicate business: {business_data['business_name']}")
            return False, duplicate_businesses
            
        # If not duplicate, proceed with registration
        response = requests.post(REGISTER_BUSINESS_URL, json=business_data_with_email, headers=headers)
        
        if response.status_code == 201:
            print_success(f"Business '{business_data['business_name']}' registered successfully")
            return True, None
        else:
            error_msg = response.json().get('detail', 'Unknown error')
            print_error(f"Failed to register business: {error_msg}")
            return False, None
    except Exception as e:
        print_error(f"Error registering business: {str(e)}")
        return False, None

def check_duplicate_business(auth_token, business_name, address_line1, business_id=None):
    """
    Check if a business is a duplicate
    """
    try:
        headers = {
            "Authorization": get_auth_header(auth_token)
        }
        
        params = {
            "business_name": business_name,
            "address_line1": address_line1
        }
        
        if business_id:
            params["business_id"] = business_id
        
        # Print debug info
        print(f"Checking duplicate with token: {auth_token[:10]}...")
        print(f"Headers: {headers}")
            
        response = requests.get(CHECK_DUPLICATE_URL, params=params, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            is_duplicate = data.get('duplicate', False)
            duplicate_businesses = data.get('duplicate_businesses', [])
            
            if is_duplicate and duplicate_businesses:
                for business in duplicate_businesses:
                    print_info(f"Business '{business_name}' at '{address_line1}' is a duplicate of: {business['account_name']}")
            elif not is_duplicate:
                print_success(f"Business '{business_name}' at '{address_line1}' is NOT a duplicate")
                
            return is_duplicate, duplicate_businesses
        else:
            error_msg = response.json().get('detail', 'Unknown error')
            print_error(f"Failed to check duplicate: {error_msg}")
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False, []
    except Exception as e:
        print_error(f"Error checking duplicate: {str(e)}")
        return False, []

def generate_test_cases():
    """
    Generate test cases for duplicate detection
    """
    base_business = {
        "business_name": "Acme Technology Solutions",
        "address_line1": "123 Main Street",
        "city": "Anytown",
        "state": "CA",
        "zip_code": "94001",
        "country": "US",
        "company_phone": "************",
        "website": "https://www.acmetech.example",
        "industry": ["Technology"],
        "tax_id": "12-3456789"
    }
    
    test_cases = [
        {
            "name": "Base Business",
            "data": base_business.copy(),
            "expected_duplicate": False,
            "base": True
        },
        {
            "name": "Exact Duplicate",
            "data": base_business.copy(),
            "expected_duplicate": True
        },
        {
            "name": "Business with Suffix",
            "data": {**base_business, "business_name": "Acme Technology Solutions, Inc."},
            "expected_duplicate": True
        },
        {
            "name": "Different Capitalization",
            "data": {**base_business, "business_name": "ACME TECHNOLOGY SOLUTIONS", "address_line1": "123 MAIN STREET"},
            "expected_duplicate": True
        },
        {
            "name": "Special Characters",
            "data": {**base_business, "business_name": "Acme Tech. Solutions & Co."},
            "expected_duplicate": True
        },
        {
            "name": "Address Abbreviation",
            "data": {**base_business, "address_line1": "123 Main St."},
            "expected_duplicate": True
        },
        {
            "name": "Address with Suite",
            "data": {**base_business, "address_line1": "123 Main Street, Suite 200"},
            "expected_duplicate": True
        },
        {
            "name": "Business with 'The' Prefix",
            "data": {**base_business, "business_name": "The Acme Technology Solutions"},
            "expected_duplicate": True
        },
        {
            "name": "Business Name with Typos",
            "data": {**base_business, "business_name": "Acme Tecnology Solutins"},
            "expected_duplicate": True
        },
        {
            "name": "Address with Typos",
            "data": {**base_business, "address_line1": "123 Mian Stret"},
            "expected_duplicate": True
        },
        {
            "name": "Different Business Name",
            "data": {**base_business, "business_name": "Beta Technology Solutions"},
            "expected_duplicate": False
        },
        {
            "name": "Different Address",
            "data": {**base_business, "business_name": "Acme Technology Solutions", "address_line1": "456 Oak Street"},
            "expected_duplicate": False
        },
        {
            "name": "Different Business at Same Address",
            "data": {**base_business, "business_name": "Zenith Consulting Group"},
            "expected_duplicate": False
        },
        {
            "name": "Same Business at Different Address",
            "data": {**base_business, "business_name": "Acme Technology Solutions", "address_line1": "789 Broadway"},
            "expected_duplicate": False
        },
    ]
    
    return test_cases

def main():
    # Generate a unique email for testing
    timestamp = int(time.time())
    test_email = f"test{timestamp}@example.com"
    test_password = "Test123456!"
    
    print_header("Enhanced Business Duplicate Detection Test")
    
    # Register and login test user
    if not register_user(test_email, test_password, "Test", "User"):
        sys.exit(1)
    
    auth_token = login_user(test_email, test_password)
    if not auth_token:
        sys.exit(1)
    
    # Delete test businesses before running tests
    delete_test_businesses(auth_token)
    
    # Generate test cases
    test_cases = generate_test_cases()
    
    print_header(f"Running {len(test_cases)} test cases")
    
    # Register the base business first
    base_case = next((case for case in test_cases if case.get("base", False)), None)
    if base_case:
        print_section(f"Registering base business: {base_case['data']['business_name']}")
        success, _ = register_business(auth_token, base_case['data'], test_email)
        if not success:
            print_error("Failed to register base business. Cannot continue tests.")
            sys.exit(1)
    
    # Run test cases (excluding the base case)
    test_results = []
    for i, test_case in enumerate([tc for tc in test_cases if not tc.get("base", False)], 1):
        print_section(f"Test Case {i}: {test_case['name']}")
        
        # For test cases that shouldn't be duplicates, we'll try to register them
        if not test_case["expected_duplicate"]:
            is_duplicate, _ = check_duplicate_business(
                auth_token, 
                test_case["data"]["business_name"], 
                test_case["data"]["address_line1"]
            )
            
            test_passed = is_duplicate == test_case["expected_duplicate"]
            test_results.append(test_passed)
            
            if test_passed:
                print_success(f"Test passed: Expected duplicate={test_case['expected_duplicate']}, Got duplicate={is_duplicate}")
                # If it's not a duplicate, we should be able to register it
                success, _ = register_business(auth_token, test_case["data"], test_email)
                if success:
                    print_success(f"Successfully registered non-duplicate business: {test_case['data']['business_name']}")
                else:
                    print_error(f"Failed to register non-duplicate business: {test_case['data']['business_name']}")
            else:
                print_error(f"Test failed: Expected duplicate={test_case['expected_duplicate']}, Got duplicate={is_duplicate}")
        else:
            # For duplicate test cases, just check if they're duplicates
            is_duplicate, _ = check_duplicate_business(
                auth_token, 
                test_case["data"]["business_name"], 
                test_case["data"]["address_line1"]
            )
            
            test_passed = is_duplicate == test_case["expected_duplicate"]
            test_results.append(test_passed)
            
            if test_passed:
                print_success(f"Test passed: Expected duplicate={test_case['expected_duplicate']}, Got duplicate={is_duplicate}")
            else:
                print_error(f"Test failed: Expected duplicate={test_case['expected_duplicate']}, Got duplicate={is_duplicate}")
    
    # Print test summary
    print_header("Test Summary")
    passed = sum(test_results)
    failed = len(test_results) - passed
    success_rate = (passed / len(test_results)) * 100 if test_results else 0
    
    print(f"Total tests: {len(test_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success rate: {success_rate:.2f}%")
    
    if failed > 0:
        sys.exit(1)

if __name__ == "__main__":
    main() 