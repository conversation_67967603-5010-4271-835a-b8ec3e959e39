# The Kong API Gateway

```bash
# Stop existing Kong
docker rm -f kong

# Replace environment variables in kong.yml
envsubst < kong_template.yml > kong.yml

# Run Kong with debugging tools
docker run -d --name kong \
  -v ./kong.yml:/usr/local/kong/declarative/kong.yml \
  --env-file ./.env \
  -e "KONG_DATABASE=off" \
  -e "KONG_DECLARATIVE_CONFIG=/usr/local/kong/declarative/kong.yml" \
  -e "KONG_PROXY_LISTEN=0.0.0.0:8000" \
  -e "KONG_ADMIN_LISTEN=0.0.0.0:8001" \
  -p 8080:8000 \
  -p 8081:8001 \
  --user root \
  --entrypoint /bin/sh \
  kong:latest -c "apt-get update && apt-get install -y curl && /docker-entrypoint.sh kong docker-start"

# Test backend connectivity
docker exec -it kong curl http://172.17.0.1:8000/api/auth/login/
```
