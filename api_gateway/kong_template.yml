_format_version: "3.0"
_transform: true

consumers:
  - username: aida-admin
    jwt_secrets:
      - key: aida-admin
        algorithm: HS256
        rsa_public_key: null
        secret: $KONG_JWT_SECRET

services:
  - name: backend-api
    url: http://$HOST_IP:8000
    routes:
      - name: auth-route
        paths:
          - /api/auth
          - /api/csrf
          - /api/chat
        strip_path: false
        preserve_host: false

      - name: api-route
        paths:
          - /api
        strip_path: false
        preserve_host: false
        plugins:
          - name: jwt
            config:
              secret_is_base64: false
              claims_to_verify:
                - exp
              run_on_preflight: false
              key_claim_name: iss
              maximum_expiration: 86400
              anonymous: null
              cookie_names: []

    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
            - X-CSRFTOKEN
            - Access-Control-Allow-Origin
            - Access-Control-Allow-Methods
            - Access-Control-Allow-Headers
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false

      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  - name: llm-api
    url: http://$HOST_IP:8002/api
    routes:
      - name: llm-api-route
        paths:
          - /llm-api
        strip_path: true
        preserve_host: false
        # plugins:
        #   - name: jwt
        #     config:
        #       secret_is_base64: false
        #       claims_to_verify:
        #         - exp
        #       run_on_preflight: false
        #       key_claim_name: iss
        #       maximum_expiration: 86400
        #       anonymous: null
        #       cookie_names: []

    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Content-Type
            - Authorization
            - X-CSRFTOKEN
            - Access-Control-Allow-Origin
            - Access-Control-Allow-Methods
            - Access-Control-Allow-Headers
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false

      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local
