#!/bin/bash

# Source environment variables
set -a
source .env
set +a

# Replace environment variables in kong.yml
envsubst < kong_template.yml > kong.yml

# Start Kong
docker rm -f kong
docker run -d --name kong \
  -v ./kong.yml:/usr/local/kong/declarative/kong.yml \
  -e "KONG_DATABASE=off" \
  -e "KONG_DECLARATIVE_CONFIG=/usr/local/kong/declarative/kong.yml" \
  -e "KONG_PROXY_LISTEN=0.0.0.0:8000" \
  -e "KONG_ADMIN_LISTEN=0.0.0.0:8001" \
  -p 8080:8000 \
  -p 8081:8001 \
  kong:latest 