#!/usr/bin/env bash
# Get your token from the browser and replace TOKEN_HERE
if [ -p /dev/stdin ]; then
  TOKEN=$(cat)
  echo "Token: $TOKEN"
else
  echo "expecting token to be piped in, exiting script"
  exit
fi

if [[ -z "$SECRET" ]]; then
  echo "expecting SECRET to be set, exiting script"
  exit
fi

echo "Secret: $SECRET"

# Use Python to verify the JWT token
python3 -c "
import jwt
import sys

try:
    token = '$TOKEN'
    secret = '$SECRET'
    decoded = jwt.decode(token, secret, algorithms=['HS256'])
    print('\nDecoded token:')
    for key, value in decoded.items():
        print(f'{key}: {value}')
    sys.exit(0)
except jwt.InvalidSignatureError:
    print('\nInvalid signature')
    sys.exit(1)
except Exception as e:
    print(f'\nError: {str(e)}')
    sys.exit(1)
"
