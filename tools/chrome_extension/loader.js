// This script will be used to load the Aida resources
(function() {
  function loadResource(url, type) {
    return new Promise((resolve, reject) => {
      const element = document.createElement(type === 'js' ? 'script' : 'link');
      
      if (type === 'js') {
        element.src = url;
        element.async = true;
      } else {
        element.rel = 'stylesheet';
        element.href = url;
      }

      element.onload = () => resolve();
      element.onerror = (error) => reject(error);

      document.head.appendChild(element);
    });
  }

  // Get the base URL from the data attribute or use default
  const aidaBase = document.currentScript.getAttribute('data-aida-base');

  if (aidaBase) {
    Promise.all([
      loadResource(`${aidaBase}/assets/main.js`, 'js'),
      loadResource(`${aidaBase}/assets/index.css`, 'css')
    ])
    .then(() => {
      console.log('Aida resources loaded successfully');
    })
    .catch((error) => {
      console.error('Error loading Aida resources:', error);
    });
  }
})();
