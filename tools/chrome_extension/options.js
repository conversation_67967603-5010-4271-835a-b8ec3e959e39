document.addEventListener('DOMContentLoaded', () => {
  const enabledCheckbox = document.getElementById('enabled');
  const scriptUrlInput = document.getElementById('scriptUrl');
  const domainsTextarea = document.getElementById('domains');
  const excludedDomainsTextarea = document.getElementById('excludedDomains');
  const errorDiv = document.getElementById('error');
  const saveButton = document.getElementById('save');

  const DEFAULT_SCRIPT_URL = 'https://research-triangle.ai/aida/loader.js';

  // Load saved settings
  chrome.storage.sync.get({
    enabled: true,
    scriptUrl: DEFAULT_SCRIPT_URL,
    allowedDomains: ['*', 'research-triangle.ai', 'racl.org'],
    excludedDomains: []
  }, (result) => {
    enabledCheckbox.checked = result.enabled;
    scriptUrlInput.value = result.scriptUrl;
    domainsTextarea.value = result.allowedDomains.join('\n');
    excludedDomainsTextarea.value = result.excludedDomains.join('\n');
  });

  saveButton.addEventListener('click', () => {
    const domains = domainsTextarea.value
      .split('\n')
      .map(d => d.trim())
      .filter(d => d);

    const excludedDomains = excludedDomainsTextarea.value
      .split('\n')
      .map(d => d.trim())
      .filter(d => d);

    // Validate domains
    const validateDomain = domain => {
      if (domain === '*' || domain === 'localhost' || domain === '127.0.0.1') return true;
      return domain.match(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/);
    };

    const invalidDomains = domains.filter(d => !validateDomain(d));
    const invalidExcludedDomains = excludedDomains.filter(d => !validateDomain(d));

    if (invalidDomains.length > 0) {
      errorDiv.textContent = `Invalid allowed domains: ${invalidDomains.join(', ')}`;
      return;
    }

    if (invalidExcludedDomains.length > 0) {
      errorDiv.textContent = `Invalid excluded domains: ${invalidExcludedDomains.join(', ')}`;
      return;
    }

    // Validate script URL
    const scriptUrl = scriptUrlInput.value.trim() || DEFAULT_SCRIPT_URL;
    try {
      new URL(scriptUrl);
    } catch (e) {
      errorDiv.textContent = 'Invalid script URL';
      return;
    }

    errorDiv.textContent = '';
    chrome.runtime.sendMessage({
      type: 'UPDATE_SETTINGS',
      settings: {
        enabled: enabledCheckbox.checked,
        scriptUrl: scriptUrl,
        allowedDomains: domains,
        excludedDomains: excludedDomains
      }
    });

    // Show saved feedback
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Saved!';
    setTimeout(() => {
      saveButton.textContent = originalText;
    }, 1500);
  });
});
