chrome.runtime.onInstalled.addListener(() => {
  // Initialize default settings with default domains
  chrome.storage.sync.set({
    enabled: true,
    scriptUrl: 'https://research-triangle.ai/aida/loader.js',
    allowedDomains: [
      '*',
      'research-triangle.ai',
      'racl.org'
    ],
    excludedDomains: [
      'localhost',
      '127.0.0.1'
    ]
  });
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'UPDATE_SETTINGS') {
    chrome.storage.sync.set(request.settings);
  }
});
