<!DOCTYPE html>
<html>
<head>
  <title><PERSON>a Settings</title>
  <style>
    body { padding: 20px; }
    .error { color: red; }
    .form-group { margin-bottom: 20px; }
    input[type="text"] { width: 100%; padding: 8px; margin-top: 5px; }
    textarea { width: 100%; }
    .help-text { 
      color: #666; 
      font-size: 0.9em;
      margin-top: 4px;
    }
  </style>
</head>
<body>
  <h1>Aida Settings</h1>
  <div class="form-group">
    <label>
      <input type="checkbox" id="enabled">
      Enable Aida
    </label>
  </div>
  <div class="form-group">
    <label for="scriptUrl">Script URL:</label>
    <input type="text" id="scriptUrl">
  </div>
  <div class="form-group">
    <h3>Allowed Domains</h3>
    <textarea id="domains" rows="5" cols="50"></textarea>
    <div class="help-text">One domain per line. Use * for all domains.</div>
  </div>
  <div class="form-group">
    <h3>Excluded Domains</h3>
    <textarea id="excludedDomains" rows="5" cols="50"></textarea>
    <div class="help-text">One domain per line. These domains will be excluded even if allowed above.</div>
  </div>
  <div class="error" id="error"></div>
  <button id="save">Save</button>
  <script src="options.js"></script>
</body>
</html>
