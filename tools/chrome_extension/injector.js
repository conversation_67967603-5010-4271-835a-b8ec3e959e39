// Add error handling for script injection
function injectScript(aidaBase) {
  console.log('Attempting to inject <PERSON><PERSON> with base URL:', aidaBase);  // Debug log

  try {
    // Get the URL for our loader script
    const loaderUrl = chrome.runtime.getURL('loader.js');
    console.log('Loader URL:', loaderUrl);

    // Create and inject the loader script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = loaderUrl;
    script.setAttribute('data-aida-base', aidaBase);

    script.onload = () => {
      console.log('Aida loader script loaded successfully');
    };

    script.onerror = (error) => {
      console.error('Error loading Aida loader script:', error);
    };

    document.head.appendChild(script);
  } catch (error) {
    console.error('Failed to inject Aida:', error);
  }
}

// Check if we should inject on this page
console.log('Checking if script should be injected');  // Debug log

chrome.storage.sync.get({
  enabled: true,
  aidaBase: 'https://aida.research-triangle.ai/aida_releases/1.0',
  allowedDomains: ['*', 'research-triangle.ai', 'racl.org'],
  excludedDomains: ['localhost', '127.0.0.1']
}, (result) => {
  console.log('Got storage settings:', result);  // Debug log

  // First check if extension is enabled globally
  if (!result.enabled) {
    console.log('Extension is disabled');  // Debug log
    return;
  }
  
  const hostname = window.location.hostname;
  console.log('Current hostname:', hostname);  // Debug log
  
  // Helper function to check if domain matches (including subdomains)
  const isDomainMatch = (pattern, domain) => {
    if (pattern === '*') return true;
    return domain === pattern || domain.endsWith('.' + pattern);
  };
  
  // Then check if domain is allowed
  const isAllowed = result.allowedDomains.some(pattern => 
    isDomainMatch(pattern, hostname)
  );
  
  console.log('Allowed domains:', result.allowedDomains);  // Debug log
  console.log('Is domain allowed?', isAllowed);  // Debug log
  
  if (!isAllowed) {
    console.log('Domain not in allowed list');  // Debug log
    return;
  }

  // Finally check if domain is excluded
  const isExcluded = result.excludedDomains.some(pattern => 
    isDomainMatch(pattern, hostname)
  );
  console.log('Excluded domains:', result.excludedDomains);  // Debug log
  console.log('Is domain excluded?', isExcluded);  // Debug log
  
  if (isExcluded) {
    console.log('Domain is in exclude list');  // Debug log
    return;
  }

  console.log('Injection criteria met, proceeding with injection');  // Debug log
  injectScript(result.aidaBase);
});
