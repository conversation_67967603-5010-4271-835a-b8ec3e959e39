{"name": "admin-platform", "version": "1.0.0", "private": true, "workspaces": ["frontend", "js_component"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:js-component\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python manage.py runserver", "dev:js-component": "cd js_component && npm run dev", "install:all": "concurrently \"npm run install:frontend\" \"npm run install:js-component\" \"npm run install:backend\"", "install:frontend": "cd frontend && npm install", "install:js-component": "cd js_component && npm install", "install:backend": "cd backend && pip install -r requirements.txt", "build": "concurrently \"npm run build:frontend\" \"npm run build:js-component\"", "build:frontend": "cd frontend && npm run build", "build:js-component": "cd js_component && npm run build", "test": "concurrently \"npm run test:frontend\" \"npm run test:js-component\" \"npm run test:backend\"", "test:frontend": "cd frontend && npm test", "test:js-component": "cd js_component && npm test", "test:backend": "cd backend && python manage.py test", "clean": "concurrently \"npm run clean:frontend\" \"npm run clean:js-component\"", "clean:frontend": "cd frontend && rm -rf node_modules", "clean:js-component": "cd js_component && rm -rf node_modules", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down", "docker:clean": "docker-compose down -v"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "wait-on": "^7.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}