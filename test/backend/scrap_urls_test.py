import asyncio
from datetime import datetime
import json
import os
import unittest
import uuid
import ultraimport
scrap_urls_service = ultraimport('__dir__/../../backend/main/services/LinkServices/scrap_urls_service.py', 'scrap_urls_service')

class test_scrap_urls(unittest.TestCase):
    base_url = 'http://www.racl.org'
    valid_url_list=[]
    #valid_url_list = [ 'http://www.racl.org/wp-content/uploads/*', 'http://www.racl.org/news-and-announcements/*']
    #valid_after = "2024-01-01"
    valid_after = ""
    begin_datetime = datetime.now()
    response = asyncio.run(scrap_urls_service.scrap_urls (base_url, valid_url_list, valid_after)) 
    print ("the crawling takes ", datetime.now()-begin_datetime)
    print (response)
    for res in response:
        print (res['url'])
    print (len(response))

