from datetime import datetime
import json
import os
import unittest
import uuid
import ultraimport
AnythingLLMService = ultraimport('__dir__/../../backend/main/services/LLMServices/AnythingLLMService.py', 'AnythingLLMService')
scrap_urls_service = ultraimport('__dir__/../../backend/main/services/LinkServices/scrap_urls_service.py', 'scrap_urls_service')

class TestAnythingLLMService(unittest.TestCase):
    def test_1_e2e_aida_scenario(self):
        business_name = "Shanghai Alley"
        workspace_slug = "aida-demo-6d1413ea"
          #start the conversation
        my_uuid = str(uuid.uuid4())
        response11 = AnythingLLMService.createChat (workspace_slug, "chat-"+my_uuid)
        #print (response11)
        # Access the embedded key value
        thread_slug = response11["thread"]["threadSlug"]
        thread_name = response11["thread"]["name"]
        print ("chat_id" + thread_slug)
        #Scenario 5: submit first question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        # Get the current timestamp
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question1="where is the location of Shanghai Alley? "
        response11 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question1)
        print (response11['textResponse'])
        question2="What is the ingredients in Mapo Tofu? "
        response12 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question2)
        print (response12['textResponse'])
        question3="How much is Mapo Tofu? "
        response13 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question3)
        print (response13['textResponse'])
        question4="Can I order a Mapo Tofu?"
        response14 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question4)
        print (response14['textResponse'])
        question5="yes, please"
        response15 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question5)
        print (response15['textResponse'])
        response16 = AnythingLLMService.summarizeOrder(workspace_slug, thread_slug)
        print (response16)
        #Scenario 7: the web guest clicks a chat close logo to stop a conversation  
        #within this Scenario, the thread will be archived and deleted
        response14 = AnythingLLMService.deleteChat(workspace_slug, thread_slug)
        #AnythingLLMService.delete_file (workspace_slug, new_file_path)
        #Scenario 8: remove an existing customer  
        #within this Scenario, the workspace will be archived and deleted
        #the folder associated with this workspace will be archived (??) and deleted
        #AnythingLLMService.delete_customer(workspace_slug, customer_folder)
        #response15 = AnythingLLMService.deleteWorkSpace(workspace_slug)


    def test_51_summarizeOrder_scenario(self):
        workspace_slug = "shanghai-alley"
        my_uuid = str(uuid.uuid4())
        response11 = AnythingLLMService.createChat (workspace_slug, "chat-"+my_uuid)
        #print (response11)
        # Access the embedded key value
        thread_slug = response11["thread"]["slug"]
        thread_name = response11["thread"]["name"]
        print ("chat_id" + thread_slug)
        #Scenario 5: submit first question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        # Get the current timestamp
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question1="I want to order mapo tofu "
        response11 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question1)
        print (response11)
        question2="I also want to order kongpo chicken "
        response12 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question2)  
        print (response12)      
        response13 = AnythingLLMService.summarizeOrder(workspace_slug, thread_slug)
        print (response13)
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 6: submit second question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question2="does RACL offer chinese language class? "
        response13 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question2)
        print (response13)
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 7: the web guest clicks a chat close logo to stop a conversation  
        #within this Scenario, the thread will be archived and deleted
        response14 = AnythingLLMService.deleteChat(workspace_slug, thread_slug)

    def test_1_e2e_scenario(self):
        #register a business
        #business_name = "racl"
        business_name = "Shanghai Alley"
        #response = AnythingLLMService.register (business_name)
        #store the workspace_slug and filefold created for this business
        #workspace_slug = response['workspace_slug']
        workspace_slug = "aida-demo-6d1413ea"
        customer_folder = response['customer_folder']
        #upload a new file
        #racl_file=os.path.dirname(__file__) + '\\..\\resource\\racl_questions_answers.csv'
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-1.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-2.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-3.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-4.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-5.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-6.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
        racl_file=os.path.dirname(__file__) + '\\..\\resource\\qMenu_Food_Ordering_App-7.pdf'
        new_file_path_in_LLM = AnythingLLMService.upload_file (workspace_slug, customer_folder, racl_file)
       #test the file replacement
        #new_file_path = AnythingLLMService.replace_file (workspace_slug, customer_folder, new_file_path_in_LLM, racl_file)        
        #scrap webpages per the business base_url  
        #base_url='http://www.racl.org'
        base_url='https://shanghaialleyinc.com/'
        options = {
            "url_filter": "",
            "valid_after": "",
            "crawl_depth": 2, 
            "max_url_count": 15
        }
        response = AnythingLLMService.upload_link (workspace_slug, base_url, options)
        #start the conversation
        my_uuid = str(uuid.uuid4())
        response11 = AnythingLLMService.createChat (workspace_slug, "chat-"+my_uuid)
        #print (response11)
        # Access the embedded key value
        thread_slug = response11["thread"]["slug"]
        thread_name = response11["thread"]["name"]
        print ("chat_id" + thread_slug)
        #Scenario 5: submit first question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        # Get the current timestamp
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question1="where is the location of RACL? "
        response12 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question1)
        question2="where is the location of RACL? "
        response12 = AnythingLLMService.summarizeOrder(workspace_slug, thread_slug, question1)
        print (response12)
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 6: submit second question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question2="does RACL offer chinese language class? "
        response13 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question2)
        print (response13)
        now = datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 7: the web guest clicks a chat close logo to stop a conversation  
        #within this Scenario, the thread will be archived and deleted
        response14 = AnythingLLMService.deleteChat(workspace_slug, thread_slug)
        AnythingLLMService.delete_file (workspace_slug, new_file_path)
        #Scenario 8: remove an existing customer  
        #within this Scenario, the workspace will be archived and deleted
        #the folder associated with this workspace will be archived (??) and deleted
        AnythingLLMService.delete_customer(workspace_slug, customer_folder)
        #response15 = AnythingLLMService.deleteWorkSpace(workspace_slug)

    
    def test_2_e2e_scenario(self):
        #Scenario 1 : create a new customer in AIDA LLM
        #within this Scenario, create a workspace for this new customer
        # create a folder to hold uploaded materials for this customer
        response1 = AnythingLLMService.createWorkSpace ("racl")
        workspace_slug_racl = response1["workspace"]["slug"]
        customerFolder = workspace_slug_racl+"-folder"
        response2 = AnythingLLMService.createFolder(customerFolder)
      
        #Scenario 2:  provide a basle url link associated to this customer
        #within this Scenario, upload and embed all webpages per the base url link and under 
        base_url='http://www.racl.org'
        valid_url_list=[]
        #valid_url_list = ['http://www.racl.org']
        valid_after = ""
        #valid_after = "2016-01-01"
        begin_datetime = datetime.now()
        response3 = AnythingLLMService.uploadfromBaseURL(workspace_slug_racl, base_url, valid_url_list, valid_after)
        print (response3)
        print("scapping and embedding take ", (datetime.now() - begin_datetime))
        #Scenario 4: a web guest clicks a chat logo to start a conversation
        #within this Scenario, a thread under this workspace is created
        my_uuid = str(uuid.uuid4())
        response4 = AnythingLLMService.createChat (workspace_slug_racl, "chat-"+my_uuid)
        #print (response11)
        # Access the embedded key value
        thread_slug = response4["thread"]["slug"]
        thread_name = response4["thread"]["name"]
        #Scenario 5: submit first question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        # Get the current timestamp
        question1="where is the location of Raleigh Academy of Chinese Language? "
        response5 = AnythingLLMService.submitChat(workspace_slug_racl, thread_slug, question1)
        print (response5)
        question2="Please provie a list of classes offerred from Raleigh Academy of Chinese Language. "
        response6 = AnythingLLMService.submitChat(workspace_slug_racl, thread_slug, question2)
        print (response6)
        #Scenario 3:  provide an updated url link (i.e)  associated to this customer
        #within this Scenario, upload and embed all webpages per the base url link and under 

        #Scenario 4:  delete all embedings per the basle url link associated to this customer
        #within this Scenario, upload and embed all webpages per the base url link and under
        response7 = AnythingLLMService.deletefromBaseURL(workspace_slug_racl, base_url)  
        print (response7)

    def test_3_e2e_scenario(self):
        #Scenario 1 : create a new customer in AIDA LLM
        #within this Scenario, create a workspace for this new customer
        # create a folder to hold uploaded materials for this customer
        response1 = AnythingLLMService.createWorkSpace ("racl")
        workspace_slug_racl = response1["workspace"]["slug"]
        customerFolder = workspace_slug_racl+"-folder"
        response2 = AnythingLLMService.createFolder(customerFolder)

        #Scenario 2: upload an document to this customer
        #within this Scenario, upload the document to this folder assigned to this customer
        #then, move this documents from this folder to the workspace
        file=os.path.dirname(__file__) + '\\..\\resource\\racl_questions_answers.csv'
        response3 = AnythingLLMService.uploadDocument(file)
        file_location = response3['documents'][0]['location']
        print (response3['documents'][0]['location'])
        newFilePath = customerFolder+'/'+os.path.basename(file_location)
        response6 = AnythingLLMService.moveFile(file_location, 
                                                newFilePath)
        doc_json = {"adds": [ newFilePath]}
        response7 = AnythingLLMService.updateEmbeddings(workspace_slug_racl, doc_json)        
        #Scenario 3:  provide a root url link associated to this customer
        #within this Scenario, upload the url link to this folder assigned to this customer
        #then, move this documents from this folder to the workspace
        url='http://www.racl.org'
        response8 = AnythingLLMService.uploadLink(url)
        url_location = response8['documents'][0]['location']
        print (response8['documents'][0]['location'])
        newUrlPath = customerFolder+'/'+os.path.basename(url_location)
        response9 = AnythingLLMService.moveFile(url_location, 
                                                newUrlPath)
        url_json = {"adds": [ newUrlPath ]}
        response10 = AnythingLLMService.updateEmbeddings(workspace_slug_racl, url_json)     
        #Scenario 4: a web guest clicks a chat logo to start a conversation
        #within this Scenario, a thread under this workspace is created
        my_uuid = str(uuid.uuid4())
        response11 = AnythingLLMService.createChat (workspace_slug_racl, "chat-"+my_uuid)
        #print (response11)
        # Access the embedded key value
        thread_slug = response11["thread"]["slug"]
        thread_name = response11["thread"]["name"]
        #Scenario 5: submit first question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        # Get the current timestamp
        now = datetime.datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question1="where is the location of Raleigh Academy of Chinese Language? "
        response12 = AnythingLLMService.submitChat(workspace_slug_racl, thread_slug, question1)
        print (response12)
        now = datetime.datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 6: submit second question and get an answer
        #within this Scenario, a question is submitted to this chat thread
        now = datetime.datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        question2="does RACL offer chinese language class? "
        response13 = AnythingLLMService.submitChat(workspace_slug_racl, thread_slug, question2)
        print (response3)
        now = datetime.datetime.now()
        # Format the timestamp as a string
        formatted_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        print(formatted_timestamp)
        #Scenario 7: the web guest clicks a chat close logo to stop a conversation  
        #within this Scenario, the thread will be archived and deleted
        response14 = AnythingLLMService.deleteChat(workspace_slug_racl, thread_slug)
        #Scenario 8: remove an existing customer  
        #within this Scenario, the workspace will be archived and deleted
        #the folder associated with this workspace will be archived (??) and deleted
        response15 = AnythingLLMService.deleteWorkSpace(workspace_slug_racl)
        response16 = AnythingLLMService.deleteWorkSpace(workspace_slug_racl)
        
    def test_3_workspace(self):
        # Generate a UUID
        my_uuid = str(uuid.uuid4())
        # create a Workspace
        response1 = AnythingLLMService.createWorkSpace ("workspace"+my_uuid)
        print (response1)

        # Access the embedded key value
        slug = response1["workspace"]["slug"]
        self.assertEqual(slug and not slug=='', True)

        # search this new workspace
        response2 = AnythingLLMService.listOneWorkSpace (slug)
        print (response2)
        # Access the embedded key value
        the_slug = response2["workspace"][0]["slug"]
        self.assertEqual(slug==the_slug, True)

        # update the workspace
        newWorkSpaceName = "newWorksSpace"
        updatedData = {"name": newWorkSpaceName}
        response3 = AnythingLLMService.updateWorkSpace(slug, updatedData)
        print (response3)
        # Access the embedded key value
        workspace_name = response3["workspace"]["name"]
        self.assertEqual(workspace_name==newWorkSpaceName, True)

        # delete the workspace
        response4 = AnythingLLMService.deleteWorkSpace(slug)
        self.assertEqual(response4['status']=='success', True)

    def test_4_chat(self):
        # Generate a UUID
        my_uuid = str(uuid.uuid4())
        # create a Workspace
        response1 = AnythingLLMService.createWorkSpace ("workspace"+my_uuid)
        print (response1)

        # Access the embedded key value
        workspace_slug = response1["workspace"]["slug"]
        self.assertEqual(workspace_slug and not workspace_slug=='', True)

        # create a new chat session into this new workspace
        response2 = AnythingLLMService.createChat (workspace_slug, "chat"+my_uuid)
        print (response2)
        # Access the embedded key value
        thread_slug = response2["thread"]["slug"]
        thread_name = response2["thread"]["name"]
        self.assertEqual(thread_name=="chat"+my_uuid, True)

        # search this new thread ???

        # update the workspace
        newThreadName = "newThreadName"
        updatedData = {"name": newThreadName}
        response3 = AnythingLLMService.updateChat(workspace_slug, thread_slug, updatedData)
        print (response3)
        # Access the embedded key value
        thread_name = response3["thread"]["name"]
        self.assertEqual(thread_name==newThreadName, True)

        #send a question
        question="tell me about Mapo tofu in two sentences? please response with both Engilish and Chinese "
        response3 = AnythingLLMService.submitChat(workspace_slug, thread_slug, question)
        print (response3)
        # Access the embedded key value
        answer = response3["textResponse"]
        self.assertEqual(answer !='', True)

        # delete the thread
        response4 = AnythingLLMService.deleteChat(workspace_slug, thread_slug)
        self.assertEqual(response4['status']=='success', True)

    def test_5_document(self):
        # create two folder
        folder1 = 'folder'+str(uuid.uuid1())
        folder2 = 'folder'+str(uuid.uuid1())
        response1 = AnythingLLMService.createFolder(folder1)
        self.assertEqual(response1['success']==True, True)
        response2 = AnythingLLMService.createFolder(folder2)
        self.assertEqual(response2['success']==True, True)
        # check the folder
        # upload a file to the folder
        file=os.path.dirname(__file__) + '\\..\\resource\\racl_questions_answers.csv'
        response3 = AnythingLLMService.uploadDocument(file)
        print (response3['documents'][0]['location'])
        self.assertEqual (response3['documents'][0]['title']=='racl_questions_answers.csv', True)
        # upload a link
        url = "http://racl.org"
        response4 = AnythingLLMService.uploadLink(url)
        self.assertEqual (response4['documents'][0]['url']!='', True)
        # upload raw-text
        # query the file
        #response5 = AnythingLLMService.uploadLink(url)
        #self.assertEqual (response3['documents'][0]['url']=="file://"+url)       
        # move the file from one folder to another
        response6 = AnythingLLMService.moveFile(response3['documents'][0]['location'], 
                                                folder1+'/'+response3['documents'][0]['title'])
        # delete the file
        response7 = AnythingLLMService.deleteDocuments(folder1+'/'+response3['documents'][0]['title'])
        self.assertEqual(response7['status']=='success', True)



if __name__ == '__main__':
    unittest.main()