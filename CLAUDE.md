# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AIDA is a multi-tenant SaaS platform that provides AI-powered chat widgets for businesses. The project uses a microservices architecture with three main components:

1. **Frontend** (`/frontend`) - React admin dashboard for business management
2. **Backend** (`/backend`) - Django REST API server with PostgreSQL
3. **JS Component** (`/js_component`) - Embeddable Svelte chat widget

## Common Development Commands

### Full Stack Development
```bash
# Install all dependencies (frontend, backend, js_component)
npm run install:all

# Start all services in development mode
npm run dev

# Build all components for production
npm run build

# Run all tests
npm run test
```

### Component-Specific Commands

**Frontend (React Admin)**
```bash
cd frontend
npm run dev      # Start dev server on port 3000
npm run build    # Build for production
npm run test     # Run Cucumber E2E tests with Puppeteer
```

**Backend (Django API)**
```bash
cd backend
source venv/bin/activate                    # Activate virtual environment
python manage.py runserver 0.0.0.0:8000    # Start Django server
python manage.py test                       # Run Django unit tests
python manage.py migrate                    # Apply database migrations
celery -A worker worker -l info            # Start Celery worker (for async tasks)
```

**JS Component (Svelte Widget)**
```bash
cd js_component
npm run dev      # Start dev server
npm run build    # Build production bundle
npm run release  # Build and copy to releases directory
```

### Docker Commands
```bash
docker-compose up --build    # Build and start all containers
docker-compose down          # Stop all containers
docker-compose down -v       # Stop and remove volumes (reset DB)
```

## Architecture & Key Concepts

### Multi-Tenant Structure
- **User Types**: AIDA Admin, Business Admin, Business User
- **Subscription Tiers**: Basic (free), Pro ($79/month), Premium ($149/month)
- Each business has isolated data with role-based permissions

### API Structure
- `/api/v1/admin/` - Admin interface endpoints (JWT auth)
- `/api/v1/public/` - Public endpoints for widgets (API key auth)
- All API requests go through Kong API Gateway on port 8080

### Key Technologies
- **Frontend**: React 19, Vite, Tailwind CSS, Shadcn UI, React Query
- **Backend**: Django 5.1, DRF, PostgreSQL, Celery, Redis
- **JS Component**: Svelte 3, Stripe.js, DOMPurify
- **Infrastructure**: Docker, Kong API Gateway, Nginx

### Important Services
1. **AnythingLLM Integration** - AI chat functionality (see backend/chats/views.py)
2. **Document Processing** - Async via Celery (see backend/tasks.py)
3. **Stripe Integration** - Payment processing in JS component
4. **Google OAuth** - User authentication

### Environment Configuration
Backend requires `.env` file with:
- Django secrets (DJANGO_SECRET_KEY, JWT_SECRET_KEY)
- Database credentials (POSTGRES_*)
- Email settings (EMAIL_*)
- Service URLs (ANYTHING_LLM_URL, FRONTEND_URL)
- API keys (GOOGLE_CLIENT_*, ANYTHING_LLM_API_KEY)

### Testing Strategy
- **Frontend**: Cucumber.js + Puppeteer for E2E tests
- **Backend**: Django unit tests
- **JS Component**: No test configuration found

### Known Architecture Notes
- Team member limits are enforced only in frontend (not backend)
- Widget customization is per-business account
- Document processing uses Celery for async operations
- Kong API Gateway normalizes URL patterns

## Development Workflow

1. Always run `npm run install:all` after cloning
2. Use `npm run dev` to start all services together
3. Frontend runs on http://localhost:3000
4. Backend runs on http://localhost:8000
5. API Gateway runs on http://localhost:8080
6. Ensure Redis is running for Celery tasks

## File Organization

```
/aida
├── frontend/          # React admin interface
├── backend/           # Django API server
├── js_component/      # Svelte embeddable widget
├── api_gateway/       # Kong configuration
├── docker/            # Docker configurations
├── docs/              # Project documentation
└── test/              # Test files
```

When modifying the codebase:
- Admin UI changes go in `/frontend/src`
- API endpoints go in `/backend/[app_name]/views.py`
- Widget features go in `/js_component/src`
- Database models in `/backend/[app_name]/models.py`