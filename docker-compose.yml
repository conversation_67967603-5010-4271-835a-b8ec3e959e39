services:
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    depends_on:
      - backend

  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    volumes:
      - ./backend:/app
      - media_files:/app/media
      - static_files:/app/static
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_DB=rtai
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - DEBUG=True
      - SECRET_KEY=your-secret-key-here
    depends_on:
      - db

  kong:
    image: kong:latest
    container_name: kong
    environment:
      - KONG_DATABASE=off
      - KONG_DECLARATIVE_CONFIG=/usr/local/kong/declarative/kong.yml
      - KONG_PROXY_LISTEN=0.0.0.0:8000
      - KONG_ADMIN_LISTEN=0.0.0.0:8001
    env_file:
      - ./api_gateway/.env
    volumes:
      - ./kong.yml:/usr/local/kong/declarative/kong.yml
    ports:
      - "8080:8000"
      - "8081:8001"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - type: bind
        source: ./docker/nginx/nginx.conf
        target: /etc/nginx/nginx.conf
        read_only: true
    depends_on:
      - frontend
      - backend

  db:
    image: postgres:13-alpine
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=rtai
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
  media_files:
  static_files: 