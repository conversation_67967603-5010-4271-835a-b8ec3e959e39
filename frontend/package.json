{"type": "module", "name": "frontend", "version": "0.1.0", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview", "test": "./run-tests.sh"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@react-oauth/google": "^0.12.1", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.66.0", "@types/react-dropzone": "^4.2.2", "@types/react-router-dom": "^5.3.3", "antd": "^5.22.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.469.0", "next": "^15.1.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-router-dom": "^6.28.1", "shadcn": "^1.0.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@cucumber/cucumber": "^11.2.0", "@svgr/webpack": "^8.1.0", "@types/chai": "^5.0.1", "@types/node": "^22.10.5", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "chai": "^5.1.2", "postcss": "^8.4.49", "puppeteer": "^24.1.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.7.2", "vite": "^5.1.0", "vite-plugin-static-copy": "^2.2.0", "vite-plugin-svgr": "^4.3.0"}}