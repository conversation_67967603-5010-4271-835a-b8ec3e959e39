import axios from 'axios';
import { config } from '../config';

// We'll implement API interfaces when needed

// Store the current auth status to prevent circular redirects
let isRedirecting = false;

// Default API configuration
const api = axios.create({
  baseURL: config.API_BASE_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
});

// Add request interceptor to include authentication token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle authentication errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Only handle 401 errors if not already redirecting
    if (error.response && error.response.status === 401 && !isRedirecting) {
      // Check current location - don't redirect if already on login page
      if (!window.location.pathname.includes('/login')) {
        isRedirecting = true;
        
        // Only remove token if we're actually redirecting
        localStorage.removeItem('token');
        
        // Use a small timeout to prevent potential race conditions
        setTimeout(() => {
          // Redirect to login with session expired parameter
          window.location.href = '/login?session=expired';
          isRedirecting = false;
        }, 100);
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;
