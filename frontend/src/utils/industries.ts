/**
 * Centralized management of industry options for the application
 * 
 * This file provides a consistent way to handle industry values across
 * the frontend and their mapping to backend values.
 * 
 * - Backend values are stored in the `value` property (e.g., 'tech', 'food_service')
 * - User-facing display labels are stored in the `label` property (e.g., 'Technology', 'Food Service')
 */

export interface IndustryOption {
  value: string;
  label: string;
}

/**
 * Master list of industry options.
 * All components should use this list to ensure consistency across the application.
 */
export const INDUSTRY_OPTIONS: IndustryOption[] = [
  { value: 'tech', label: 'Technology' },
  { value: 'finance', label: 'Finance' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'education', label: 'Education' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'food_service', label: 'Food Service' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'hospitality', label: 'Hospitality' },
  { value: 'restaurant', label: 'Restaurant / Dining' },
  { value: 'other', label: 'Other' }
];

/**
 * Get the display label for a given industry value
 * @param value - The backend value (e.g., 'tech')
 * @returns The user-facing label (e.g., 'Technology')
 */
export function getIndustryLabel(value: string): string {
  // Handle empty values
  if (!value) return '';
  
  // Check if the value is already a comma-separated list
  if (value.includes(',')) {
    return value
      .split(',')
      .map(val => getIndustryLabel(val.trim()))
      .filter(Boolean)
      .join(', ');
  }

  // Find the corresponding option
  const option = INDUSTRY_OPTIONS.find(opt => opt.value === value.trim());
  
  // Return the label if found, otherwise provide a properly capitalized fallback
  return option?.label || capitalizeWords(value.replace(/_/g, ' '));
}

/**
 * Parse a comma-separated string of industry values into an array of values
 * @param industryString - A comma-separated string of industry values
 * @returns Array of industry values
 */
export function parseIndustryValues(industryString: string): string[] {
  if (!industryString) return [];
  return industryString.split(',').map(val => val.trim()).filter(Boolean);
}

/**
 * Helper function to properly capitalize words
 */
function capitalizeWords(str: string): string {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
} 