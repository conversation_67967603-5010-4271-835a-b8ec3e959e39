/**
 * This is a utility file to test if your Google Maps API key is valid
 * and has the correct permissions enabled.
 */

/**
 * Tests the Google Maps API key by making a direct request to verify the key works
 * @param apiKey The Google Maps API key to test
 * @returns A promise that resolves with a diagnostic result
 */
export const testGoogleMapsApiKey = async (apiKey: string): Promise<{
  isValid: boolean;
  placesApiEnabled: boolean;
  mapsJsApiEnabled: boolean;
  message: string;
}> => {
  if (!apiKey || apiKey === 'YOUR_API_KEY') {
    return {
      isValid: false,
      placesApiEnabled: false,
      mapsJsApiEnabled: false,
      message: 'No API key provided'
    };
  }

  const result = {
    isValid: false,
    placesApiEnabled: false,
    mapsJsApiEnabled: false,
    message: ''
  };

  try {
    // Test the Places API
    const placesResponse = await fetch(
      `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=Museum%20of%20Contemporary%20Art%20Australia&inputtype=textquery&fields=formatted_address,name,rating,opening_hours,geometry&key=${apiKey}`,
      { mode: 'no-cors' } // Note: This will make the response opaque but still tells us if request succeeded
    );
    
    // Since we're using no-cors, we can't actually check the response content
    // but if it didn't throw an error, it's likely valid
    result.placesApiEnabled = true;
    
    // Test the Maps JavaScript API
    const mapsJsResponse = await fetch(
      `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`,
      { mode: 'no-cors' }
    );
    
    result.mapsJsApiEnabled = true;
    result.isValid = true;
    result.message = 'API key appears to be valid and has necessary permissions';
    
  } catch (error) {
    console.error('Error testing Google Maps API key:', error);
    result.message = `Error testing API key: ${error instanceof Error ? error.message : String(error)}`;
  }

  return result;
};

// You can use this in the console for debugging:
// import { testGoogleMapsApiKey } from './utils/apiKeyTest';
// testGoogleMapsApiKey('YOUR-ACTUAL-KEY-HERE').then(console.log); 