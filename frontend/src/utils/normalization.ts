/**
 * Utility functions for normalizing business names and addresses
 * to improve duplicate detection
 */

/**
 * Normalizes a business name by:
 * - Converting to lowercase
 * - Removing leading/trailing whitespace
 * - Removing special characters
 * - Standardizing spaces
 * - Removing common business suffixes
 * - Standardizing common abbreviations
 * - Removing articles (a, an, the)
 * 
 * @param name The business name to normalize
 * @returns Normalized business name
 */
export const normalizeBusinessName = (name: string): string => {
  if (!name) return '';
  
  let normalized = name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s\-&]/g, '') // Remove special characters except hyphens and ampersands
    .replace(/\s+/g, ' ') // Standardize spaces
    .replace(/\b(inc|llc|ltd|corp|corporation|incorporated|company|co|inc\.|llc\.|ltd\.|corp\.|co\.|pllc|pc|lp|llp|gmbh)\b/g, ''); // Remove common business suffixes
  
  // Replace common abbreviations
  normalized = normalized
    .replace(/\b&\b/g, 'and')
    .replace(/\bintl\b/g, 'international')
    .replace(/\bsvcs\b/g, 'services')
    .replace(/\bmfg\b/g, 'manufacturing')
    .replace(/\btech\b/g, 'technology');
  
  // Remove articles
  normalized = normalized
    .replace(/^the\s+/i, '')
    .replace(/^a\s+/i, '')
    .replace(/^an\s+/i, '');
  
  // Remove multiple spaces and trim again
  normalized = normalized.replace(/\s+/g, ' ').trim();
  
  return normalized;
};

/**
 * Calculates string similarity using Levenshtein distance
 * 
 * @param str1 First string
 * @param str2 Second string
 * @returns Similarity score between 0 and 1 (1 = exact match)
 */
export const calculateStringSimilarity = (str1: string, str2: string): number => {
  if (!str1 && !str2) return 1;
  if (!str1 || !str2) return 0;
  
  const len1 = str1.length;
  const len2 = str2.length;
  
  // Initialize matrix of size (len1+1) x (len2+1)
  const matrix: number[][] = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null));
  
  // Fill the first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  // Fill the rest of the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }
  
  // Calculate similarity (0 to 1)
  const maxLen = Math.max(len1, len2);
  if (maxLen === 0) return 1;
  
  return 1 - (matrix[len1][len2] / maxLen);
};

/**
 * Normalizes an address by:
 * - Converting to lowercase
 * - Removing leading/trailing whitespace
 * - Standardizing spaces
 * - Removing special characters except commas
 * - Removing unit/suite/apartment numbers
 * - Standardizing common address abbreviations
 * 
 * @param address The address to normalize
 * @returns Normalized address
 */
export const normalizeAddress = (address: string): string => {
  if (!address) return '';
  
  let normalized = address
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ');
  
  // Standardize address abbreviations
  const abbreviations: Record<string, string> = {
    'street': 'st',
    'st\\.': 'st',
    'avenue': 'ave',
    'ave\\.': 'ave',
    'boulevard': 'blvd',
    'blvd\\.': 'blvd',
    'road': 'rd',
    'rd\\.': 'rd',
    'drive': 'dr',
    'dr\\.': 'dr',
    'lane': 'ln',
    'ln\\.': 'ln',
    'court': 'ct',
    'ct\\.': 'ct',
    'circle': 'cir',
    'cir\\.': 'cir',
    'highway': 'hwy',
    'hwy\\.': 'hwy',
    'parkway': 'pkwy',
    'pkwy\\.': 'pkwy',
    'place': 'pl',
    'pl\\.': 'pl',
    'square': 'sq',
    'sq\\.': 'sq',
    'north': 'n',
    'n\\.': 'n',
    'south': 's',
    's\\.': 's',
    'east': 'e',
    'e\\.': 'e',
    'west': 'w',
    'w\\.': 'w',
    'northeast': 'ne',
    'ne\\.': 'ne',
    'northwest': 'nw',
    'nw\\.': 'nw',
    'southeast': 'se',
    'se\\.': 'se',
    'southwest': 'sw',
    'sw\\.': 'sw'
  };
  
  // Apply abbreviation standardization
  Object.entries(abbreviations).forEach(([full, abbr]) => {
    normalized = normalized.replace(new RegExp(`\\b${full}\\b`, 'gi'), abbr);
  });
  
  // Remove unit/apt numbers and special characters
  normalized = normalized
    .replace(/\b(suite|ste|apt|apartment|unit|#|no|number)\s*[\w\-]+\b/gi, '') // Remove unit/apt numbers
    .replace(/[^\w\s,]/g, '') // Remove special chars except commas
    .replace(/\s+/g, ' ') // Standardize spaces again
    .trim();
  
  return normalized;
};

/**
 * Checks if two business names are similar using both exact matching of normalized forms
 * and fuzzy matching with Levenshtein distance
 * 
 * @param name1 First business name
 * @param name2 Second business name
 * @param threshold Similarity threshold (0-1)
 * @returns True if names are similar
 */
export const isSimilarBusinessName = (name1: string, name2: string, threshold = 0.8): boolean => {
  const normalized1 = normalizeBusinessName(name1);
  const normalized2 = normalizeBusinessName(name2);
  
  // Exact match on normalized strings
  if (normalized1 === normalized2) return true;
  
  // Fuzzy match using Levenshtein distance
  const similarity = calculateStringSimilarity(normalized1, normalized2);
  return similarity >= threshold;
};

/**
 * Checks if two addresses are similar using both exact matching of normalized forms
 * and fuzzy matching with Levenshtein distance
 * 
 * @param address1 First address
 * @param address2 Second address
 * @param threshold Similarity threshold (0-1)
 * @returns True if addresses are similar
 */
export const isSimilarAddress = (address1: string, address2: string, threshold = 0.8): boolean => {
  const normalized1 = normalizeAddress(address1);
  const normalized2 = normalizeAddress(address2);
  
  // Exact match on normalized strings
  if (normalized1 === normalized2) return true;
  
  // Fuzzy match using Levenshtein distance
  const similarity = calculateStringSimilarity(normalized1, normalized2);
  return similarity >= threshold;
}; 