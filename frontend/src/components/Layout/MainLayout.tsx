import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { UserType } from '../../types/user';
import { useState, useEffect } from 'react';
import api from '../../utils/api';
import { Logo } from '@/components/ui/Logo';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ 
  children
}: MainLayoutProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, user, userType, logout } = useAuth();
  const year = new Date().getFullYear();
  const [businessName, setBusinessName] = useState("");
  const [subscriptionTier, setSubscriptionTier] = useState("Basic");
  const [currentAccountId, setCurrentAccountId] = useState<string | null>(null);

  useEffect(() => {
    // Fetch business name and subscription tier when user is authenticated
    if (isAuthenticated && user) {
      const fetchAccountData = async () => {
        try {
          const response = await api.get('/api/accounts/');
          if (response.data?.length > 0) {
            const account = response.data[0];
            setBusinessName(account.account_name || "Your Business");
            
            // Set subscription tier (capitalize first letter)
            const tier = account.subscription_tier || 'basic';
            setSubscriptionTier(tier.charAt(0).toUpperCase() + tier.slice(1));
            
            // Store the current account ID for use in navigation
            setCurrentAccountId(account.id.toString());
          }
        } catch (error) {
          console.error('Failed to fetch account data:', error);
          setBusinessName("Your Business");
        }
      };
      
      fetchAccountData();
    }
  }, [isAuthenticated, user]);

  // Define menu items with user type and group restrictions
  const menuItems = [
    {
      key: '/',
      label: 'Home',
      path: '/',
      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER],
      allowedGroups: ['Business Admins', 'Business Regular Users'],
    },
    {
      key: '/my-business',
      label: 'My Business',
      path: '/my-business',
      allowedTypes: [UserType.BUSINESS_USER],
      allowedGroups: ['Business Regular Users'],
      regularUserOnly: true,
    },
    /* Hide API Check from navigation menu
    {
      key: '/api-check',
      label: 'API Check',
      path: '/api-check',
      allowedTypes: [UserType.BUSINESS_USER],
      allowedGroups: ['Business Regular Users'],
      regularUserOnly: true,
    },
    */
    {
      key: '/accounts',
      label: 'Accounts',
      path: '/accounts',
      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN],
      allowedGroups: ['Business Admins'],
      requireAdmin: true,
    },
    {
      key: '/users',
      label: 'Users',
      path: '/users',
      allowedTypes: [UserType.AIDA_ADMIN],
      requireSuperuser: true,
    },
    {
      key: '/subscription',
      label: 'Subscription',
      path: '/subscription',
      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER],
      allowedGroups: ['Business Admins', 'Business Regular Users'],
    },
    {
      key: '/documents',
      label: 'Documents',
      path: currentAccountId ? `/documents/${currentAccountId}` : '/documents',
      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN, UserType.BUSINESS_USER],
      allowedGroups: ['Business Admins', 'Business Regular Users'],
    },
    {
      key: '/setup',
      label: 'Setup',
      path: '/setup',
      allowedTypes: [UserType.AIDA_ADMIN],
      requireSuperuser: true,
    },
  ];

  // Filter menu items based on auth state and groups
  const visibleMenuItems = menuItems.filter(item => {
    // If not authenticated, only show public routes
    if (!isAuthenticated && !item.publicRoute) return false;
    
    // If there's no user, only show public routes
    if (!user) return item.publicRoute;
    
    // Superuser specific logic
    if (user.is_superuser) {
      // Superusers don't see regular user items
      if (item.regularUserOnly) return false;
      
      // Superusers see all other items
      return true;
    }
    
    // Regular user checks
    if (item.requireSuperuser) return false;
    
    // Regular business users see their specific routes
    if (item.regularUserOnly) {
      return user.groups.includes('Business Regular Users') && 
             !user.groups.includes('Business Admins');
    }
    
    // Admin-only route check
    if (item.requireAdmin && !user.groups.includes('Business Admins')) return false;
    
    // Check if user's groups match any of the allowed groups
    return item.allowedGroups?.some(group => user.groups.includes(group)) || item.publicRoute;
  });

  return (
    <div className="min-h-full flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <Button 
            variant="ghost" 
            className="p-0 h-auto hover:bg-transparent"
            onClick={() => navigate('/')}
          >
            <Logo size="md" />
          </Button>
          <div className="flex items-center gap-4">
            {isAuthenticated ? (
              <>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost"
                    className="text-muted-foreground hover:text-primary"
                    onClick={() => navigate('/settings')}
                  >
                    {user?.name || 'User'}
                  </Button>
                  {userType && (
                    <Button
                      variant="ghost"
                      className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                      onClick={() => navigate('/subscription')}
                    >
                      {businessName || "Your Business"} • {subscriptionTier}
                    </Button>
                  )}
                </div>
                <Button 
                  variant="ghost"
                  onClick={logout}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button 
                  variant="ghost"
                  onClick={() => navigate('/login')}
                >
                  Login
                </Button>
                <Button
                  onClick={() => navigate('/register')}
                >
                  Register
                </Button>
              </>
            )}
          </div>
        </div>
        <nav className="border-t bg-muted">
          <div className="container mx-auto px-4">
            <div className="flex space-x-4 h-12">
              {visibleMenuItems.map((item) => (
                <Button
                  key={item.key}
                  variant="ghost"
                  onClick={() => navigate(item.path)}
                  className={`h-12 px-3 text-sm font-medium hover:bg-transparent ${
                    location.pathname === item.path
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-primary'
                  }`}
                >
                  {item.label}
                </Button>
              ))}
            </div>
          </div>
        </nav>
      </header>
      
      <main className="flex-1 bg-muted/50">
        <div className="container mx-auto px-4 py-8">
          {children}
        </div>
      </main>

      <footer className="border-t bg-muted py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-muted-foreground">
              © {year} AIDA. All rights reserved.
            </div>
            <div className="flex space-x-6">
              <Button
                variant="ghost"
                className="text-sm text-muted-foreground hover:text-primary"
                onClick={() => navigate('/about')}
              >
                About
              </Button>
              <Button
                variant="ghost"
                className="text-sm text-muted-foreground hover:text-primary"
                onClick={() => navigate('/privacy')}
              >
                Privacy Policy
              </Button>
              <Button
                variant="ghost"
                className="text-sm text-muted-foreground hover:text-primary"
                onClick={() => navigate('/legal')}
              >
                Legal
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout; 