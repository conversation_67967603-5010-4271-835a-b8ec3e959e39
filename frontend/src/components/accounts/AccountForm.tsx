import React, { useState, useImperativeHandle, forwardRef, useEffect } from 'react';
import { Form, Input, Select, Button, message, Row, Col, Tag, Alert, Tooltip, Modal } from 'antd';
import { SyncOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Account } from '../../types/account';
import api from '../../utils/api';
import { INDUSTRY_OPTIONS, getIndustryLabel, parseIndustryValues } from '../../utils/industries';

const { Option } = Select;

// Industry options with proper capitalization for display
// const INDUSTRY_OPTIONS = [ ... ];

// Function to get display label for industry value
// export const getIndustryLabel = (value: string): string => { ... };

interface AccountFormProps {
  initialValues?: Account;
  onSubmit: (values: Account) => void;
  onCancel?: () => void;
  loading?: boolean;
  isAdmin?: boolean; // New prop to control admin-only fields
}

// Export the form handle type for external use
export interface AccountFormHandle {
  form: any; // Ant Design form instance
  setFieldsValue: (values: any) => void;
  getFieldsValue: () => Account;
  validateFields: () => Promise<Account>;
  resetFields: () => void;
}

// DomainHelpModal component to explain domains to users
const DomainHelpModal = () => {
  const [isVisible, setIsVisible] = useState(false);
  
  return (
    <>
      <InfoCircleOutlined 
        className="cursor-pointer text-blue-500" 
        onClick={() => setIsVisible(true)}
      />
      
      <Modal
        title="What is a domain?"
        open={isVisible}
        onCancel={() => setIsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsVisible(false)}>
            Got it
          </Button>
        ]}
        width={600}
      >
        <div className="space-y-4">
          <p>
            A <strong>domain</strong> is the address people type in their web browser to visit your business website. 
            For example, "amazon.com" or "starbucks.com".
          </p>
          
          <div className="bg-gray-50 p-4 rounded-md">
            <h4 className="font-medium mb-2">Examples:</h4>
            <ul className="list-disc pl-5 space-y-1">
              <li><strong>mybusiness.com</strong> - A typical business domain</li>
              <li><strong>shop.mybusiness.com</strong> - A subdomain for an online store</li>
              <li><strong>mybusiness.org</strong> - A domain for a non-profit organization</li>
            </ul>
          </div>
          
          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Don't have a website?</h4>
            <p>
              If your business doesn't have a website yet, you can use our placeholder option.
              This will generate a temporary domain for your account that you can update later.
            </p>
          </div>
        </div>
      </Modal>
    </>
  );
};

const AccountForm = forwardRef<AccountFormHandle, AccountFormProps>(({
  initialValues,
  onSubmit,
  onCancel,
  loading = false,
  isAdmin = false, // Default to regular user (non-admin)
}, ref) => {
  const [form] = Form.useForm();
  const [syncing, setSyncing] = useState(false);
  
  console.log('AccountForm received initialValues:', initialValues);

  // Expose form methods via ref
  useImperativeHandle(ref, () => ({
    form,
    setFieldsValue: (values: any) => form.setFieldsValue(values),
    getFieldsValue: () => form.getFieldsValue() as Account,
    validateFields: () => form.validateFields() as Promise<Account>,
    resetFields: () => form.resetFields(),
  }));

  const handleWorkspaceSync = async () => {
    if (!initialValues?.id) return;

    setSyncing(true);
    try {
      // Use direct API calls instead of client methods that don't exist
      const endpoint = `/api/accounts/${initialValues.id}/create_workspace/`;
      const response = await api.post(endpoint);

      const data = response.data;
      message.success(initialValues.llm_slug
        ? 'Workspace updated successfully'
        : 'Workspace created successfully'
      );

      // Update the form values
      form.setFieldsValue({
        llm_id: data.workspace_id,
        llm_slug: data.workspace_slug,
      });
    } catch (error) {
      message.error('Failed to sync workspace: ' + (error as Error).message);
    } finally {
      setSyncing(false);
    }
  };

  // Transform initial values to match form structure
  const transformedInitialValues = React.useMemo(() => {
    if (!initialValues) return undefined;
    
    console.log('Computing transformed values from:', initialValues);
    return {
      ...initialValues,
      domain: initialValues.websites?.[0]?.domain || '',
    };
  }, [initialValues]);
  
  console.log('Transformed form initialValues:', transformedInitialValues);
  
  // Log when the component is rendering
  console.log('AccountForm rendering with props:', { 
    hasInitialValues: !!initialValues,
    transformedValues: transformedInitialValues 
  });

  // Reset form with initialValues when the key prop changes
  useEffect(() => {
    if (transformedInitialValues) {
      console.log('Form reset due to key change with values:', transformedInitialValues);
      form.resetFields();
    }
  }, [form]);
  
  // Set initialValues directly on mount and when they change
  useEffect(() => {
    if (initialValues) {
      console.log('Setting initialValues directly to form:', transformedInitialValues);
      form.setFieldsValue(transformedInitialValues);
    }
  }, [form, initialValues, transformedInitialValues]);

  // Additional effect to clean up empty industry values that might get added
  useEffect(() => {
    // Add a small delay to ensure form is fully initialized
    const timeoutId = setTimeout(() => {
      const formValues = form.getFieldsValue();
      if (Array.isArray(formValues.industry_name)) {
        // Filter out any empty values
        const filteredIndustries = formValues.industry_name.filter(
          (value: string) => value && value.trim() !== ''
        );
        
        // Only update if there's a difference (to avoid infinite loops)
        if (filteredIndustries.length !== formValues.industry_name.length) {
          form.setFieldsValue({ 
            industry_name: filteredIndustries.length > 0 ? filteredIndustries : undefined 
          });
        }
      }
    }, 100);
    
    return () => clearTimeout(timeoutId);
  }, [form, initialValues]);

  // Custom filter function to enforce no empty tags
  const filterEmptyTags = (value: any) => {
    if (Array.isArray(value)) {
      return value.filter(item => item && typeof item === 'string' && item.trim() !== '');
    }
    return value;
  };

  // Custom normalize function for the industry field
  const normalizeIndustry = (value: any) => {
    return filterEmptyTags(value);
  };

  // Update the onFinish function (if it exists) to handle industry values properly
  const onFinish = (values: any) => {
    try {
      console.log('Form values on submit:', values);
      
      // Validate domain format - placeholder domains are acceptable
      const domain = values.domain || '';
      const isPlaceholderDomain = domain.includes('.placeholder.com');
      
      if (!isPlaceholderDomain && !domain.match(/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/)) {
        message.error('Please enter a valid domain!');
        return;
      }
      
      // Format industry_name if it's an array
      if (Array.isArray(values.industry_name)) {
        // Filter out empty values
        const filteredIndustries = values.industry_name.filter((industry: string) => 
          industry && industry.trim() !== ''
        );
        values.industry_name = filteredIndustries.join(', ');
      }
      
      // Create websites array from domain
      const websites = values.domain ? [{
        domain: values.domain,
        url_patterns: values.url_patterns || ''
      }] : [];
      
      // Remove domain from values since it's not a direct field on the Account type
      delete values.domain;
      delete values.url_patterns;
      
      // Add websites array to values
      values.websites = websites;
      
      onSubmit(values);
    } catch (error) {
      console.error('Error in form submission:', error);
      message.error('There was an error submitting the form. Please try again.');
    }
  };

  // When initializing form values from backend data
  useEffect(() => {
    if (initialValues) {
      // Convert comma-separated industry string to array for the form
      const formValues = { ...initialValues };
      if (typeof formValues.industry_name === 'string' && formValues.industry_name) {
        // Parse the industry values and filter out any empty strings
        formValues.industry_name = parseIndustryValues(formValues.industry_name)
          .filter(value => value.trim() !== '');
      } else if (Array.isArray(formValues.industry_name)) {
        // Filter any empty values from arrays as well
        formValues.industry_name = formValues.industry_name.filter(value => 
          value && typeof value === 'string' && value.trim() !== ''
        );
      }
      
      // If we end up with an empty array, set it to undefined to prevent validation errors
      if (Array.isArray(formValues.industry_name) && formValues.industry_name.length === 0) {
        formValues.industry_name = [] as string[];
      }
      
      form.setFieldsValue(formValues);
    }
  }, [initialValues, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={transformedInitialValues}
      className="max-w-2xl"
    >
      {/* Debug info - hidden but kept for future development needs */}
      {import.meta.env.MODE !== 'production' && (
        <div className="mb-4 p-2 border border-gray-200 rounded bg-gray-50 text-xs overflow-auto max-h-40" style={{ display: 'none' }}>
          <details>
            <summary className="font-bold cursor-pointer">Form Debug Info</summary>
            <div className="mt-2">
              <p><strong>Has initialValues:</strong> {initialValues ? 'Yes' : 'No'}</p>
              <p><strong>Account ID:</strong> {initialValues?.id}</p>
              <p><strong>Account Name:</strong> {initialValues?.account_name}</p>
              <p><strong>Domain:</strong> {transformedInitialValues?.domain}</p>
              <button 
                className="text-blue-500 hover:text-blue-700 mt-2 text-xs"
                onClick={(e) => {
                  e.preventDefault();
                  if (form) {
                    console.log('Current form values:', form.getFieldsValue());
                  }
                }}
              >
                Log current form values
              </button>
            </div>
          </details>
        </div>
      )}
      
      <div className="mb-4">
        <pre className="text-xs text-gray-500 hidden">Debug: {JSON.stringify(transformedInitialValues, null, 2)}</pre>
      </div>
      {/* Render status field conditionally based on isAdmin or show status message */}
      {isAdmin ? (
        <Form.Item
          label="Status"
          name="status"
          rules={[{ required: true, message: 'Please select status!' }]}
        >
          <Select>
            <Option value="active">Active</Option>
            <Option value="inactive">Inactive</Option>
            <Option value="pending">Pending</Option>
            <Option value="suspended">Suspended</Option>
          </Select>
        </Form.Item>
      ) : (
        <>
          <Form.Item
            name="status"
            style={{ display: 'none' }}
          >
            <Input type="hidden" />
          </Form.Item>
          
          {initialValues?.status && initialValues.status !== 'active' && (
            <Form.Item>
              <Alert
                type="warning"
                message={`Account Status: ${initialValues.status.charAt(0).toUpperCase() + initialValues.status.slice(1)}`}
                description={
                  <div>
                    <p>Your account is currently not active. This may affect your ability to use all features.</p>
                    <p>If you believe this is an error or need assistance, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                  </div>
                }
                showIcon
              />
            </Form.Item>
          )}
        </>
      )}

      <Form.Item
        label="Account Name"
        name="account_name"
        rules={[{ required: true, message: 'Please input account name!' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Industry"
        name="industry_name"
        rules={[{ required: true, message: 'Please select at least one industry' }]}
        tooltip="Select one or more industries that best describe this business"
        normalize={normalizeIndustry}
      >
        <Select
          mode="tags"
          style={{ width: '100%' }}
          options={INDUSTRY_OPTIONS}
          allowClear
          tokenSeparators={[',']}
          notFoundContent={null}
          onChange={(value) => {
            // Filter out empty values
            const filtered = filterEmptyTags(value);
            if (JSON.stringify(filtered) !== JSON.stringify(value)) {
              // If there's a difference, update the form field
              form.setFieldsValue({ industry_name: filtered });
            }
          }}
          tagRender={(props) => {
            // Only render non-empty tags with content or just hide empty ones with display: none
            if (!props.value || props.value === '') {
              // Return an empty div instead of null to satisfy the type requirements
              return <div style={{ display: 'none' }}></div>;
            }
            return (
              <Tag 
                closable={props.closable}
                onClose={props.onClose}
                className="ant-select-selection-item"
                style={{ marginRight: 3 }}
              >
                {props.label || props.value}
              </Tag>
            );
          }}
          filterOption={(input, option) =>
            (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
          }
          // Prevent empty values from being added
          onSelect={(value) => {
            if (!value || value === '') {
              return;
            }
          }}
        >
          {/* No need for manual options as we're using the options prop */}
        </Select>
      </Form.Item>

      <Form.Item
        label="Domain"
        name="domain"
        tooltip={{
          title: "A domain is the website address for your business (e.g., 'mybusiness.com'). If your business doesn't have a website, you can use the option below.",
          overlayStyle: { maxWidth: '300px' }
        }}
        rules={[
          { required: true, message: 'Please input domain!' },
          { 
            validator: async (_, value) => {
              if (!value) return Promise.reject('Please input domain!');
              
              // Allow placeholder domains
              if (value.includes('.placeholder.com')) {
                return Promise.resolve();
              }
              
              // Validate regular domains
              if (!/^(?!:\/\/)([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/.test(value)) {
                return Promise.reject('Please enter a valid domain!');
              }
              
              return Promise.resolve();
            }
          }
        ]}
        extra={
          <div className="mt-2">
            <span 
              className="cursor-pointer text-blue-600 hover:text-blue-800"
              onClick={() => {
                // Generate a default domain based on business name
                const businessName = form.getFieldValue('account_name') || '';
                const defaultDomain = businessName
                  ? `${businessName.toLowerCase().replace(/[^a-z0-9]/g, '')}.placeholder.com`
                  : 'business.placeholder.com';
                
                form.setFieldsValue({ domain: defaultDomain });
              }}
            >
              My business doesn't have a website
            </span>
            <span className="text-gray-400 text-xs ml-2">
              (Will use a placeholder domain)
            </span>
          </div>
        }
      >
        <Input
          placeholder="example.com"
          addonBefore={isAdmin ? 
            <Select 
              defaultValue="http://" 
              className="w-[90px]"
              options={[
                { value: 'http://', label: 'http://' },
                { value: 'https://', label: 'https://' },
              ]}
            /> : 
            "www."}
          addonAfter={<DomainHelpModal />}
        />
      </Form.Item>

      {/* Contact Information */}
      <Form.Item
        label="Primary Contact"
        name="primary_contact"
        rules={[{ required: true, message: 'Please input primary contact!' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Primary Phone"
        name="primary_phone"
        rules={[{ required: true, message: 'Please input primary phone!' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Secondary Phone"
        name="secondary_phone"
      >
        <Input />
      </Form.Item>

      {/* Address Information */}
      <Form.Item
        label="Address Line 1"
        name="address_line1"
        rules={[{ required: true, message: 'Please enter address line 1' }]}
      >
        <Input id="address_line1" placeholder="123 Main St" />
      </Form.Item>

      <Form.Item
        label="Address Line 2"
        name="address_line2"
      >
        <Input id="address_line2" placeholder="Suite 100" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label="City"
            name="city"
            rules={[{ required: true, message: 'Please enter city' }]}
          >
            <Input id="city" placeholder="San Francisco" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="State"
            name="state"
            rules={[{ required: true, message: 'Please enter state' }]}
          >
            <Input id="state" placeholder="CA" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="ZIP Code"
            name="zip_code"
            rules={[{ required: true, message: 'Please enter ZIP code' }]}
          >
            <Input id="zip_code" placeholder="94102" />
          </Form.Item>
        </Col>
      </Row>

      {/* Hidden fields for LLM data */}
      <Form.Item name="llm_id" hidden>
        <Input />
      </Form.Item>
      <Form.Item name="llm_slug" hidden>
        <Input />
      </Form.Item>

      <div className="flex gap-4">
        <Button type="primary" htmlType="submit" loading={loading}>
          Save
        </Button>

        {initialValues?.id ? (
          <Button
            type="default"
            icon={<SyncOutlined />}
            onClick={handleWorkspaceSync}
            loading={syncing}
          >
            {initialValues.llm_slug ? 'Update AnythingLLM Workspace' : 'Create AnythingLLM Workspace'}
          </Button>
        ) : null}

        {onCancel && (
          <Button onClick={onCancel}>
            Cancel
          </Button>
        )}

      </div>
    </Form>
  );
});

AccountForm.displayName = 'AccountForm';

export default AccountForm;
