import React from 'react';
import { Card, Descriptions, Button, Space } from 'antd';
import { EditOutlined, PlusOutlined, CodeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Account } from '../../types/account';
import { getIndustryLabel } from '../../utils/industries';

interface AccountViewProps {
  account: Account;
}

const AccountView: React.FC<AccountViewProps> = ({ account }) => {
  const navigate = useNavigate();
  
  // Get the first website domain if available
  const domain = account.websites?.[0]?.domain;
  
  // Get URL patterns from the first website if available
  const urlPatterns = account.websites?.[0]?.url_patterns;

  return (
    <Card
      title="Account Details"
      extra={
        <Space>
          <Button 
            icon={<CodeOutlined />}
            onClick={() => navigate(`/setup?accountId=${account.id}`)}
            type="primary"
          >
            Setup Widget
          </Button>
          <Button 
            icon={<PlusOutlined />} 
            onClick={() => navigate('/accounts/add')}
          >
            Add Account
          </Button>
          <Button 
            icon={<EditOutlined />} 
            onClick={() => navigate(`/accounts/${account.id}/edit`)}
          >
            Edit
          </Button>
        </Space>
      }
    >
      <Descriptions column={2}>
        <Descriptions.Item label="Name">{account.account_name}</Descriptions.Item>
        <Descriptions.Item label="Status">{account.status}</Descriptions.Item>
        <Descriptions.Item label="Domain">{domain}</Descriptions.Item>
        <Descriptions.Item label="Industry">{typeof account.industry_name === 'string' ? getIndustryLabel(account.industry_name) : Array.isArray(account.industry_name) ? account.industry_name.map(getIndustryLabel).join(', ') : ''}</Descriptions.Item>
        <Descriptions.Item label="Primary Contact">{account.primary_contact}</Descriptions.Item>
        <Descriptions.Item label="Primary Phone">{account.primary_phone}</Descriptions.Item>
        {account.primary_email && (
          <Descriptions.Item label="Primary Email">{account.primary_email}</Descriptions.Item>
        )}
        <Descriptions.Item label="Address">
          {account.address_line1}
          {account.address_line2 && <br />}
          {account.address_line2}
          <br />
          {account.city}, {account.state} {account.zip_code}
        </Descriptions.Item>
        {urlPatterns && (
          <Descriptions.Item label="URL Patterns">
            <pre>{urlPatterns}</pre>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );
};

export default AccountView; 