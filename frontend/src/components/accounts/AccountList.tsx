import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, message } from 'antd';
import type { Breakpoint } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined, CodeOutlined, FileOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Account } from '../../types/account';
import api from '../../utils/api';
import { useWindowSize } from '../../hooks/useWindowSize';
import { useAuth } from '../../contexts/AuthContext';
import { UserType } from '../../types/user';
import { getIndustryLabel } from '../../utils/industries';

interface AccountListProps {
  accounts: Account[];
  loading: boolean;
  onDelete: (id: number) => void;
}

const AccountList: React.FC<AccountListProps> = ({ accounts, loading, onDelete }) => {
  const navigate = useNavigate();
  const { user, userType } = useAuth();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<Account | null>(null);
  const { width } = useWindowSize();
  const isMobile = width < 768;

  // Responsive columns configuration
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'Name',
        dataIndex: 'account_name',
        key: 'account_name',
        fixed: isMobile ? undefined : 'left' as const,
        width: isMobile ? undefined : 200,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        responsive: ['md' as Breakpoint],
      },
      {
        title: 'Domain',
        dataIndex: ['websites', 0, 'domain'],
        key: 'domain',
        render: (_: any, record: Account) => record.websites?.[0]?.domain || '-',
        responsive: ['lg' as Breakpoint],
      },
      {
        title: 'Industry',
        dataIndex: 'industry_name',
        key: 'industry_name',
        responsive: ['md' as Breakpoint],
        render: (industry_name: string | string[]) => {
          if (!industry_name) return '-';
          return typeof industry_name === 'string' 
            ? getIndustryLabel(industry_name) 
            : Array.isArray(industry_name) 
              ? industry_name.map(getIndustryLabel).join(', ') 
              : '-';
        }
      },
      {
        title: 'Actions',
        key: 'actions',
        fixed: isMobile ? undefined : 'right' as const,
        width: isMobile ? undefined : 360,
        render: (_: any, record: Account) => (
          <Space wrap={isMobile}>
            <Button
              type="primary"
              icon={<CodeOutlined />}
              onClick={() => navigate(`/setup?accountId=${record.id}`)}
              size={isMobile ? 'middle' : undefined}
            >
              {isMobile ? '' : 'Setup Widget'}
            </Button>
            <Button
              icon={<FileOutlined />}
              onClick={() => navigate(`/accounts/${record.id}/documents`)}
              size={isMobile ? 'middle' : undefined}
            >
              {isMobile ? '' : 'Documents'}
            </Button>
            <Button
              icon={<EditOutlined />}
              onClick={() => navigate(`/accounts/${record.id}/edit`)}
              size={isMobile ? 'middle' : undefined}
            >
              {isMobile ? '' : 'Edit'}
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => showDeleteConfirm(record)}
              size={isMobile ? 'middle' : undefined}
            >
              {isMobile ? '' : 'Delete'}
            </Button>
          </Space>
        ),
      },
    ];

    return baseColumns;
  };

  const showDeleteConfirm = (account: Account) => {
    setAccountToDelete(account);
    setDeleteModalVisible(true);
  };

  const handleDelete = async () => {
    if (!accountToDelete) return;
    
    try {
      await api.delete(`/api/accounts/${accountToDelete.id}/`);
      message.success('Account deleted successfully');
      onDelete(accountToDelete.id);
    } catch (error) {
      message.error('Failed to delete account');
    } finally {
      setDeleteModalVisible(false);
      setAccountToDelete(null);
    }
  };

  return (
    <div className="responsive-container">
      <div className="header-actions" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
        {/* Only show Add Account button for AIDA Admin and Business Admin */}
        {userType && [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN].includes(userType) && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/accounts/add')}
          >
            {isMobile ? '' : 'Add Account'}
          </Button>
        )}
      </div>

      <div className="table-container" style={{ width: '100%', overflowX: 'auto' }}>
        <Table
          columns={getColumns()}
          dataSource={accounts}
          loading={loading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          size={isMobile ? 'small' : 'middle'}
          pagination={{
            responsive: true,
            position: ['bottomCenter'],
            size: isMobile ? 'small' : 'default',
          }}
        />
      </div>

      <Modal
        title="Delete Account"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setAccountToDelete(null);
        }}
        okText="Delete"
        cancelText="Cancel"
      >
        <p>
          Are you sure you want to delete account "{accountToDelete?.account_name}"?
          This action cannot be undone.
        </p>
      </Modal>
    </div>
  );
};

export default AccountList; 