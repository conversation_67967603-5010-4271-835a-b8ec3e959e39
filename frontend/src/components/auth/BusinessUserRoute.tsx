import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Result } from 'antd';
import { UserType } from '../../types/user';

interface BusinessUserRouteProps {
  children: React.ReactNode;
  fallbackPath?: string;
}

/**
 * A route wrapper that handles access control for business users.
 * Regular business users who try to access restricted areas will be redirected to the fallback path.
 * Business admins and AIDA admins can access all routes.
 */
const BusinessUserRoute: React.FC<BusinessUserRouteProps> = ({ 
  children, 
  fallbackPath = '/' 
}) => {
  const { isAuthenticated, user, userType } = useAuth();

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" />;
  }

  // Allow business admins and AIDA admins to access all routes
  if (user.is_superuser || user.groups.includes('Business Admins')) {
    return <>{children}</>;
  }

  // Block regular business users from accessing restricted routes
  if (userType === UserType.BUSINESS_USER) {
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
};

export default BusinessUserRoute; 