import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import api from '../../utils/api';
import { testGoogleMapsApiKey } from '../../utils/apiKeyTest';
import { INDUSTRY_OPTIONS, getIndustryLabel } from '../../utils/industries';
import { normalizeBusinessName, normalizeAddress } from '../../utils/normalization';
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { MultiSelect } from "../../components/ui/multi-select"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Info, MapPin, CheckCircle2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

// Define the Google Places API types
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          Autocomplete: new (input: HTMLInputElement, options?: any) => any;
        };
        event: {
          addListener: (instance: any, eventName: string, callback: Function) => void;
          removeListener: (listener: any) => void;
          clearInstanceListeners: (instance: any) => void;
        };
      };
    };
    initGoogleMapsAutocomplete?: () => void;
  }
}

interface BusinessForm {
  business_name: string;
  address_line1: string;
  address_line2: string;
  city: string;
  state: string;
  zip_code: string;
  industry: string[];
  company_phone: string;
  company_email: string;
  website: string;
}

interface AddressErrors {
  address_line1?: string;
  city?: string;
  state?: string;
  zip_code?: string;
}

const US_STATES = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
  { value: 'DC', label: 'District of Columbia' },
];

// Function to load Google Maps API
const loadGoogleMapsScript = () => {
  // Remove debug environment variables logging
  
  // Prevent duplicate loading - check for both the script tag and global google object
  if (
    document.querySelector('script[src*="maps.googleapis.com/maps/api/js"]') ||
    (window.google && window.google.maps && window.google.maps.places)
  ) {
    // If script is already loaded but google.maps is not fully initialized yet,
    // we'll set up our initialization to run once it's ready
    if (window.google && !window.google.maps) {
      // Remove console log
    } else {
      // If already fully loaded, initialize immediately
      if (window.initGoogleMapsAutocomplete) {
        window.initGoogleMapsAutocomplete();
      }
    }
    return;
  }
  
  // Create script element with error handling
  const googleScript = document.createElement('script');
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
  
  // If no API key is provided, we'll skip loading and just use manual entry
  if (!apiKey || apiKey === 'YOUR_API_KEY') {
    // Remove console warn
    return;
  }
  
  // Add the script loading with retry logic
  const loadScript = () => {
    // Use recommended loading pattern with async
    googleScript.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMapsAutocomplete&loading=async`;
    googleScript.async = true;
    googleScript.defer = true;
    
    // Add error handling
    googleScript.onerror = (error) => {
      // Remove console errors
      // Remove the script tag to prevent future errors
      googleScript.remove();
    };
    
    document.head.appendChild(googleScript);
  };
  
  // Try loading the script
  loadScript();
};

const BusinessRegistration = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, user } = useAuth();
  const isOptional = location.state?.optional === true;
  const [loading, setLoading] = useState(false);
  const [duplicateError, setDuplicateError] = useState(false);
  const [addressErrors, setAddressErrors] = useState<AddressErrors>({});
  const [addressSelected, setAddressSelected] = useState(false);
  const addressInputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<any>(null);
  
  // Remove debug API configuration and test
  const [apiKeyStatus, setApiKeyStatus] = useState<{
    checked: boolean;
    isValid: boolean;
    message: string;
  }>({
    checked: false,
    isValid: false,
    message: 'Checking API key...'
  });

  // Helper function to ensure the user is authenticated
  const ensureAuthenticated = async (): Promise<boolean> => {
    const registrationDataStr = localStorage.getItem('registrationData');
    
    if (!registrationDataStr) {
      navigate('/login');
      return false;
    }

    try {
      const registrationData = JSON.parse(registrationDataStr);
      
      if (!registrationData.email || !registrationData.password) {
        toast.error('Registration data is incomplete. Please register again.');
        navigate('/register');
        return false;
      }
      
      console.log('Attempting to authenticate with stored credentials');
      
      const loginResponse = await api.post('/api/auth/login/', {
        username: registrationData.email,
        password: registrationData.password
      });
      
      // Use the AuthContext login function to properly set authentication state
      if (loginResponse.data.token) {
        // Store the token in localStorage directly as well to ensure it's available
        localStorage.setItem('token', loginResponse.data.token);
        console.log('Authentication successful, token stored');
        login(loginResponse.data.token);
      }
      
      // If login successful, return true
      return true;
    } catch (error: any) {
      console.error('Authentication failed:', error);
      toast.error('Authentication failed. Please log in again.');
      navigate('/login');
      return false;
    }
  };

  // Check for registration data
  useEffect(() => {
    const registrationData = localStorage.getItem('registrationData');
    
    if (!registrationData) {
      toast.error('User registration information not found');
      navigate('/register');
      return;
    }
    
    try {
      const parsedData = JSON.parse(registrationData);
      
      // Check if we have the required data for authentication
      if (!parsedData.email || !parsedData.password) {
        toast.error('Registration data is incomplete. Please register again.');
        // Clear the incomplete data
        localStorage.removeItem('registrationData');
        navigate('/register');
        return;
      }
    } catch (e) {
      toast.error('Error processing registration data. Please register again.');
      navigate('/register');
    }
  }, [navigate]);

  // Check API key validity
  useEffect(() => {
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
    if (!apiKey || apiKey === 'YOUR_API_KEY') {
      setApiKeyStatus({
        checked: true,
        isValid: false,
        message: 'No Google Maps API key provided'
      });
      return;
    }

    // Test the API key
    testGoogleMapsApiKey(apiKey).then(result => {
      setApiKeyStatus({
        checked: true,
        isValid: result.isValid,
        message: result.message
      });
    });
  }, []);

  // Initialize Google Maps Places Autocomplete
  useEffect(() => {
    // Only attempt to load Google Maps if the API key is valid
    if (apiKeyStatus.checked && !apiKeyStatus.isValid) {
      // Skip Google Maps initialization due to invalid API key
      return;
    }
    
    // Function to initialize Google Maps Autocomplete
    window.initGoogleMapsAutocomplete = () => {
      if (!addressInputRef.current) return;
      
      // Check if Google Maps API is loaded correctly
      if (!window.google || !window.google.maps || !window.google.maps.places) {
        // Address suggestions will not be available
        return;
      }
      
      try {
        // Check if autocomplete is already initialized to prevent duplicates
        if (autocompleteRef.current) {
          window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
        }
        
        autocompleteRef.current = new window.google.maps.places.Autocomplete(
          addressInputRef.current,
          { 
            types: ['address'],
            componentRestrictions: { country: 'us' } 
          }
        );
        
        window.google.maps.event.addListener(
          autocompleteRef.current,
          'place_changed',
          () => {
            try {
              const place = autocompleteRef.current.getPlace();
              if (!place || !place.address_components) return;
              
              // Extract address components
              let streetNumber = '';
              let route = '';
              let city = '';
              let state = '';
              let zipCode = '';
              
              for (const component of place.address_components) {
                const types = component.types;
                
                if (types.includes('street_number')) {
                  streetNumber = component.long_name;
                } else if (types.includes('route')) {
                  route = component.long_name;
                } else if (types.includes('locality')) {
                  city = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                  state = component.short_name;
                } else if (types.includes('postal_code')) {
                  zipCode = component.long_name;
                }
              }
              
              // Update form with selected address
              setFormData(prev => ({
                ...prev,
                address_line1: `${streetNumber} ${route}`.trim(),
                city,
                state,
                zip_code: zipCode
              }));
              
              setAddressSelected(true);
              setAddressErrors({});
            } catch (err) {
              // Error handling place selection
            }
          }
        );
      } catch (error) {
        // Error initializing Google Maps Autocomplete
      }
    };
    
    // Load the Google Maps script
    loadGoogleMapsScript();
    
    // Cleanup
    return () => {
      if (autocompleteRef.current && window.google && window.google.maps && window.google.maps.event) {
        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
        autocompleteRef.current = null;
      }
    };
  }, [apiKeyStatus.isValid, apiKeyStatus.checked]);

  // Function to validate domain format
  const isDomainValid = (domain: string): boolean => {
    if (!domain || domain.trim() === '') return true; // Empty domains are valid (it's optional)
    
    // Basic domain validation regex
    // Checks for: example.com, sub.example.com, etc.
    const domainRegex = /^[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,}$/i;
    return domainRegex.test(domain);
  };

  // Helper function to clean up domain format
  const formatDomain = (domain: string): string => {
    if (!domain || domain.trim() === '') return '';
    
    // Strip common prefixes
    let cleaned = domain.trim().toLowerCase();
    cleaned = cleaned.replace(/^(https?:\/\/)?(www\.)?/, '');
    cleaned = cleaned.replace(/\/+$/, ''); // Remove trailing slashes
    
    return cleaned;
  };

  // Handle website input with improved validation
  const handleWebsiteChange = (value: string) => {
    // Apply formatting but keep original in form data
    const processedValue = formatDomain(value);
    setFormData(prev => ({ ...prev, website: processedValue }));
  };

  const handleInputChange = (field: keyof BusinessForm, value: any) => {
    // Convert single industry to array if needed
    if (field === 'industry' && typeof value === 'string') {
      setFormData({
        ...formData,
        [field]: [value]
      });
    } else {
      setFormData({
        ...formData,
        [field]: value
      });
    }
    
    // Clear any duplicate error when values change
    if (field === 'business_name' || field === 'address_line1') {
      setDuplicateError(false);
    }
  };

  const validateAddress = (): boolean => {
    const errors: AddressErrors = {};
    let isValid = true;

    // Check if address line 1 is provided
    if (!formData.address_line1.trim()) {
      errors.address_line1 = 'Address is required';
      isValid = false;
    }

    // Check if city is provided
    if (!formData.city.trim()) {
      errors.city = 'City is required';
      isValid = false;
    }

    // Check if state is selected
    if (!formData.state) {
      errors.state = 'State is required';
      isValid = false;
    }

    // Validate ZIP code format
    if (!formData.zip_code) {
      errors.zip_code = 'ZIP code is required';
      isValid = false;
    } else if (!/^\d{5}$/.test(formData.zip_code)) {
      errors.zip_code = 'ZIP code must be 5 digits';
      isValid = false;
    }

    setAddressErrors(errors);
    return isValid;
  };

  // Enhanced function to check for duplicate business
  const checkDuplicateBusiness = async (businessName: string, addressLine1: string): Promise<boolean> => {
    try {
      if (!businessName || !addressLine1) {
        return false;
      }
      
      // Normalize the business name and address
      const normalizedName = normalizeBusinessName(businessName);
      const normalizedAddress = normalizeAddress(addressLine1);
      
      // Call the API to check for duplicate business
      const response = await api.get('/api/accounts/check-duplicate', {
        params: {
          business_name: normalizedName,
          address_line1: normalizedAddress
        }
      });
      
      return response.data.duplicate;
    } catch (error) {
      console.error('Error checking for duplicate business:', error);
      return false;
    }
  };

  // Helper function to generate a safe domain name from business name
  const generateSafeDomain = (businessName: string): string => {
    // Remove special characters, replace spaces with hyphens, and convert to lowercase
    const safeName = businessName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-');
    
    // Ensure it's not empty, fallback to a default if it is
    return safeName ? `${safeName}.example.com` : 'business.example.com';
  };

  // Function to handle skipping business registration
  const handleSkip = async () => {
    try {
      // Ensure the user is authenticated before proceeding
      const isAuthenticated = await ensureAuthenticated();
      
      if (!isAuthenticated) {
        toast.error('Authentication failed. Please log in again.');
        navigate('/login');
        return;
      }
      
      localStorage.removeItem('registrationData'); // Clean up
      toast.success('Registration complete! You can add business details later.');
      navigate('/');
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate address before submitting
    if (!validateAddress()) {
      toast.error('Please fix address errors before submitting');
      return;
    }
    
    // Validate website if provided
    if (formData.website && !isDomainValid(formData.website)) {
      toast.error('Please enter a valid website domain format (e.g. example.com)');
      return;
    }
    
    setLoading(true);
    
    try {
      // Ensure authentication first
      const isAuthenticated = await ensureAuthenticated();
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }
      
      // Check for duplicate business before submission
      const isDuplicate = await checkDuplicateBusiness(formData.business_name, formData.address_line1);
      
      if (isDuplicate) {
        setDuplicateError(true);
        setLoading(false);
        return;
      }
      
      // Get user email from registration data
      const registrationDataStr = localStorage.getItem('registrationData');
      let userEmail = '';
      
      if (registrationDataStr) {
        try {
          const registrationData = JSON.parse(registrationDataStr);
          userEmail = registrationData.email || '';
        } catch (e) {
          console.error('Error parsing registration data:', e);
        }
      }
      
      if (!userEmail) {
        toast.error('User email not found. Please register again.');
        navigate('/register');
        setLoading(false);
        return;
      }
      
      // Add normalized fields to the form data
      const submissionData = {
        ...formData,
        normalized_name: normalizeBusinessName(formData.business_name),
        normalized_address: normalizeAddress(formData.address_line1),
        email: userEmail // Include the user's email in the submission
      };
      
      // Log the request data
      console.log('===== BUSINESS REGISTRATION DEBUG =====');
      console.log('Submitting business registration data to API');
      console.log('URL:', '/api/auth/register/business/');
      console.log('Headers:', {
        'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : 'No token found',
        'Content-Type': 'application/json'
      });
      console.log('Data:', submissionData);
      console.log('User Email:', userEmail);
      
      // Submit the form data with detailed error handling
      try {
        console.log('Starting API request...');
        const response = await api.post('/api/auth/register/business/', submissionData);
        console.log('API Response:', response);
        
        // Clean up registration data
        localStorage.removeItem('registrationData');
        
        // Show success toast
        toast.success('Business account created successfully!');
        
        // Determine redirect path based on user type
        const redirectPath = user?.groups.includes('Business Admins') ? '/accounts' : '/my-business';
        
        // Set a small delay to ensure the toast is visible
        setTimeout(() => {
          navigate(redirectPath);
        }, 1000);
      } catch (apiError: any) {
        console.error('API Error Details:', apiError);
        if (apiError.response) {
          console.error('Response Status:', apiError.response.status);
          console.error('Response Data:', apiError.response.data);
          console.error('Response Headers:', apiError.response.headers);
          
          if (apiError.response.status === 404) {
            toast.error('API endpoint not found. Please contact support.');
          } else if (apiError.response.status === 401) {
            toast.error('Authentication failed. Please log in again.');
            navigate('/login');
          } else {
            toast.error(`Error submitting form: ${apiError.response.data.detail || 'Unknown error'}`);
          }
        } else {
          toast.error(`Network error: ${apiError.message}`);
        }
        setLoading(false);
      }
    } catch (error: any) {
      console.error('Business Registration Error:', error);
      toast.error(`An error occurred: ${error.message}`);
      setLoading(false);
    }
  };

  const [formData, setFormData] = useState<BusinessForm>({
    business_name: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    zip_code: '',
    industry: [],
    company_phone: '',
    company_email: '',
    website: ''
  });

  return (
    <div className="container mx-auto flex items-center justify-center min-h-full p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Add Business Information</CardTitle>
          <CardDescription>
            {isOptional 
              ? "This step is optional. Add your business details or skip to continue." 
              : "Complete your business profile to enhance your experience."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isOptional && (
            <Alert className="mb-6 bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertDescription className="text-blue-700">
                Your account has been created successfully. Adding business information is optional.
              </AlertDescription>
            </Alert>
          )}
          
          {duplicateError && (
            <Alert className="mb-6 bg-amber-50 border-amber-200">
              <AlertDescription className="text-amber-800">
                A business with this name and address is already registered in our system. If this is your business, please contact support.
              </AlertDescription>
            </Alert>
          )}
          
          {!apiKeyStatus.isValid && apiKeyStatus.checked && (
            <Alert className="mb-6 bg-amber-50 border-amber-200">
              <AlertDescription className="text-amber-800">
                Google Maps address suggestions are not available: {apiKeyStatus.message}
              </AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="business_name">Business Name</Label>
              <Input
                id="business_name"
                value={formData.business_name}
                onChange={(e) => handleInputChange('business_name', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry">Industry</Label>
              <MultiSelect
                options={INDUSTRY_OPTIONS}
                selected={formData.industry}
                onChange={(selectedValues: string[]) => {
                  handleInputChange('industry', selectedValues);
                }}
                placeholder="Select your industry"
                className="w-full"
              />
              <div className="text-xs text-muted-foreground mt-1">
                Select all industries that apply to your business
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <Label htmlFor="address_line1">Address Line 1</Label>
                {!apiKeyStatus.isValid ? (
                  <div className="flex items-center text-xs text-amber-500">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span>Address suggestions unavailable</span>
                  </div>
                ) : (
                  <div className="flex items-center text-xs text-muted-foreground">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span>Google address suggestions available</span>
                  </div>
                )}
              </div>
              <div className="relative">
                <Input
                  id="address_line1"
                  ref={addressInputRef}
                  value={formData.address_line1}
                  onChange={(e) => handleInputChange('address_line1', e.target.value)}
                  placeholder="Street address, P.O. box, company name"
                  className={`${addressErrors.address_line1 ? "border-rose-300" : ""} ${addressSelected ? "border-emerald-200" : ""}`}
                  required
                />
                {addressSelected && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-emerald-500">
                    <CheckCircle2 className="h-4 w-4" />
                  </div>
                )}
              </div>
              {addressErrors.address_line1 && (
                <p className="text-xs text-rose-500 mt-1">{addressErrors.address_line1}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="address_line2">Address Line 2 (Optional)</Label>
              <Input
                id="address_line2"
                value={formData.address_line2}
                onChange={(e) => handleInputChange('address_line2', e.target.value)}
                placeholder="Apartment, suite, unit, building, floor, etc."
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-1">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className={addressErrors.city ? "border-rose-300" : ""}
                  required
                />
                {addressErrors.city && (
                  <p className="text-xs text-rose-500 mt-1">{addressErrors.city}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="state">State</Label>
                <Select 
                  value={formData.state}
                  onValueChange={(value) => handleInputChange('state', value)}
                  required
                >
                  <SelectTrigger id="state" className={addressErrors.state ? "border-rose-300" : ""}>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {US_STATES.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {addressErrors.state && (
                  <p className="text-xs text-rose-500 mt-1">{addressErrors.state}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="zip_code">ZIP Code</Label>
                <Input
                  id="zip_code"
                  value={formData.zip_code}
                  onChange={(e) => handleInputChange('zip_code', e.target.value)}
                  maxLength={5}
                  placeholder="5-digit ZIP"
                  className={addressErrors.zip_code ? "border-rose-300" : ""}
                  required
                />
                {addressErrors.zip_code && (
                  <p className="text-xs text-rose-500 mt-1">{addressErrors.zip_code}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company_phone">Company Phone (Optional)</Label>
                <Input
                  id="company_phone"
                  type="tel"
                  value={formData.company_phone}
                  onChange={(e) => handleInputChange('company_phone', e.target.value)}
                  placeholder="(*************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_email">Company Email</Label>
                <Input
                  id="company_email"
                  type="email"
                  value={formData.company_email}
                  onChange={(e) => handleInputChange('company_email', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">
                Business Website <span className="text-muted-foreground text-sm">(Optional - we'll create a placeholder if empty)</span>
              </Label>
              <Input
                id="website"
                type="text"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="example.com"
                className={formData.website && !isDomainValid(formData.website) ? "border-yellow-300" : ""}
              />
              {formData.website && !isDomainValid(formData.website) ? (
                <p className="text-xs text-yellow-500">
                  This doesn't look like a valid domain name. Enter only the domain (e.g., yourcompany.com).
                </p>
              ) : (
                <p className="text-xs text-muted-foreground">
                  Enter your domain name without http:// or www. (e.g., yourcompany.com)
                </p>
              )}
            </div>

            <div className="flex justify-between pt-4">
              {isOptional ? (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSkip}
                >
                  Skip for Now
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/')}
                >
                  Back to Home
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? "Saving Information..." : "Save Business Information"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default BusinessRegistration; 