import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import api from '../../utils/api';
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Info } from 'lucide-react';

interface RegisterForm {
  email: string;
  password: string;
  password2: string;
  first_name: string;
  last_name: string;
}

interface FormErrors {
  email?: string;
  password?: string[];
  password2?: string;
  first_name?: string;
  last_name?: string;
}

const Register = () => {
  const [loading, setLoading] = useState(false);
  const [existingEmail, setExistingEmail] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formTouched, setFormTouched] = useState<Record<string, boolean>>({
    email: false,
    password: false,
    password2: false,
    first_name: false,
    last_name: false
  });
  const navigate = useNavigate();
  const [formData, setFormData] = useState<RegisterForm>({
    email: '',
    password: '',
    password2: '',
    first_name: '',
    last_name: ''
  });

  // Validate form data whenever it changes
  useEffect(() => {
    validateForm();
  }, [formData]);

  const validateForm = () => {
    const errors: FormErrors = {};

    // Email validation
    if (formTouched.email) {
      if (!formData.email) {
        errors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }

    // First name validation
    if (formTouched.first_name && !formData.first_name) {
      errors.first_name = 'First name is required';
    }

    // Last name validation
    if (formTouched.last_name && !formData.last_name) {
      errors.last_name = 'Last name is required';
    }

    // Password validation
    if (formTouched.password) {
      const passwordErrors: string[] = [];
      
      if (!formData.password) {
        passwordErrors.push('Password is required');
      } else {
        if (formData.password.length < 8) {
          passwordErrors.push('At least 8 characters');
        }
        if (!/[A-Z]/.test(formData.password)) {
          passwordErrors.push('One uppercase letter');
        }
        if (!/[a-z]/.test(formData.password)) {
          passwordErrors.push('One lowercase letter');
        }
        if (!/[0-9]/.test(formData.password)) {
          passwordErrors.push('One number');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) {
          passwordErrors.push('One special character');
        }
        
        // Check for common passwords
        const commonPasswords = ['password', 'password123', '123456', 'qwerty', 'admin'];
        if (commonPasswords.includes(formData.password.toLowerCase())) {
          passwordErrors.push('Password is too common');
        }
      }
      
      if (passwordErrors.length > 0) {
        errors.password = passwordErrors;
      }
    }

    // Confirm password validation
    if (formTouched.password2) {
      if (!formData.password2) {
        errors.password2 = 'Please confirm your password';
      } else if (formData.password !== formData.password2) {
        errors.password2 = 'Passwords do not match';
      }
    }

    setFormErrors(errors);
  };

  const handleInputChange = (field: keyof RegisterForm, value: string) => {
    setFormData({ ...formData, [field]: value });
    setFormTouched({ ...formTouched, [field]: true });
    setExistingEmail(false); // Clear existing email error when email field changes
  };

  const isFormValid = () => {
    // Mark all fields as touched to show all validation errors
    const allTouched = Object.keys(formTouched).reduce((acc, key) => {
      return { ...acc, [key]: true };
    }, {});
    setFormTouched(allTouched);
    
    // Re-validate the form
    validateForm();
    
    // Check if there are any errors
    return (
      !formErrors.email &&
      !formErrors.password &&
      !formErrors.password2 &&
      !formErrors.first_name &&
      !formErrors.last_name &&
      formData.email &&
      formData.password &&
      formData.password2 &&
      formData.first_name &&
      formData.last_name
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFormValid()) {
      toast.error("Please check form for errors");
      return;
    }
    
    setLoading(true);

    try {
      const response = await api.post('/api/auth/register/', formData);
      if (response.status === 201) {
        // Store user data in localStorage for optional business information step
        // Include the password from the form data to allow login in subsequent steps
        const registrationDataToStore = {
          ...response.data.user,
          password: formData.password // Include password for future authentication
        };
        localStorage.setItem('registrationData', JSON.stringify(registrationDataToStore));
        
        // Show success message
        toast.success("Registration successful! You can now add your business information or go to the dashboard.");
        
        // Navigate to business registration
        navigate('/register/business', { state: { optional: true } });
      }
    } catch (error: any) {
      if (error.response?.data?.email) {
        setExistingEmail(true);
      } else if (error.response?.data?.password) {
        // Handle specific password errors from the server
        const passwordErrors = Array.isArray(error.response.data.password) 
          ? error.response.data.password 
          : [error.response.data.password];
        
        setFormErrors({
          ...formErrors,
          password: passwordErrors
        });
        
        // Show the first password error as a toast
        toast.error(`Password error: ${passwordErrors[0]}`);
      } else {
        toast.error(error.response?.data?.detail || "Registration failed");
      }
    } finally {
      setLoading(false);
    }
  };

  const getFieldClass = (field: keyof FormErrors) => {
    if (formTouched[field] && formErrors[field]) {
      return "border-rose-300 focus-visible:ring-rose-200"; // More subtle red
    }
    if (formTouched[field] && !formErrors[field] && formData[field]) {
      return "border-emerald-200 focus-visible:ring-emerald-100"; // More subtle green
    }
    return "";
  };

  return (
    <div className="container mx-auto flex items-center justify-center min-h-full p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Create an Account</CardTitle>
          <CardDescription>Enter your personal information to register</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={getFieldClass('email')}
                required
              />
              {formTouched.email && formErrors.email && (
                <p className="text-xs text-rose-500 mt-1">{formErrors.email}</p>
              )}
              {existingEmail && (
                <Alert variant="default" className="mt-2 bg-amber-50 text-amber-800 border-amber-200">
                  <AlertDescription className="text-sm">
                    This email is already registered.{' '}
                    <button
                      type="button"
                      onClick={() => navigate('/forgot-password')}
                      className="text-primary hover:underline"
                    >
                      Forgot your password?
                    </button>
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  className={getFieldClass('first_name')}
                  required
                />
                {formTouched.first_name && formErrors.first_name && (
                  <p className="text-xs text-rose-500 mt-1">{formErrors.first_name}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className={getFieldClass('last_name')}
                  required
                />
                {formTouched.last_name && formErrors.last_name && (
                  <p className="text-xs text-rose-500 mt-1">{formErrors.last_name}</p>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={getFieldClass('password')}
                required
              />
              {formTouched.password && formErrors.password && formErrors.password.length > 0 && (
                <div className="text-xs text-muted-foreground mt-1">
                  <p>Password requirements:</p>
                  <ul className="list-disc pl-4 mt-1 grid grid-cols-2 gap-x-4 text-rose-500">
                    {formErrors.password.map((error, idx) => (
                      <li key={idx} className="text-xs">{error}</li>
                    ))}
                  </ul>
                </div>
              )}
              {!formErrors.password && formData.password && (
                <div className="text-xs text-emerald-600 mt-1 flex items-center">
                  <Info className="h-3 w-3 mr-1" />
                  Password meets all requirements
                </div>
              )}
              {!formTouched.password && (
                <div className="text-xs text-muted-foreground mt-1">
                  Password must be at least 8 characters with uppercase, lowercase, numbers, and special characters
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="password2">Confirm Password</Label>
              <Input
                id="password2"
                type="password"
                value={formData.password2}
                onChange={(e) => handleInputChange('password2', e.target.value)}
                className={getFieldClass('password2')}
                required
              />
              {formTouched.password2 && formErrors.password2 && (
                <p className="text-xs text-rose-500 mt-1">{formErrors.password2}</p>
              )}
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Creating Account..." : "Create Account"}
            </Button>
            <p className="text-center text-sm text-muted-foreground mt-2">
              After registration, you'll have the option to add business information.
            </p>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Register; 