import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'sonner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireRegularUser?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requireRegularUser }) => {
  const { isAuthenticated, user, isInitialized } = useAuth();
  const location = useLocation();
  const [showLoading, setShowLoading] = useState(false);
  
  // Add a slight delay before showing the loading indicator to prevent flashing
  useEffect(() => {
    if (!isInitialized) {
      const timer = setTimeout(() => setShowLoading(true), 100);
      return () => clearTimeout(timer);
    } else {
      setShowLoading(false);
    }
  }, [isInitialized]);

  // Removed debug logging effect
  
  // If auth is still initializing, show a loading indicator (after delay)
  if (!isInitialized) {
    return showLoading ? (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading authentication...</p>
        </div>
      </div>
    ) : null; // Return null initially to prevent flash
  }

  // Not authenticated - redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if we're on a regular business user route
  if (requireRegularUser) {
    const isRegularUser = user?.groups?.includes('Business Regular Users') && 
                         !user?.groups?.includes('Business Admins');
    
    if (!isRegularUser) {
      toast.error('This page is only accessible to regular business users');
      return <Navigate to="/" replace />;
    }
  }

  // Authenticated and passed any role checks
  return <>{children}</>;
};

export default ProtectedRoute;
