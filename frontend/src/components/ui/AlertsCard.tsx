import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCircle2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface Alert {
  id: string | number;
  type: 'info' | 'warning' | 'success';
  message: string;
  timestamp: string;
}

interface AlertsCardProps {
  alerts: Alert[];
}

export function AlertsCard({ alerts }: AlertsCardProps) {
  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return <Bell className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">Recent Alerts</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <p className="text-sm text-muted-foreground">No new alerts</p>
          ) : (
            alerts.map((alert) => (
              <div
                key={alert.id}
                className={cn(
                  "flex items-start space-x-4 rounded-md border p-4",
                  {
                    'bg-yellow-50 border-yellow-200': alert.type === 'warning',
                    'bg-green-50 border-green-200': alert.type === 'success',
                    'bg-blue-50 border-blue-200': alert.type === 'info',
                  }
                )}
              >
                {getAlertIcon(alert.type)}
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {alert.message}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {alert.timestamp}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
} 