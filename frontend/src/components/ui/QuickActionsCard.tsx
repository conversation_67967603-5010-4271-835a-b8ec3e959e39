import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./card";
import { Button } from "./button";
import { Code2, PlusCircle, Settings, Upload } from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { UserType } from "../../types/user";

interface QuickAction {
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  allowedTypes?: UserType[];
}

export function QuickActionsCard() {
  const navigate = useNavigate();
  const { userType } = useAuth();

  const quickActions: QuickAction[] = [
    {
      label: "Create Account",
      icon: <PlusCircle className="h-4 w-4" />,
      onClick: () => navigate("/accounts/add"),
      allowedTypes: [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN],
    },
    {
      label: "Aida Setup",
      icon: <Code2 className="h-4 w-4" />,
      onClick: () => navigate("/setup"),
    },
    {
      label: "Upload Documents",
      icon: <Upload className="h-4 w-4" />,
      onClick: () => navigate("/documents/upload"),
    },
    {
      label: "Security Settings",
      icon: <Settings className="h-4 w-4" />,
      onClick: () => navigate("/settings"),
    },
  ];

  // Filter actions based on user type
  const visibleActions = quickActions.filter(action => 
    !action.allowedTypes || (userType && action.allowedTypes.includes(userType))
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          {visibleActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="justify-start space-x-2"
              onClick={action.onClick}
            >
              {action.icon}
              <span>{action.label}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 