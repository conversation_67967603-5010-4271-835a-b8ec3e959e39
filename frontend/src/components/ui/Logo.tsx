import React from 'react';
import { SvgImage } from './SvgImage';

export const Logo: React.FC = () => {
  return (
    <div className="h-10 w-40 relative">
      <div className="logo-aida1 absolute inset-0 transition-opacity">
        <SvgImage 
          src="/images/aida1.svg" 
          alt="AIDA Logo 1" 
          className="h-full w-full object-contain" 
        />
      </div>
      <div className="logo-aida3 absolute inset-0 transition-opacity">
        <SvgImage 
          src="/images/aida3.svg" 
          alt="AIDA Logo 3" 
          className="h-full w-full object-contain" 
          background="#3f72af"
        />
      </div>
      <div className="logo-aida4 absolute inset-0 transition-opacity">
        <SvgImage 
          src="/images/aida4.svg" 
          alt="AIDA Logo 4" 
          className="h-full w-full object-contain" 
        />
      </div>
      <div className="logo-aida4b absolute inset-0 transition-opacity">
        <SvgImage 
          src="/images/aida4b.svg" 
          alt="AIDA Logo 4B" 
          className="h-full w-full object-contain" 
        />
      </div>
      <div className="logo-aida4c absolute inset-0 transition-opacity">
        <SvgImage 
          src="/images/aida4c.svg" 
          alt="AIDA Logo 4C" 
          className="h-full w-full object-contain" 
          background="#333333"
        />
      </div>
      <div className="logo-png absolute inset-0 transition-opacity">
        <img
          src="/images/logo.png" 
          alt="AIDA Logo Default" 
          className="h-full w-full object-contain" 
        />
      </div>
    </div>
  );
};

export default Logo; 