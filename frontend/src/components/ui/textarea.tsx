import React from 'react';
import { Input } from 'antd';

const { TextArea: AntTextArea } = Input;

interface TextareaProps extends React.ComponentProps<typeof AntTextArea> {
  className?: string;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <AntTextArea
        ref={ref}
        className={className}
        {...props}
      />
    );
  }
);

Textarea.displayName = "Textarea";

export { Textarea };
