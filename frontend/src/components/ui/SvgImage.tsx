import React, { useState, useEffect } from 'react';

interface SvgImageProps {
  src: string;
  alt: string;
  className?: string;
  background?: string;
}

export const SvgImage: React.FC<SvgImageProps> = ({ 
  src, 
  alt, 
  className = '', 
  background = 'transparent' 
}) => {
  const [svgContent, setSvgContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const sanitizeAndFixSvg = (svgText: string): string => {
      // Remove XML declaration (if present)
      let sanitized = svgText.replace(/<\?xml[^>]*\?>/, '');
      
      // Ensure SVG tag has its namespace
      if (!sanitized.includes('xmlns="http://www.w3.org/2000/svg"')) {
        sanitized = sanitized.replace(/<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
      }
      
      // Extract width/height attributes if they exist
      const widthMatch = sanitized.match(/width=["'](\d+(?:\.\d+)?)[^"']*["']/);
      const heightMatch = sanitized.match(/height=["'](\d+(?:\.\d+)?)[^"']*["']/);
      
      let width = widthMatch ? parseFloat(widthMatch[1]) : 0;
      let height = heightMatch ? parseFloat(heightMatch[1]) : 0;
      
      // If viewBox doesn't exist but width and height do, add viewBox
      if (!sanitized.includes('viewBox=') && width > 0 && height > 0) {
        sanitized = sanitized.replace(/<svg/, `<svg viewBox="0 0 ${width} ${height}"`);
      } else if (!sanitized.includes('viewBox=')) {
        // If no viewBox or dimensions, add a default
        sanitized = sanitized.replace(/<svg/, '<svg viewBox="0 0 100 100"');
      }
      
      // Ensure preserveAspectRatio is set properly (especially important for scaled SVGs)
      if (!sanitized.includes('preserveAspectRatio')) {
        sanitized = sanitized.replace(/<svg/, '<svg preserveAspectRatio="xMidYMid meet"');
      }
      
      // Remove any absolute positioning or fixed dimensions that might affect containment
      sanitized = sanitized.replace(/position:\s*absolute/g, 'position: relative');
      sanitized = sanitized.replace(/position:\s*fixed/g, 'position: relative');
      
      // Force SVG to respect container bounds
      sanitized = sanitized.replace(/<svg/, '<svg style="width: 100%; height: 100%; display: block;"');
      
      return sanitized;
    };

    const fetchSvg = async () => {
      try {
        setIsLoading(true);
        
        // Normalize the URL to ensure it's absolute and has proper slashes
        let url = src;
        if (!url.startsWith('http') && !url.startsWith('/')) {
          url = `/${url}`;
        }
        
        // Append a cache-busting parameter
        const cacheBuster = `?t=${new Date().getTime()}`;
        const finalUrl = `${url}${cacheBuster}`;
        
        console.log('Fetching SVG from:', finalUrl);
        
        const response = await fetch(finalUrl);
        if (!response.ok) {
          throw new Error(`Failed to load SVG: ${response.status} ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('svg')) {
          console.warn(`Response content type is not SVG: ${contentType}`);
        }
        
        const svgText = await response.text();
        
        if (!svgText || svgText.trim().length === 0) {
          throw new Error('SVG content is empty');
        }
        
        if (!svgText.trim().startsWith('<svg')) {
          console.warn('Response does not start with <svg> tag:', svgText.substring(0, 50));
        }
        
        // Sanitize and fix SVG before setting
        const fixedSvg = sanitizeAndFixSvg(svgText);
        setSvgContent(fixedSvg);
        setError(null);
      } catch (err) {
        console.error('Error loading SVG:', err);
        setError(`Failed to load image: ${err instanceof Error ? err.message : String(err)}`);
        // Fall back to a simple text representation
        setSvgContent(`<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#f0f0f0" />
          <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="14" fill="#666">
            ${alt}
          </text>
        </svg>`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSvg();
  }, [src, alt]);

  if (isLoading) {
    return <div className={`${className} animate-pulse bg-gray-200`} style={{ minWidth: '40px', minHeight: '40px' }} aria-label={`Loading ${alt}`} />;
  }

  const bgStyle = background !== 'transparent' ? { backgroundColor: background } : {};

  // Use a wrapper div to control sizing and a separate div for the SVG content
  return (
    <div 
      className={className}
      aria-label={alt}
      style={{ 
        position: 'relative',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...bgStyle
      }}
    >
      <div
        dangerouslySetInnerHTML={{ __html: svgContent || '' }}
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      />
    </div>
  );
};

export default SvgImage; 