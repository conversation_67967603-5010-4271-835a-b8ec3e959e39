import React, { useState, useEffect } from 'react';
import { useTheme, PRESET_THEMES, PresetTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Save, CheckCircle2 } from 'lucide-react';
import { SvgImage } from './SvgImage';

export const ThemeCustomizer: React.FC = () => {
  const { themeMode, presetTheme, setPresetTheme, refreshTheme } = useTheme();
  
  // Temp values for preview
  const [tempTheme, setTempTheme] = useState<PresetTheme>(presetTheme);
  const [previewedTheme, setPreviewedTheme] = useState<PresetTheme | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Track if we've made a theme selection change
  useEffect(() => {
    const hasChanges = tempTheme !== presetTheme;
    setHasUnsavedChanges(hasChanges);
    
    // Reset the previewed theme when context values change
    if (presetTheme !== previewedTheme) {
      setPreviewedTheme(null);
    }
  }, [tempTheme, presetTheme, previewedTheme]);
  
  // Save function - apply changes to context and refresh
  const saveTheme = () => {
    setPresetTheme(tempTheme);
    refreshTheme();
    setHasUnsavedChanges(false);
    setPreviewedTheme(tempTheme);
  };
  
  // Preview function - temporarily show theme without saving
  const previewTheme = (theme: PresetTheme) => {
    // Skip if this is the same theme we're already previewing
    if (previewedTheme === theme) {
      return;
    }
    
    setTempTheme(theme);
    setPreviewedTheme(theme);
    
    // Apply temporary theme
    const root = document.documentElement;
    
    // Find selected theme config
    const selectedTheme = PRESET_THEMES.find(t => t.id === theme);
    if (!selectedTheme) return;
    
    // 1. Apply theme class (light/dark/system)
    root.classList.remove('light', 'dark');
    root.classList.add(selectedTheme.mode);
    
    // 2. Apply preset theme class
    PRESET_THEMES.forEach(t => {
      root.classList.remove(`theme-${t.id}`);
    });
    root.classList.add(`theme-${theme}`);
    
    // 3. Set logo attribute
    const logoValue = selectedTheme.logoIsPng ? 'logo-png' : selectedTheme.logo;
    root.setAttribute('data-logo', logoValue);
    
    // 4. Set font attribute
    root.setAttribute('data-font', selectedTheme.font);

    // 5. Set accent color if available
    if (selectedTheme.accentColor) {
      root.style.setProperty('--accent-color', selectedTheme.accentColor);
    } else {
      root.style.removeProperty('--accent-color');
    }
  };
  
  // Reset preview if component unmounts
  useEffect(() => {
    return () => {
      // Reset to actual theme
      refreshTheme();
    };
  }, [refreshTheme]);
  
  // Get all available themes (all are light themes now)
  const themes = PRESET_THEMES;
  
  // Theme card component
  const ThemeCard = ({ theme, isActive }: { theme: typeof PRESET_THEMES[0], isActive: boolean }) => {
    return (
      <div 
        className={cn(
          "flex flex-col border rounded-lg cursor-pointer transition-all overflow-hidden",
          "hover:shadow-lg",
          isActive ? "border-primary ring-2 ring-primary/20" : "border-border",
          "bg-white"
        )}
        onClick={() => previewTheme(theme.id as PresetTheme)}
      >
        {/* Header with logo and theme name */}
        <div className="p-3 border-b border-border flex items-center justify-between" 
             style={{ 
               backgroundColor: 'rgba(0,0,0,0.03)',
               fontFamily: theme.font === 'inter' ? '"Inter", sans-serif' :
                           theme.font === 'poppins' ? '"Poppins", sans-serif' :
                           theme.font === 'space-grotesk' ? '"Space Grotesk", sans-serif' : 'sans-serif'
             }}>
          <div className="h-8 w-8 mr-2 flex items-center justify-center">
            {theme.logoIsPng ? (
              <img
                src={`/images/${theme.logo}.png`}
                alt={`${theme.name} logo`}
                className="h-6 w-auto"
              />
            ) : (
              <SvgImage
                src={`/images/${theme.logo}.svg`}
                alt={`${theme.name} logo`}
                className="h-6 w-auto"
                background={undefined}
              />
            )}
          </div>
          
          <div className="flex-1 font-medium text-sm" style={{ color: '#333' }}>
            {theme.name}
            {isActive && <span className="ml-2">✓</span>}
          </div>
        </div>
        
        {/* Preview section */}
        <div className="p-3" style={{ 
          color: '#333',
          backgroundColor: '#ffffff'
        }}>
          {/* Color dots */}
          <div className="flex items-center mb-3 gap-2">
            <div className="w-6 h-6 rounded-full shadow-sm" style={{ backgroundColor: theme.primaryColor }} />
            {theme.accentColor && (
              <div className="w-6 h-6 rounded-full shadow-sm" style={{ backgroundColor: theme.accentColor }} />
            )}
          </div>
          
          {/* Sample text with theme font */}
          <div className="mb-3" style={{ 
            fontFamily: theme.font === 'inter' ? '"Inter", sans-serif' :
                        theme.font === 'poppins' ? '"Poppins", sans-serif' :
                        theme.font === 'space-grotesk' ? '"Space Grotesk", sans-serif' : 'sans-serif'
          }}>
            <p className="text-sm mb-1 opacity-90">Sample text with {theme.name} theme</p>
            <p className="text-xs opacity-70">The quick brown fox jumps over the lazy dog.</p>
          </div>
          
          {/* Sample button with primary color */}
          <div className="flex items-center justify-between mt-3">
            <div className="flex gap-2">
              <div className="py-1 px-2 text-xs rounded flex items-center justify-center"
                   style={{ 
                     backgroundColor: theme.primaryColor,
                     color: '#ffffff'
                   }}>
                Button
              </div>
              
              {theme.accentColor && (
                <div className="py-1 px-2 text-xs rounded flex items-center justify-center"
                     style={{ 
                       backgroundColor: theme.accentColor,
                       color: '#ffffff'
                     }}>
                  Accent
                </div>
              )}
            </div>
            
            <div className="bg-opacity-10 rounded p-1"
                 style={{ 
                   backgroundColor: theme.primaryColor,
                   color: '#333'
                 }}>
              <div className="w-4 h-4" style={{ backgroundColor: 'currentColor', maskImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M12 20h9\'%3E%3C/path%3E%3Cpath d=\'M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\'%3E%3C/path%3E%3C/svg%3E")' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="p-4 bg-card rounded-xl shadow-md border border-border">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h2 className="text-lg font-semibold">Theme Customization</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Click a theme to preview. Changes won't be applied until you save.
          </p>
        </div>
        
        {hasUnsavedChanges && (
          <Button size="sm" onClick={saveTheme} className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            <span>Save Changes</span>
          </Button>
        )}
      </div>
      
      <div className="space-y-6">
        {/* Themes Section */}
        <div>
          <h3 className="text-sm font-medium mb-3">Available Themes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {themes.map(theme => (
              <ThemeCard 
                key={theme.id}
                theme={theme}
                isActive={tempTheme === theme.id}
              />
            ))}
          </div>
        </div>
      </div>
      
      {hasUnsavedChanges && (
        <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700/30 p-3 rounded-lg flex items-center text-sm">
          <CheckCircle2 className="h-5 w-5 text-yellow-500 mr-2" />
          <p className="text-yellow-800 dark:text-yellow-300">Theme previewed but not saved. Click "Save Changes" to apply permanently.</p>
        </div>
      )}
    </div>
  );
}; 