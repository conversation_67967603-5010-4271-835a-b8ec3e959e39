import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import api from "../../utils/api";
import { UserType } from "../../types/user";

export function WelcomeCard() {
  const { user, userType } = useAuth();
  const navigate = useNavigate();
  const [subscriptionTier, setSubscriptionTier] = useState("Basic");
  const [businessName, setBusinessName] = useState("");

  useEffect(() => {
    const fetchAccountData = async () => {
      try {
        const response = await api.get('/api/accounts/');
        if (response.data?.length > 0) {
          const account = response.data[0];
          setBusinessName(account.account_name || "");
          // This would use actual subscription data once implemented
          const tier = account.subscription_tier || 'basic';
          setSubscriptionTier(tier.charAt(0).toUpperCase() + tier.slice(1));
        }
      } catch (error) {
        console.error('Failed to fetch account data:', error);
      }
    };
    
    if (user) {
      fetchAccountData();
    }
  }, [user]);

  // Determine features based on subscription tier
  const getSubscriptionFeatures = () => {
    switch (subscriptionTier) {
      case "Pro":
        return [
          "5 team member accounts",
          "Document upload (50 documents)",
          "URL scraping (5 pages)",
          "Advanced chat widget customization"
        ];
      case "Premium":
        return [
          "Unlimited team members",
          "Unlimited document upload",
          "Unlimited URL scraping",
          "Advanced AI model access",
          "Booking & ordering through chat"
        ];
      case "Basic":
      default:
        return [
          "1 team member account",
          "Basic chat widget",
          "Standard AI model",
          "Community support"
        ];
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl">Welcome back, {user?.name || 'User'}</CardTitle>
            <CardDescription>
              {businessName ? `${businessName} • ` : ''}
              Here's what's happening with your account
            </CardDescription>
          </div>
          <div 
            onClick={() => navigate('/subscription')}
            className="group cursor-pointer"
          >
            <Badge variant="secondary" className="text-sm group-hover:opacity-80 transition-opacity">
              {subscriptionTier}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid gap-2">
            <div className="text-sm text-muted-foreground">
              Your business subscription includes:
            </div>
            <div className="grid gap-1">
              {getSubscriptionFeatures().map((feature, index) => (
                <div key={index} className="text-sm">• {feature}</div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 