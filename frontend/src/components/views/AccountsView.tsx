import React from 'react';
import { BaseUser, isAidaAdmin, isBusinessAdmin, isBusinessUser, isDemoUser } from '../../types/user';

interface AccountsViewProps {
  user: BaseUser;
}

export const AccountsView: React.FC<AccountsViewProps> = ({ user }) => {
  // AIDA Admin View
  if (isAidaAdmin(user)) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">System-wide Accounts</h2>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-800">Total Businesses</h3>
              <p className="text-2xl font-bold text-blue-900">247</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-800">Active Users</h3>
              <p className="text-2xl font-bold text-green-900">1,234</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-800">New This Month</h3>
              <p className="text-2xl font-bold text-purple-900">+23</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Business Admin View
  if (isBusinessAdmin(user)) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">{user.businessName} Accounts</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-800">Active Users</h3>
              <p className="text-2xl font-bold text-blue-900">48</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-800">Available Seats</h3>
              <p className="text-2xl font-bold text-green-900">12</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Business User View
  if (isBusinessUser(user)) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Account</h2>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-800">Account Status</h3>
              <p className="text-lg text-blue-900">Active</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-800">Last Login</h3>
              <p className="text-lg text-green-900">Today at 9:30 AM</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Demo User View
  if (isDemoUser(user)) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow p-6 text-white">
          <h2 className="text-xl font-semibold mb-2">Account Management Demo</h2>
          <p className="text-blue-100 mb-4">
            Explore how AIDA helps you manage your business accounts efficiently
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Feature Highlights</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-green-700">
                <span className="mr-2">✓</span>
                Centralized account management
              </li>
              <li className="flex items-center text-green-700">
                <span className="mr-2">✓</span>
                Role-based access control
              </li>
              <li className="flex items-center text-green-700">
                <span className="mr-2">✓</span>
                Usage analytics and reporting
              </li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Demo Actions</h3>
            <div className="space-y-2">
              <button className="w-full p-2 bg-blue-50 hover:bg-blue-100 rounded text-blue-700 text-sm font-medium text-left">
                Try Account Creation
              </button>
              <button className="w-full p-2 bg-green-50 hover:bg-green-100 rounded text-green-700 text-sm font-medium text-left">
                View Sample Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default AccountsView; 