import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import AccountView from './accounts/AccountView';
import AccountList from './accounts/AccountList';
import api from '../utils/api';
import { Account } from '../types/account';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusIcon } from 'lucide-react';
import { WelcomeCard } from './ui/WelcomeCard';
import { StatsCard } from './ui/StatsCard';
import { AlertsCard } from './ui/AlertsCard';
import { QuickActionsCard } from './ui/QuickActionsCard';
import { UserType } from '../types/user';

const Home = () => {
  const [loading, setLoading] = useState(true);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const { isAuthenticated, userType } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchAccounts = async () => {
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }
      
      try {
        const response = await api.get('/api/accounts/');
        setAccounts(response.data);
      } catch (error) {
        console.error('Failed to fetch accounts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAccounts();
  }, [isAuthenticated]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader>
            <CardTitle>Welcome to Aida</CardTitle>
            <CardDescription>Please log in to manage your accounts</CardDescription>
          </CardHeader>
          <CardContent className="flex gap-4">
            <Button onClick={() => navigate('/login')}>
              Log In
            </Button>
            <Button variant="outline" onClick={() => navigate('/register')}>
              Register
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Example stats data
  const stats = [
    { label: 'Total Accounts', value: accounts.length },
    { label: 'Active Accounts', value: accounts.filter(a => a.status === 'active').length },
    { label: 'Storage Used', value: '2.1 GB', description: 'of 10 GB' },
  ];

  // Example alerts data
  const alerts = [
    {
      id: 1,
      type: 'info' as const,
      message: 'Welcome to your new dashboard!',
      timestamp: 'Just now'
    },
    {
      id: 2,
      type: 'warning' as const,
      message: 'Your storage is 80% full',
      timestamp: '2 hours ago'
    }
  ];

  if (accounts.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-6">
        <WelcomeCard />
        <QuickActionsCard />
        {/* Only show Create Account card for AIDA Admin and Business Admin */}
        {userType && [UserType.AIDA_ADMIN, UserType.BUSINESS_ADMIN].includes(userType) && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Get Started</CardTitle>
              <CardDescription>Create your first account to begin</CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => navigate('/accounts/add')}>
                <PlusIcon className="mr-2 h-4 w-4" />
                Create Account
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  if (accounts.length === 1) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <WelcomeCard />
          <StatsCard title="Account Overview" stats={stats} />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <AlertsCard alerts={alerts} />
          <QuickActionsCard />
        </div>
        <AccountView account={accounts[0]} />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <WelcomeCard />
        <StatsCard title="Account Overview" stats={stats} />
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <AlertsCard alerts={alerts} />
        <QuickActionsCard />
      </div>
      <AccountList 
        accounts={accounts} 
        loading={loading}
        onDelete={(id) => {
          setAccounts(accounts.filter(c => c.id !== id));
        }}
      />
    </div>
  );
};

export default Home; 