import { Check, X, <PERSON><PERSON><PERSON>cle, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Users } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'
import api from '../utils/api'
import { Account } from '../types/account'
import { UserType } from '../types/user'
import { toast } from 'sonner'

import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"

const plans = [
  {
    name: "Basic",
    description: "Essential AI functionality for small businesses",
    price: "Free",
    features: [
      { name: "AI chat assistant on your website", included: true },
      { name: "1 team member account", included: true },
      { name: "Basic chat widget (light & dark themes)", included: true },
      { name: "Self-service setup and management", included: true },
      { name: "Standard AI model", included: true },
      { name: "Community support", included: true },
      { name: "Document upload for AI training", included: false },
      { name: "URL content scraping for training", included: false },
      { name: "Custom chat widget appearance", included: false },
      { name: "Booking & ordering through chat", included: false },
      { name: "Premium AI model access", included: false },
    ],
    popular: false,
    currentPlan: true,
    className: "",
    buttonVariant: "outline" as const,
  },
  {
    name: "Pro",
    description: "Enhanced capabilities for growing businesses",
    price: "$79/month",
    features: [
      { name: "AI chat assistant on your website", included: true },
      { name: "5 team member accounts", included: true },
      { name: "Advanced chat widget customization", included: true },
      { name: "Document upload for AI training (50 documents)", included: true },
      { name: "Standard AI model", included: true },
      { name: "Priority email support", included: true },
      { name: "URL content scraping for training (5 pages)", included: true },
      { name: "Custom chat widget icon", included: true },
      { name: "Booking & ordering through chat", included: false },
      { name: "Premium AI model access", included: false },
    ],
    popular: true,
    currentPlan: false,
    className: "shadow-lg",
    buttonVariant: "default" as const,
  },
  {
    name: "Premium",
    description: "Enterprise-grade AI solution",
    price: "$149/month",
    features: [
      { name: "AI chat assistant on your website", included: true },
      { name: "Unlimited team member accounts", included: true },
      { name: "Complete chat widget customization", included: true },
      { name: "Document upload for AI training (unlimited)", included: true },
      { name: "Advanced AI model access", included: true },
      { name: "24/7 priority support", included: true },
      { name: "URL content scraping for training (unlimited)", included: true },
      { name: "Fully customizable chat widget", included: true },
      { name: "Booking & ordering through chat", included: true },
      { name: "Premium AI model with enhanced capabilities", included: true },
    ],
    popular: false,
    currentPlan: false,
    className: "bg-gradient-to-br from-violet-600 to-indigo-600 text-white",
    buttonVariant: "secondary" as const,
  }
];

export default function SubscriptionPage() {
  const { user, userType } = useAuth();
  const [loading, setLoading] = useState(true);
  const [account, setAccount] = useState<Account | null>(null);
  const [currentPlan, setCurrentPlan] = useState("Basic");
  const [isAdmin, setIsAdmin] = useState(false);
  const [teamMemberCount, setTeamMemberCount] = useState(1); // Default to 1 for basic users

  // Get team member limit based on subscription tier
  const getTeamMemberLimit = (tier: string) => {
    switch(tier.toLowerCase()) {
      case 'premium': return Infinity; // Unlimited
      case 'pro': return 5;
      case 'basic':
      default: return 1;
    }
  };

  useEffect(() => {
    // Check if user is an admin
    setIsAdmin(
      user?.is_superuser || 
      user?.groups?.includes('Business Admins') || 
      userType === UserType.BUSINESS_ADMIN || 
      userType === UserType.AIDA_ADMIN
    );

    const fetchAccountData = async () => {
      try {
        const response = await api.get('/api/accounts/');
        if (response.data?.length > 0) {
          const accountData = response.data[0];
          setAccount(accountData);
          
          // Determine current plan from account data
          const accountPlan = accountData.subscription_tier || 'basic';
          setCurrentPlan(accountPlan.charAt(0).toUpperCase() + accountPlan.slice(1));
          
          // Get team member count (minimum 1 for basic tier to fix the "0 of 1" issue)
          const memberCount = (accountData.users?.length || 0);
          setTeamMemberCount(accountPlan.toLowerCase() === 'basic' ? Math.max(1, memberCount) : memberCount);
        }
      } catch (error) {
        console.error('Failed to fetch account data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAccountData();
  }, [user, userType]);

  const handleUpgradeClick = (planName: string) => {
    if (!isAdmin) {
      toast.error("Please contact your business administrator to upgrade your subscription plan");
      return;
    }
    
    // In a real implementation, this would navigate to a payment page
    // or initiate a subscription change process
    toast.info(`Initiating upgrade to ${planName} plan. This feature is coming soon.`);
  };

  const getButtonText = (targetPlan: string, currentPlan: string) => {
    if (!isAdmin) return "Contact Admin to Upgrade";
    
    const planRank = { 'Basic': 1, 'Pro': 2, 'Premium': 3 };
    if (planRank[targetPlan as keyof typeof planRank] > planRank[currentPlan as keyof typeof planRank]) {
      return `Upgrade to ${targetPlan}`;
    } else {
      return `Downgrade to ${targetPlan}`;
    }
  };

  // Update plans with current plan status
  const updatedPlans = plans.map(plan => ({
    ...plan,
    currentPlan: plan.name === currentPlan
  }));

  const shouldShowUpgradeButton = (targetPlan: string, currentPlan: string) => {
    const planRank = { 'Basic': 1, 'Pro': 2, 'Premium': 3 };
    const targetRank = planRank[targetPlan as keyof typeof planRank];
    const currentRank = planRank[currentPlan as keyof typeof planRank];
    
    // Don't show any buttons for the current plan
    if (targetPlan === currentPlan) return false;
    
    // If on Premium, don't show any buttons
    if (currentPlan === 'Premium') return false;
    
    // If on Pro:
    // - Show upgrade button for Premium
    // - Don't show downgrade button for Basic
    if (currentPlan === 'Pro') {
      return targetRank > currentRank;
    }
    
    // If on Basic:
    // - Show upgrade buttons for Pro and Premium
    if (currentPlan === 'Basic') {
      return targetRank > currentRank;
    }
    
    return false;
  };

  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Business Subscription Plans</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Subscriptions are managed at the business level, giving all team members access to the same powerful features.
            {!isAdmin && " Only business administrators can upgrade subscription plans."}
          </p>
        </div>

        {!isAdmin && (
          <Alert className="mb-8 max-w-2xl mx-auto">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Team Member Information</AlertTitle>
            <AlertDescription>
              You're viewing the subscription options as a team member. Subscription plans are managed at the business level.
              Please contact your business administrator to discuss upgrading your business subscription.
            </AlertDescription>
          </Alert>
        )}

        {/* Pricing Cards */}
        <div className="grid gap-8 md:grid-cols-3 mb-12">
          {updatedPlans.map((plan) => (
            <Card 
              key={plan.name} 
              className={`relative ${plan.className} ${
                plan.currentPlan ? 
                  'border-2 border-primary' : 
                  'border border-gray-200'
              } transition-all duration-200 hover:shadow-xl`}
            >
              {plan.popular && plan.name === "Pro" && currentPlan !== "Premium" && (
                <div className="absolute top-2 right-2">
                  <Badge variant="default" className="bg-primary text-primary-foreground">
                    <Sparkles className="w-4 h-4 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              {plan.currentPlan && (
                <div className="absolute -top-4 left-0 right-0 flex justify-center">
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-500">
                    Current Plan
                  </Badge>
                </div>
              )}
              <CardHeader>
                <CardTitle className={`text-2xl ${plan.className.includes('text-white') ? 'text-white' : ''}`}>
                  {plan.name}
                </CardTitle>
                <CardDescription className={plan.className.includes('text-white') ? 'text-white/80' : ''}>
                  {plan.description}
                  <p className={`mt-2 text-2xl font-bold ${plan.className.includes('text-white') ? 'text-white' : ''}`}>
                    {plan.price}
                  </p>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {plan.features.map((feature) => (
                    <div key={feature.name} className="flex items-center gap-2">
                      {feature.included ? (
                        <Check className={`h-5 w-5 flex-shrink-0 ${plan.className.includes('text-white') ? 'text-white' : 'text-green-500'}`} />
                      ) : (
                        <X className="h-5 w-5 flex-shrink-0 text-red-500" />
                      )}
                      <span className={`text-sm ${plan.className.includes('text-white') ? 'text-white/90' : ''}`}>
                        {feature.name}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                {plan.currentPlan ? (
                  <div className="w-full space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          Team Members: {teamMemberCount} of {
                            getTeamMemberLimit(plan.name) === Infinity 
                              ? "Unlimited" 
                              : getTeamMemberLimit(plan.name)
                          }
                        </span>
                      </div>
                      {plan.name !== "Premium" && isAdmin && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleUpgradeClick(plan.name === "Basic" ? "Pro" : "Premium")}
                          className="ml-2"
                        >
                          Upgrade
                        </Button>
                      )}
                    </div>
                    
                    {plan.name !== "Premium" && (
                      <>
                        <Progress 
                          value={Math.min(100, (teamMemberCount / getTeamMemberLimit(plan.name)) * 100)}
                          className={
                            plan.name === "Basic" && teamMemberCount === 1
                              ? "text-green-500"
                              : teamMemberCount >= getTeamMemberLimit(plan.name)
                                ? "text-red-500"
                                : plan.name === "Pro" ? "text-blue-500" : ""
                          }
                        />
                        {teamMemberCount >= getTeamMemberLimit(plan.name) && (
                          <div className={`text-xs flex items-center ${
                            plan.name === "Basic" && teamMemberCount === 1
                              ? "text-green-600"
                              : "text-red-600"
                          }`}>
                            {plan.name === "Basic" && teamMemberCount === 1 ? (
                              <>
                                <Check className="h-3 w-3 mr-1" />
                                Basic plan includes 1 team member
                              </>
                            ) : (
                              <>
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Team member limit reached
                                {isAdmin && (
                                  <Button 
                                    variant="link" 
                                    size="sm" 
                                    className="text-xs p-0 h-auto ml-1"
                                    onClick={() => handleUpgradeClick(plan.name === "Basic" ? "Pro" : "Premium")}
                                  >
                                    Upgrade to {plan.name === "Basic" ? "Pro" : "Premium"}
                                  </Button>
                                )}
                              </>
                            )}
                          </div>
                        )}
                        {teamMemberCount < getTeamMemberLimit(plan.name) && (
                          <div className="text-xs text-muted-foreground">
                            {plan.name === "Basic" 
                              ? "Limited to 1 team member on the Basic plan" 
                              : `Can add ${getTeamMemberLimit(plan.name) - teamMemberCount} more team member${getTeamMemberLimit(plan.name) - teamMemberCount !== 1 ? 's' : ''}`}
                          </div>
                        )}
                      </>
                    )}
                    {plan.name === "Premium" && (
                      <div className="text-xs text-muted-foreground">
                        Premium plan includes unlimited team members
                      </div>
                    )}
                  </div>
                ) : (
                  shouldShowUpgradeButton(plan.name, currentPlan) && (
                    <Button 
                      className="w-full" 
                      variant={plan.buttonVariant}
                      onClick={() => handleUpgradeClick(plan.name)}
                      disabled={!isAdmin}
                    >
                      {getButtonText(plan.name, currentPlan)}
                    </Button>
                  )
                )}
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
          <Accordion type="single" collapsible>
            <AccordionItem value="business-sub">
              <AccordionTrigger>
                How do business subscriptions work?
              </AccordionTrigger>
              <AccordionContent>
                Subscriptions are tied to your business account, not individual users. When your business upgrades to a higher tier, all team members automatically gain access to the new features. This ensures a consistent experience across your organization while simplifying billing and management.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="billing">
              <AccordionTrigger>
                How does billing work?
              </AccordionTrigger>
              <AccordionContent>
                Your business is billed monthly on the date you upgrade. When you upgrade, we'll prorate the amount for the remainder of your current billing period. Only business administrators can manage billing and subscription changes.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="cancel">
              <AccordionTrigger>
                Can we cancel anytime?
              </AccordionTrigger>
              <AccordionContent>
                Yes, your business administrator can cancel your subscription at any time. Your team will continue to have access to your plan features until the end of your current billing period, after which you'll be downgraded to the Basic plan.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="documents">
              <AccordionTrigger>
                What are document uploads and URL scraping?
              </AccordionTrigger>
              <AccordionContent>
                Document uploads allow you to train the AI with your business-specific information by uploading files (PDFs, DOCs, etc.). URL scraping automatically extracts content from your website pages to train the AI. Both features enhance your AI assistant's knowledge about your specific business, enabling more accurate and helpful responses to customer inquiries.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="llm">
              <AccordionTrigger>
                What's the difference between standard and premium AI models?
              </AccordionTrigger>
              <AccordionContent>
                Standard models provide excellent general-purpose AI capabilities. Premium models offer enhanced reasoning abilities, better context understanding, and more sophisticated responses, particularly beneficial for complex industries or specialized knowledge domains where nuanced understanding is crucial.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        {/* Need Help Section */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-muted-foreground" />
            <span className="text-muted-foreground">
              Need help choosing a plan? <a href="/contact" className="underline">Contact our sales team</a>
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

