import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, File, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import api from '@/utils/api';

interface FileWithPreview extends File {
  preview?: string;
  category?: string;
}

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'image/*': ['.png', '.jpg', '.jpeg'],
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const CATEGORIES = [
  { value: 'faq', label: 'FAQs' },
  { value: 'manual', label: 'Product Manuals' },
  { value: 'policy', label: 'Policies' },
  { value: 'other', label: 'Other' },
];

export default function DocumentUpload() {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const { toast } = useToast();

  const onDrop = (acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => 
      Object.assign(file, {
        preview: file.type.startsWith('image/') 
          ? URL.createObjectURL(file)
          : undefined,
        category: 'other'
      })
    );
    setFiles(prev => [...prev, ...newFiles]);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(({ file, errors }) => {
        const errorMessages = errors.map(error => {
          switch (error.code) {
            case 'file-too-large':
              return `File ${file.name} is too large. Max size is 10MB.`;
            case 'file-invalid-type':
              return `File ${file.name} has an invalid type. Accepted types are PDF, DOC, DOCX, and images.`;
            default:
              return `File ${file.name} was rejected: ${error.message}`;
          }
        });
        toast({
          variant: "destructive",
          title: "Upload Error",
          description: errorMessages.join('\n'),
        });
      });
    }
  });

  const removeFile = (fileToRemove: FileWithPreview) => {
    setFiles(files => files.filter(file => file !== fileToRemove));
    if (fileToRemove.preview) {
      URL.revokeObjectURL(fileToRemove.preview);
    }
  };

  const updateFileCategory = (fileToUpdate: FileWithPreview, category: string) => {
    setFiles(files => files.map(file => 
      file === fileToUpdate ? { ...file, category } : file
    ));
  };

  const handleUpload = async () => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files[]', file);
      formData.append('categories[]', file.category || 'other');
    });

    try {
      await api.post('/api/documents/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      toast({
        title: "Success",
        description: "Documents uploaded successfully",
      });
      setFiles([]);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Upload Failed",
        description: "Failed to upload documents. Please try again.",
      });
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Upload Documents</CardTitle>
          <CardDescription>
            Upload your business FAQs, product manuals, or other customer-facing materials here to help improve Aida's knowledge base.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            {...getRootProps()} 
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-primary'}`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">
              Drag and drop files here, or click to select files
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Supported formats: PDF, DOC, DOCX, PNG, JPG (max 10MB)
            </p>
          </div>

          {files.length > 0 && (
            <div className="mt-6 space-y-4">
              <div className="font-medium">Selected Files:</div>
              {files.map((file, index) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                  <File className="h-8 w-8 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select
                      value={file.category}
                      onValueChange={(value) => updateFileCategory(file, value)}
                    >
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent>
                        {CATEGORIES.map(category => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeFile(file)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              <div className="flex justify-end mt-4">
                <Button onClick={handleUpload} disabled={files.length === 0}>
                  Upload {files.length} {files.length === 1 ? 'file' : 'files'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 