import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, Table, Button, Input, message, Space, Row, Col, Modal } from 'antd';
import { useParams } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { UploadOutlined } from '@ant-design/icons';
import api from '../../utils/api';

const { TextArea } = Input;

interface Document {
  id: number;
  original_filename: string;
  file_type: string;
  description: string;
  uploaded_at: string;
  uploaded_by_username: string;
  file_size: number;
  llm_processed: boolean;
  llm_processing_error: string;
}

export default function DocumentList() {
  const { accountId } = useParams<{ accountId: string }>();
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const queryClient = useQueryClient();

  const { data: documents = [], isLoading } = useQuery({
    queryKey: ['documents', accountId],
    queryFn: () => api.get(`/api/documents/?account=${accountId}`).then(res => res.data),
  });

  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      return api.post('/api/documents/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', accountId] });
      message.success('File uploaded successfully');
      setDescription('');
      setSelectedFile(null);
    },
    onError: (error: any) => {
      const errorData = error.response?.data;
      if (errorData) {
        // Handle validation errors
        if (typeof errorData === 'object') {
          Object.entries(errorData).forEach(([field, errors]) => {
            if (Array.isArray(errors)) {
              errors.forEach((err) => message.error(`${field}: ${err}`));
            }
          });
        } else {
          // Handle permission denied or other errors
          message.error(errorData.detail || 'Failed to upload file');
        }
      } else {
        message.error('Failed to upload file');
      }
    },
  });

  const retryProcessingMutation = useMutation({
    mutationFn: (documentId: number) => {
      return api.post(
        `/api/documents/${documentId}/retry_llm_processing/?account=${accountId}`
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', accountId] });
      message.success('Processing retry initiated');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.error || 'Failed to retry processing');
    }
  });

  const replaceDocumentMutation = useMutation({
    mutationFn: async ({ documentId, file }: { documentId: number; file: File }) => {
      const formData = new FormData();
      formData.append('file', file);
      return api.post(
        `/api/documents/${documentId}/replace_document/?account=${accountId}`, 
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', accountId] });
      message.success('Document replaced successfully');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.error || 'Failed to replace document');
    },
  });

  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: number) => {
      try {
        const response = await api.delete(
          `/api/documents/${documentId}/?account=${accountId}`
        );
        console.log('Delete response:', response);
        return response;
      } catch (error: any) {
        console.error('Delete error:', error);
        console.error('Response:', error.response);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', accountId] });
      message.success('Document deleted successfully');
    },
    onError: (error: any) => {
      console.error('Delete mutation error:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Failed to delete document';
      message.error(errorMessage);
    },
  });

  const handleReplaceDocument = async (documentId: number) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.txt,.md,.doc,.docx,.html';
    input.onchange = (e: Event) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        replaceDocumentMutation.mutate({ documentId, file });
      }
    };
    input.click();
  };

  const handleDeleteDocument = (documentId: number) => {
    console.log('Handling delete for document:', documentId);
    if (window.confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      deleteDocumentMutation.mutate(documentId);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setSelectedFile(file);
    }
  }, []);

  const handleUpload = () => {
    if (!selectedFile) {
      message.error('Please select a file first');
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('description', description);
    if (accountId) {
      formData.append('account', accountId);
    } else {
      message.error('No account selected');
      return;
    }

    uploadMutation.mutate(formData);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/html': ['.html'],
    },
    multiple: false,
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns = [
    {
      title: 'Filename',
      dataIndex: 'original_filename',
      key: 'original_filename',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Size',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: 'Uploaded',
      dataIndex: 'uploaded_at',
      key: 'uploaded_at',
      render: (date: string) => formatDistanceToNow(new Date(date), { addSuffix: true }),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Document) => (
        <Space>
          <Button
            type="primary"
            onClick={() => handleReplaceDocument(record.id)}
          >
            Replace
          </Button>
          <Button
            danger
            onClick={() => handleDeleteDocument(record.id)}
          >
            Delete
          </Button>
          {record.llm_processing_error && (
            <Button
              onClick={() => retryProcessingMutation.mutate(record.id)}
            >
              Retry Processing
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="Upload Document" style={{ marginBottom: 24 }}>
        <div
          {...getRootProps()}
          style={{
            border: '2px dashed #d9d9d9',
            borderRadius: '8px',
            padding: '20px',
            textAlign: 'center',
            cursor: 'pointer',
            marginBottom: '16px',
            background: isDragActive ? '#fafafa' : 'white',
          }}
        >
          <input {...getInputProps()} />
          {isDragActive ? (
            <p>Drop the file here ...</p>
          ) : (
            <p>Drag and drop a file here, or click to select a file</p>
          )}
          <p style={{ color: '#666', fontSize: '14px', marginTop: '8px' }}>
            Supported formats: PDF, TXT, MD, DOC, DOCX, HTML
          </p>
        </div>
        {selectedFile && (
          <p style={{ marginBottom: '16px' }}>
            Selected file: {selectedFile.name}
          </p>
        )}
        <Row gutter={16}>
          <Col flex="auto">
            <Input
              placeholder="Enter file description (max 50 characters)"
              value={description}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 50) {
                  setDescription(value);
                }
              }}
              maxLength={50}
            />
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={handleUpload}
              loading={uploadMutation.isPending}
              disabled={!selectedFile}
            >
              Upload
            </Button>
          </Col>
        </Row>
      </Card>

      <Card title="Documents">
        <Table
          columns={columns}
          dataSource={documents}
          loading={isLoading}
          rowKey="id"
        />
      </Card>
    </div>
  );
}
