import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Divider, List, Tag } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { toast } from 'sonner';
import api from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text, Paragraph } = Typography;

type ApiCheckResult = {
  endpoint: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  data?: any;
};

/**
 * APICheckPage is a diagnostic component that helps verify API connectivity
 * and authentication status. It's especially useful for debugging auth issues.
 */
const ApiCheckPage: React.FC = () => {
  const { user, userType, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<ApiCheckResult[]>([]);

  const runApiCheck = async () => {
    setLoading(true);
    setResults([]);
    
    // Check auth status
    const authCheck: ApiCheckResult = {
      endpoint: 'Auth Status',
      status: isAuthenticated ? 'success' : 'error',
      message: isAuthenticated 
        ? `Authenticated as ${user?.email}` 
        : 'Not authenticated',
      data: {
        user: user,
        userType: userType,
        token: localStorage.getItem('token') ? 'Present' : 'Missing'
      }
    };
    
    setResults([authCheck]);
    
    // API Header check
    const headerCheck: ApiCheckResult = {
      endpoint: 'API Headers',
      status: api.defaults.headers.common['Authorization'] ? 'success' : 'error',
      message: api.defaults.headers.common['Authorization'] 
        ? 'Authorization header is set' 
        : 'Authorization header is missing',
      data: {
        headers: api.defaults.headers.common
      }
    };
    
    setResults(prev => [...prev, headerCheck]);
    
    // Check user endpoint
    try {
      setResults(prev => [
        ...prev,
        {
          endpoint: '/api/auth/user/',
          status: 'loading' as const,
          message: 'Checking user endpoint...'
        }
      ]);
      
      // Use the diagnostic parameter to prevent redirects on failure
      const userResponse = await api.get('/api/auth/user/', { 
        params: { isCheck: true } 
      });
      
      setResults(prev => prev.map(item => 
        item.endpoint === '/api/auth/user/' 
          ? {
              ...item,
              status: 'success' as const,
              message: 'User endpoint responded successfully',
              data: userResponse.data
            }
          : item
      ));
      
      // Add a user groups check
      setResults(prev => [
        ...prev,
        {
          endpoint: 'User Groups',
          status: 'success' as const,
          message: 'Checking user group memberships',
          data: {
            groups: user?.groups || [],
            isSuperuser: user?.is_superuser || false,
            isBusinessAdmin: user?.groups?.includes('Business Admins') || false,
            isRegularUser: user?.groups?.includes('Business Regular Users') || false,
            userType: userType
          }
        }
      ]);
      
    } catch (error: any) {
      setResults(prev => prev.map(item => 
        item.endpoint === '/api/auth/user/' 
          ? {
              ...item,
              status: 'error' as const,
              message: error.response 
                ? `Error ${error.response.status}: ${JSON.stringify(error.response.data)}` 
                : error.message,
              data: error.response?.data
            }
          : item
      ));
    }
    
    // Check accounts endpoint
    try {
      setResults(prev => [
        ...prev,
        {
          endpoint: '/api/accounts/',
          status: 'loading' as const,
          message: 'Checking accounts endpoint...'
        }
      ]);
      
      const accountsResponse = await api.get('/api/accounts/');
      
      setResults(prev => prev.map(item => 
        item.endpoint === '/api/accounts/' 
          ? {
              ...item,
              status: 'success' as const,
              message: `Found ${accountsResponse.data.length} accounts`,
              data: accountsResponse.data
            }
          : item
      ));
    } catch (error: any) {
      setResults(prev => prev.map(item => 
        item.endpoint === '/api/accounts/' 
          ? {
              ...item,
              status: 'error' as const,
              message: error.response 
                ? `Error ${error.response.status}: ${JSON.stringify(error.response.data)}` 
                : error.message,
              data: error.response?.data
            }
          : item
      ));
    }
    
    setLoading(false);
  };

  // Run the check automatically when the component mounts
  useEffect(() => {
    runApiCheck();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <Card className="shadow-sm">
        <Title level={2}>API Authentication Check</Title>
        <Paragraph>
          This page helps diagnose API connectivity and authentication issues.
          It will show if your session is working correctly across the application.
        </Paragraph>
        
        <Divider />
        
        <div className="my-4">
          <Button 
            type="primary" 
            onClick={runApiCheck} 
            loading={loading}
            disabled={loading}
          >
            Run Authentication Check
          </Button>
        </div>
        
        <List
          dataSource={results}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  item.status === 'success' ? (
                    <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 24 }} />
                  ) : item.status === 'error' ? (
                    <CloseCircleOutlined style={{ color: '#f5222d', fontSize: 24 }} />
                  ) : (
                    <LoadingOutlined style={{ color: '#1890ff', fontSize: 24 }} />
                  )
                }
                title={
                  <div>
                    <Text strong>{item.endpoint}</Text>
                    <Tag 
                      color={
                        item.status === 'success' ? 'success' : 
                        item.status === 'error' ? 'error' : 'processing'
                      }
                      className="ml-2"
                    >
                      {item.status.toUpperCase()}
                    </Tag>
                  </div>
                }
                description={item.message}
              />
              {item.data && (
                <div className="ml-8 mt-2">
                  <Text type="secondary">Response Data:</Text>
                  <pre className="mt-1 bg-gray-100 p-2 rounded text-xs overflow-auto">
                    {JSON.stringify(item.data, null, 2)}
                  </pre>
                </div>
              )}
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default ApiCheckPage; 