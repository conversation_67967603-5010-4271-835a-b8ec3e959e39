import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useSearchParams } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Copy, CheckCircle2, Upload as UploadOutlined } from "lucide-react";
import { Upload } from "antd";
import { toast } from "sonner";
import type { LucideIcon } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import api from "@/utils/api";
import { Input } from "@/components/ui/input";
import { getIndustryLabel } from '../../utils/industries';

// Define Account interface

interface Account {
  id: number;
  uuid: string;
  account_name: string;
  industry_name: string;
  status: string;
  chat_icon?: string;
  config?: {
    greeting_message: string;
    chat_title?: string;
    primary_color: string;
    secondary_color: string;
    order_taking_enabled?: boolean;
    order_taking_email?: string;
    theme?: string;
  };
}

const CopyIcon = Copy as LucideIcon;
const CheckCircle2Icon = CheckCircle2 as LucideIcon;

const WIDGET_VERSION = "1.0.0";

interface CodeSnippet {
  title: string;
  description: string;
  code: string;
  location: string;
}

interface Framework {
  id: string;
  name: string;
  icon?: string;
  snippets: CodeSnippet[];
}

// CSS for theme previews
const themePreviewStyles = `
  .theme-preview {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 220px;
  }

  .preview-header {
    padding: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
  }

  .preview-header-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .preview-header-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .preview-messages {
    padding: 10px;
    min-height: 120px;
  }

  .preview-message {
    padding: 8px 12px;
    border-radius: 12px;
    margin-bottom: 8px;
    max-width: 85%;
    word-break: break-word;
    font-size: 0.875rem;
  }

  .preview-message.assistant {
    border-top-left-radius: 4px;
    margin-right: auto;
  }

  .preview-message.user {
    border-top-right-radius: 4px;
    margin-left: auto;
  }
`;

export default function AidaSetup() {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const [, setActiveFramework] = useState("vanilla");
  const [copiedSnippet, setCopiedSnippet] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [greetingMessage, setGreetingMessage] = useState("Hello! How can I help you today?");
  const [isUpdatingGreeting, setIsUpdatingGreeting] = useState(false);
  const [chatTitle, setChatTitle] = useState("AiDA");
  const [isUpdatingChatTitle, setIsUpdatingChatTitle] = useState(false);
  const [orderTakingEnabled, setOrderTakingEnabled] = useState(false);
  const [orderTakingEmail, setOrderTakingEmail] = useState("");
  const [isUpdatingOrderSettings, setIsUpdatingOrderSettings] = useState(false);
  const [theme, setTheme] = useState<string>("light");
  const [isUpdatingTheme, setIsUpdatingTheme] = useState(false);

  // Fetch user's accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await api.get('/api/accounts/');
        const accountsData = response.data || [];
        console.log('Fetched accounts with config:', accountsData);
        setAccounts(accountsData);

        // Get accountId from URL if present
        const urlAccountId = searchParams.get('accountId');
        if (urlAccountId) {
          const accountFromUrl = accountsData.find((a: Account) => a.id.toString() === urlAccountId);
          if (accountFromUrl) {
            console.log('Setting account from URL with config:', accountFromUrl);
            setSelectedAccount(accountFromUrl);
            if (accountFromUrl.config) {
              setGreetingMessage(accountFromUrl.config.greeting_message);
              setChatTitle(accountFromUrl.config.chat_title || 'AiDA');
              setOrderTakingEnabled(accountFromUrl.config.order_taking_enabled);
              setOrderTakingEmail(accountFromUrl.config.order_taking_email || '');
              setTheme(accountFromUrl.config.theme || 'light');
            }
            return;
          }
        }

        // Fallback to first account
        if (accountsData.length > 0) {
          const firstAccount = accountsData[0];
          console.log('Setting initial account with config:', firstAccount);
          setSelectedAccount(firstAccount);
          if (firstAccount.config) {
            setGreetingMessage(firstAccount.config.greeting_message);
            setChatTitle(firstAccount.config.chat_title || 'AiDA');
            setOrderTakingEnabled(firstAccount.config.order_taking_enabled);
            setOrderTakingEmail(firstAccount.config.order_taking_email || '');
            setTheme(firstAccount.config.theme || 'light');
          }
        }
      } catch (error: unknown) {
        console.error('Error fetching accounts:', error);
        toast.error('Failed to load accounts. Please try refreshing the page.');
      }
    };

    if (user) {
      fetchAccounts();
    }
  }, [user, searchParams]);

  // State for rendering

  const WIDGET_BASE = `https://aida.research-triangle.ai/widget/${selectedAccount?.uuid}`;

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>Please log in to view setup instructions.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const frameworks: Framework[] = [
    {
      id: "vanilla",
      name: "HTML",
      snippets: [
        {
          title: "Header Script",
          description: "Add this script to your page's <head> section",
          location: "<head>",
          code: `<link href="${WIDGET_BASE}/assets/index.css" rel="stylesheet" type="text/css" />
<script src="${WIDGET_BASE}/assets/main.js"></script>`
        },
        {
          title: "Body Integration",
          description: "Add this div where you want the Aida widget to appear",
          location: "<body>",
          code: '<div id="aida-root"></div>'
        }
      ]
    }
  ];

  const handleSaveGreeting = async () => {
    if (!selectedAccount) return;

    setIsUpdatingGreeting(true);
    try {
      await api.patch(`/api/accounts/${selectedAccount?.id}/config/`, {
        greeting_message: greetingMessage
      });
      toast.success('Greeting message updated');
    } catch (error: unknown) {
      console.error('Error updating greeting:', error);
      toast.error('Failed to update greeting message');
    } finally {
      setIsUpdatingGreeting(false);
    }
  };

  const handleSaveChatTitle = async () => {
    if (!selectedAccount) return;

    setIsUpdatingChatTitle(true);
    try {
      await api.patch(`/api/accounts/${selectedAccount?.id}/config/`, {
        chat_title: chatTitle
      });
      toast.success('Chat title updated');
    } catch (error: unknown) {
      console.error('Error updating chat title:', error);
      toast.error('Failed to update chat title');
    } finally {
      setIsUpdatingChatTitle(false);
    }
  };

  const handleSaveOrderSettings = async () => {
    if (!selectedAccount) return;

    // Validate email if order taking is enabled
    if (orderTakingEnabled && !orderTakingEmail) {
      toast.error('Please enter an email address for order notifications');
      return;
    }

    setIsUpdatingOrderSettings(true);
    try {
      await api.patch(`/api/accounts/${selectedAccount?.id}/config/`, {
        order_taking_enabled: orderTakingEnabled,
        order_taking_email: orderTakingEmail
      });
      toast.success('Order taking settings updated');
    } catch (error: unknown) {
      console.error('Error updating order settings:', error);
      toast.error('Failed to update order taking settings');
    } finally {
      setIsUpdatingOrderSettings(false);
    }
  };

  const handleSaveTheme = async () => {
    if (!selectedAccount) return;

    setIsUpdatingTheme(true);
    try {
      const url = `/api/accounts/${selectedAccount?.id}/config/`;
      console.log('Making request to URL:', url);
      
      // Use simple, direct approach
      const response = await api.patch(url, {
        theme: theme
      });
      
      console.log('Response:', response);
      toast.success('Theme updated successfully');
    } catch (error: unknown) {
      console.error('Error updating theme:', error);
      // Add more detailed error logging
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number; data: any } };
        if (axiosError.response) {
          console.error('Response status:', axiosError.response.status);
          console.error('Response data:', axiosError.response.data);
        }
      }
      toast.error('Failed to update theme');
    } finally {
      setIsUpdatingTheme(false);
    }
  };

  const handleAccountSelection = (accountId: string) => {
    const account = accounts.find((a: Account) => a.id.toString() === accountId);
    if (account) {
      setSelectedAccount(account);
      setGreetingMessage(account.config?.greeting_message || "Hello! How can I help you today?");
      setChatTitle(account.config?.chat_title || "AiDA");
      setOrderTakingEnabled(account.config?.order_taking_enabled || false);
      setOrderTakingEmail(account.config?.order_taking_email || "");
      setTheme(account.config?.theme || "light");
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <style dangerouslySetInnerHTML={{ __html: themePreviewStyles }} />
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Aida Widget Setup</h1>
          <p className="text-muted-foreground mt-2">
            Follow these steps to add the Aida widget to your website
          </p>
          <a
            href={`/demo?account=${selectedAccount?.uuid}`}
            className="text-primary underline inline-block mt-2"
            target="_blank"
            rel="noopener noreferrer"
          >
            Click here to see how AIDA works on your website
          </a>
        </div>
        <Badge variant="outline">v{WIDGET_VERSION}</Badge>
      </div>

      {accounts.length > 0 && (
        <Card className="mb-8 account-selector-card">
          <CardHeader>
            <CardTitle>Select Account</CardTitle>
            <CardDescription>Choose which account to generate setup instructions for</CardDescription>
          </CardHeader>
          <CardContent className="account-selector-content">
            <Select
              value={selectedAccount?.id.toString()}
              onValueChange={handleAccountSelection}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select an account" />
              </SelectTrigger>
              <SelectContent>
                {accounts.map((account) => (
                  <SelectItem key={account.id} value={account.id.toString()}>
                    {account.account_name} ({getIndustryLabel(account.industry_name)})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Chat Appearance Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Chat Appearance</CardTitle>
          <CardDescription>Customize how your chat widget appears to users</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Chat Title Subsection */}
            <div>
              <h3 className="font-medium mb-2">Chat Title</h3>
              <div className="flex gap-4 items-start">
                <Input
                  value={chatTitle}
                  onChange={(e) => setChatTitle(e.target.value)}
                  placeholder="Enter chat title"
                  className="max-w-md"
                />
                <Button
                  onClick={handleSaveChatTitle}
                  disabled={isUpdatingChatTitle}
                >
                  {isUpdatingChatTitle ? 'Saving...' : 'Save'}
                </Button>
              </div>
              <div className="text-sm text-muted-foreground mt-2">
                <p>This is the title displayed in the chat window's header</p>
              </div>
            </div>

            {/* Chat Icon Subsection */}
            <div className="border-t pt-6">
              <h3 className="font-medium mb-4">Chat Icon</h3>
              
              {selectedAccount?.chat_icon && (
                <div className="space-y-4 mb-4">
                  <h4 className="text-sm font-medium">Current Icon</h4>
                  <div className="flex items-center gap-4">
                    <img
                      src={selectedAccount.chat_icon}
                      alt="Current chat icon"
                      className="w-24 h-24 rounded-lg object-contain"
                    />
                    <Button
                      variant="destructive"
                      onClick={async () => {
                        if (!selectedAccount) return;
                        try {
                          // Use direct API call instead of api.accounts
                          await api.post(`/api/accounts/${selectedAccount.id}/remove-chat-icon/`);
                          setSelectedAccount(prev => ({
                            ...prev!,
                            chat_icon: undefined
                          }));
                          toast.success('Chat icon removed successfully!');
                        } catch (error) {
                          toast.error('Failed to remove chat icon');
                        }
                      }}
                    >
                      Reset Icon
                    </Button>
                  </div>
                </div>
              )}

              <Upload
                name="file"
                showUploadList={false}
                beforeUpload={(file) => {
                  const isImage = ['image/svg+xml', 'image/jpeg', 'image/png'].includes(file.type);
                  if (!isImage) {
                    toast.error('You can only upload SVG, JPG, or PNG files!');
                  }
                  const isLt2M = file.size / 1024 / 1024 < 2;
                  if (!isLt2M) {
                    toast.error('Image must be smaller than 2MB!');
                  }
                  return isImage && isLt2M;
                }}
                customRequest={async ({ file }) => {
                  if (!selectedAccount) return;

                  const formData = new FormData();
                  formData.append('file', file);

                  try {
                    // Use direct API call instead of api.accounts
                    const response = await api.post(
                      `/api/accounts/${selectedAccount.id}/upload-chat-icon/`,
                      formData,
                      {
                        headers: {
                          'Content-Type': 'multipart/form-data'
                        }
                      }
                    );
                    setSelectedAccount(prev => ({
                      ...prev!,
                      chat_icon: response.data.icon_url
                    }));
                    toast.success('Chat icon uploaded successfully!');
                  } catch (error) {
                    toast.error('Failed to upload chat icon');
                  }
                }}
              >
                <Button>
                  <UploadOutlined className="mr-2" />
                  Upload New Icon
                </Button>
              </Upload>

              <div className="text-sm text-muted-foreground mt-3">
                <p>Supported formats: SVG, JPG, PNG</p>
                <p>Maximum file size: 2MB</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Greeting Message Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Greeting Message</CardTitle>
          <CardDescription>Customize the initial message shown to users</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-start">
            <Input
              value={greetingMessage}
              onChange={(e) => setGreetingMessage(e.target.value)}
              placeholder="Enter greeting message"
              className="max-w-md"
            />
            <Button
              onClick={handleSaveGreeting}
              disabled={isUpdatingGreeting}
            >
              {isUpdatingGreeting ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </CardContent>
      </Card>



      {/* Order Taking Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Order Taking</CardTitle>
          <CardDescription>Enable AIDA to take orders from your customers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="orderTakingEnabled"
                checked={orderTakingEnabled}
                onChange={(e) => setOrderTakingEnabled(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label htmlFor="orderTakingEnabled" className="text-sm font-medium">
                Enable order taking functionality
              </label>
            </div>
            
            {orderTakingEnabled && (
              <div className="space-y-2">
                <label htmlFor="orderEmail" className="text-sm font-medium">
                  Order Notification Email <span className="text-red-500">*</span>
                </label>
                <div className="flex gap-4 items-start">
                  <Input
                    id="orderEmail"
                    type="email"
                    value={orderTakingEmail}
                    onChange={(e) => setOrderTakingEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="max-w-md"
                    required
                  />
                  <Button
                    onClick={handleSaveOrderSettings}
                    disabled={isUpdatingOrderSettings}
                  >
                    {isUpdatingOrderSettings ? 'Saving...' : 'Save'}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Order details will be sent to this email address when customers place orders through the chat widget.
                </p>
              </div>
            )}
            
            {!orderTakingEnabled && (
              <Button
                onClick={handleSaveOrderSettings}
                disabled={isUpdatingOrderSettings}
              >
                {isUpdatingOrderSettings ? 'Saving...' : 'Save'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Theme Selection Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Chat Widget Theme</CardTitle>
          <CardDescription>Choose between light and dark mode for your chat widget</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              {/* Light Theme Option */}
              <div 
                className={`flex flex-col items-center space-y-2 border rounded-lg p-4 hover:bg-accent cursor-pointer ${theme === 'light' ? 'ring-2 ring-primary' : ''}`}
                onClick={() => setTheme('light')}
              >
                <div className="theme-preview light">
                  <div className="preview-header" style={{ backgroundColor: '#4a90e2', color: '#ffffff' }}>
                    <div className="preview-header-icon">
                      <img src={selectedAccount?.chat_icon || '/images/chat.png'} alt="Chat Icon" />
                    </div>
                    Chat Title
                  </div>
                  <div className="preview-messages" style={{ backgroundColor: '#f9fafb' }}>
                    <div className="preview-message assistant" style={{ backgroundColor: '#f3f4f6', color: '#374151' }}>Hello! How can I help?</div>
                    <div className="preview-message user" style={{ backgroundColor: '#2563eb', color: '#ffffff' }}>I have a question</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 mt-2">
                  <input 
                    type="radio" 
                    id="light" 
                    checked={theme === 'light'} 
                    onChange={() => setTheme('light')} 
                    className="h-4 w-4"
                  />
                  <label htmlFor="light" className="text-sm font-medium">Light Theme</label>
                </div>
              </div>
              
              {/* Dark Theme Option */}
              <div 
                className={`flex flex-col items-center space-y-2 border rounded-lg p-4 hover:bg-accent cursor-pointer ${theme === 'dark' ? 'ring-2 ring-primary' : ''}`}
                onClick={() => setTheme('dark')}
              >
                <div className="theme-preview dark">
                  <div className="preview-header" style={{ backgroundColor: '#111827', color: '#f9fafb' }}>
                    <div className="preview-header-icon">
                      <img src={selectedAccount?.chat_icon || '/images/chat.png'} alt="Chat Icon" />
                    </div>
                    Chat Title
                  </div>
                  <div className="preview-messages" style={{ backgroundColor: '#1f2937' }}>
                    <div className="preview-message assistant" style={{ backgroundColor: '#374151', color: '#f3f4f6' }}>Hello! How can I help?</div>
                    <div className="preview-message user" style={{ backgroundColor: '#3b82f6', color: '#ffffff' }}>I have a question</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 mt-2">
                  <input 
                    type="radio" 
                    id="dark" 
                    checked={theme === 'dark'} 
                    onChange={() => setTheme('dark')} 
                    className="h-4 w-4"
                  />
                  <label htmlFor="dark" className="text-sm font-medium">Dark Theme</label>
                </div>
              </div>
            </div>
            
            <Button
              onClick={handleSaveTheme}
              disabled={isUpdatingTheme}
            >
              {isUpdatingTheme ? 'Saving...' : 'Save Theme'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="vanilla" className="space-y-4" onValueChange={setActiveFramework}>
        {frameworks.map((framework) => (
          <TabsContent key={framework.id} value={framework.id}>
            <div className="space-y-4">
              {framework.snippets.map((snippet, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="text-xl">{snippet.title}</CardTitle>
                        <CardDescription>{snippet.description}</CardDescription>
                      </div>
                      <Badge variant="secondary" className="ml-2">v{WIDGET_VERSION}</Badge>
                    </div>
                    <Badge variant="secondary" className="mt-2">
                      Location: {snippet.location}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                        <code className="text-sm">{snippet.code}</code>
                      </pre>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          navigator.clipboard.writeText(snippet.code);
                          setCopiedSnippet(snippet.title);
                          toast.success("Code copied to clipboard!");
                          setTimeout(() => setCopiedSnippet(null), 2000);
                        }}
                      >
                        {copiedSnippet === snippet.title ? (
                          <CheckCircle2Icon className="h-4 w-4" />
                        ) : (
                          <CopyIcon className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Common Issues & Troubleshooting</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Widget not appearing?</h3>
              <ul className="list-disc list-inside text-muted-foreground">
                <li>Verify that all code snippets are properly placed in your HTML</li>
                <li>Check browser console for any JavaScript errors</li>
                <li>Ensure your client ID is valid and active</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Need Help?</h3>
              <p className="text-muted-foreground">
                Contact our support <NAME_EMAIL> or visit our{" "}
                <a href="/docs" className="text-primary hover:underline">
                  documentation
                </a>
                .
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
