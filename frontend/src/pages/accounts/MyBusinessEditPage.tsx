import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, message, Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import AccountForm, { AccountFormHandle } from '../../components/accounts/AccountForm';
import api from '../../utils/api';
import { Account } from '../../types/account';
import { toast } from 'sonner';
import { testGoogleMapsApiKey } from '../../utils/apiKeyTest';
import { parseIndustryValues } from '../../utils/industries';

// Interface for API key status
interface ApiKeyStatus {
  isValid: boolean;
  checked: boolean;
  message: string;
}

/**
 * MyBusinessEditPage provides a way for regular business users to edit their
 * business details. It reuses the same AccountForm component that the admin
 * EditAccount page uses, ensuring consistency between the interfaces.
 * Enhanced with Google Maps API integration for address validation.
 */
const MyBusinessEditPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [account, setAccount] = useState<Account | null>(null);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [duplicateError, setDuplicateError] = useState(false);
  const [isNewAccount, setIsNewAccount] = useState(false);
  const navigate = useNavigate();
  const addressInputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<any>(null);
  const formRef = useRef<AccountFormHandle>(null);
  
  // API key status for Google Maps API
  const [apiKeyStatus, setApiKeyStatus] = useState<ApiKeyStatus>({
    isValid: false,
    checked: false,
    message: ''
  });
  
  // Initialize a key to force re-render when account data changes
  const [formKey, setFormKey] = useState<string>(Date.now().toString());
  
  // Function to normalize an address for comparison to avoid minor variations affecting duplicate detection
  const normalizeAddress = (address: string = ''): string => {
    return address.toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .trim();
  };
  
  // Function to check if a business already exists with the same name and address
  const checkDuplicateBusiness = async (businessName: string, addressLine1: string): Promise<boolean> => {
    try {
      const normalizedName = businessName.trim().toLowerCase();
      const normalizedAddress = normalizeAddress(addressLine1);
      
      if (!normalizedName || !normalizedAddress) {
        return false;
      }
      
      // Call the API to check for duplicate business
      const response = await api.get('/api/accounts/check-duplicate', {
        params: {
          business_name: normalizedName,
          address_line1: normalizedAddress
        }
      });
      
      return response.data.duplicate;
    } catch (error) {
      console.error('Error checking for duplicate business:', error);
      return false;
    }
  };
  
  // Load Google Maps API script - memoized with useCallback
  const loadGoogleMapsScript = useCallback(() => {
    if (document.getElementById('google-maps-script')) {
      // Script already loaded, just initialize
      if (window.google && window.google.maps) {
        console.log('Google Maps already loaded, initializing with account state:', isNewAccount ? 'new account' : 'existing account');
        window.initGoogleMapsAutocomplete?.();
      }
      return;
    }

    // Determine the API key from environment variables
    // For Vite projects, use import.meta.env.VITE_*
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
    
    // Skip if no API key available
    if (!apiKey || apiKey === 'YOUR_API_KEY') {
      setApiKeyStatus({
        isValid: false,
        checked: true,
        message: 'Missing Google Maps API key'
      });
      return;
    }
    
    // Create the script element
    const script = document.createElement('script');
    script.id = 'google-maps-script';
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMapsAutocomplete`;
    script.async = true;
    script.defer = true;
    
    // Handle script load and error events
    script.addEventListener('load', () => {
      console.log('Google Maps API loaded successfully with account state:', isNewAccount ? 'new account' : 'existing account');
      setApiKeyStatus({
        isValid: true,
        checked: true,
        message: 'API Key is valid'
      });
    });
    
    script.addEventListener('error', (e) => {
      console.error('Error loading Google Maps API:', e);
      setApiKeyStatus({
        isValid: false,
        checked: true,
        message: 'Could not load Google Maps API'
      });
    });
    
    document.head.appendChild(script);
  }, [isNewAccount, setApiKeyStatus]);

  // Fetch the user's business account
  useEffect(() => {
    const fetchAccount = async () => {
      setFetchLoading(true);
      try {
        // Get the user's accounts directly from the accounts list endpoint
        // This endpoint appears to filter based on the user's permissions
        console.log('Fetching accounts list...');
        const accountsResponse = await api.get('/api/accounts/');
        console.log('Accounts list response:', accountsResponse.data);
        
        if (accountsResponse.data && accountsResponse.data.length > 0) {
          // Use the first account in the list
          const firstAccount = accountsResponse.data[0];
          console.log('Using first account from list:', firstAccount);
          setAccount(firstAccount);
          setFormKey(Date.now().toString());
        } else {
          console.log('No existing business account. Creating a new account form.');
          // Instead of redirecting, set account to empty so the form can be used to create a new account
          setAccount({
            id: 0, // Use 0 to represent a new account
            uuid: '',
            account_name: '',
            status: 'active',
            industry_name: '',
            primary_contact: '',
            primary_phone: '',
            primary_email: '',
            address_line1: '',
            address_line2: '',
            city: '',
            state: '',
            zip_code: '',
            created_at: '',
            updated_at: ''
          });
          setFormKey(Date.now().toString());
          setIsNewAccount(true);
          
          // Load Google Maps script immediately for new accounts
          loadGoogleMapsScript();
        }
      } catch (error) {
        console.error('Error fetching accounts:', error);
        toast.error('Failed to load business information');
        
        // Try the fallback approach of getting account through user endpoint
        try {
          console.log('Trying fallback approach via user endpoint...');
          const userResponse = await api.get('/api/users/current/');
          console.log('Current user data:', userResponse.data);
          const user = userResponse.data;
          
          if (user.accounts && user.accounts.length > 0) {
            const accountId = user.accounts[0].id;
            try {
              const accountResponse = await api.get(`/api/accounts/${accountId}/`);
              console.log('Account data received from fallback:', accountResponse.data);
              
              if (accountResponse.data) {
                setAccount(accountResponse.data);
                setFormKey(Date.now().toString());
              }
            } catch (accountError) {
              console.error('Fallback also failed:', accountError);
              navigate('/my-business');
            }
          } else {
            navigate('/my-business');
          }
        } catch (userError) {
          console.error('User fallback failed too:', userError);
          navigate('/my-business');
        }
      } finally {
        setFetchLoading(false);
      }
    };

    fetchAccount();
    
    // Also check Google Maps API key
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
    testGoogleMapsApiKey(apiKey).then((result) => {
      setApiKeyStatus({
        isValid: result.isValid && result.placesApiEnabled && result.mapsJsApiEnabled,
        checked: true,
        message: result.message
      });
    });
  }, [navigate]);

  // Update form key when account changes to force re-render
  useEffect(() => {
    if (account) {
      console.log('Setting new form key to force re-render');
      setFormKey(Date.now().toString());
    }
  }, [account]);

  // Initialize Google Maps Places Autocomplete
  useEffect(() => {
    // Only attempt to load Google Maps if the API key is valid (removed account check)
    if (apiKeyStatus.checked && !apiKeyStatus.isValid) {
      // Skip Google Maps initialization due to invalid API key
      return;
    }
    
    // Function to initialize Google Maps Autocomplete
    window.initGoogleMapsAutocomplete = () => {
      console.log('Attempting to initialize Google Maps Autocomplete...');
      console.log('Current account state:', isNewAccount ? 'Creating new account' : 'Editing existing account');
      
      // Use a function that retries finding the address input
      const initializeAutocomplete = () => {
        // Try to find the address input in multiple ways to be more reliable
        const addressInput = document.querySelector('input[id="address_line1"]') || 
                           document.querySelector('input[name="address_line1"]') ||
                           document.querySelector('form input[id$="_address_line1"]') ||
                           document.querySelector('.ant-form input[id$="address_line1"]');
                           
        console.log('Address input element found:', addressInput);
        
        if (!addressInput) {
          console.log('Address input not found yet, will retry in 500ms');
          setTimeout(initializeAutocomplete, 500);
          return;
        }
        
        // Check if Google Maps API is loaded correctly
        if (!window.google || !window.google.maps || !window.google.maps.places) {
          console.error('Google Maps Places API not loaded correctly');
          return;
        }
        
        try {
          // Check if autocomplete is already initialized to prevent duplicates
          if (autocompleteRef.current) {
            window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
          }
          
          console.log('Initializing Google Maps Autocomplete on element:', addressInput);
          autocompleteRef.current = new window.google.maps.places.Autocomplete(
            addressInput as HTMLInputElement,
            { 
              types: ['address'],
              componentRestrictions: { country: 'us' } 
            }
          );
          
          window.google.maps.event.addListener(
            autocompleteRef.current,
            'place_changed',
            () => {
              try {
                const place = autocompleteRef.current.getPlace();
                console.log('Place selected:', place);
                if (!place || !place.address_components) return;
                
                // Extract address components
                let streetNumber = '';
                let route = '';
                let city = '';
                let state = '';
                let zipCode = '';
                
                for (const component of place.address_components) {
                  const types = component.types;
                  
                  if (types.includes('street_number')) {
                    streetNumber = component.long_name;
                  } else if (types.includes('route')) {
                    route = component.long_name;
                  } else if (types.includes('locality')) {
                    city = component.long_name;
                  } else if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                  } else if (types.includes('postal_code')) {
                    zipCode = component.long_name;
                  }
                }
                
                // Create the formatted address
                const formattedAddress = `${streetNumber} ${route}`.trim();
                
                // Use the form ref to update form values directly
                if (formRef.current) {
                  console.log('Updating form values via ref');
                  formRef.current.setFieldsValue({
                    address_line1: formattedAddress,
                    city: city,
                    state: state,
                    zip_code: zipCode
                  });
                  console.log('Form values updated');
                } else {
                  console.error('Form ref not available');
                  
                  // Fallback to DOM-based approach
                  const updateInputValue = (selector: string, value: string) => {
                    const input = document.querySelector(`input[id="${selector}"]`) || 
                                 document.querySelector(`input[name="${selector}"]`);
                    
                    if (input) {
                      // Set the value
                      (input as HTMLInputElement).value = value;
                      
                      // Create and dispatch events
                      const inputEvent = new Event('input', { bubbles: true });
                      input.dispatchEvent(inputEvent);
                      
                      const changeEvent = new Event('change', { bubbles: true });
                      input.dispatchEvent(changeEvent);
                      
                      console.log(`Updated ${selector} to ${value}`);
                    } else {
                      console.error(`Could not find input for ${selector}`);
                    }
                  };
                  
                  // Update all address fields
                  updateInputValue('address_line1', formattedAddress);
                  updateInputValue('city', city);
                  updateInputValue('state', state);
                  updateInputValue('zip_code', zipCode);
                }
                
                setDuplicateError(false);
              } catch (err) {
                // Error handling place selection
                console.error('Error selecting place:', err);
              }
            }
          );
          
          console.log('Google Maps Autocomplete initialized successfully');
        } catch (error) {
          // Error initializing Google Maps Autocomplete
          console.error('Error initializing autocomplete:', error);
        }
      };
      
      // Start the retry process with an initial delay to give form time to render
      setTimeout(initializeAutocomplete, 1500);
    };
    
    // Load the Google Maps script
    console.log('Loading Google Maps script...');
    loadGoogleMapsScript();
    
    // Cleanup
    return () => {
      if (autocompleteRef.current && window.google && window.google.maps && window.google.maps.event) {
        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
        autocompleteRef.current = null;
      }
    };
  }, [apiKeyStatus.isValid, apiKeyStatus.checked, isNewAccount, loadGoogleMapsScript]);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      
      // Check for duplicate business
      if (isNewAccount || values.account_name !== account?.account_name || 
          normalizeAddress(values.address_line1) !== normalizeAddress(account?.address_line1)) {
        const isDuplicate = await checkDuplicateBusiness(values.account_name, values.address_line1);
        if (isDuplicate) {
          setDuplicateError(true);
          setLoading(false);
          return;
        }
      }
      
      // Format industry_name if it's an array
      if (Array.isArray(values.industry_name)) {
        values.industry_name = values.industry_name.join(', ');
      }
      
      let response;
      
      // If it's a new account, use POST to create it
      if (isNewAccount) {
        response = await api.post('/api/accounts/', values);
        toast.success('Business information created successfully!');
      } else {
        // For existing accounts, use PUT to update
        response = await api.put(`/api/accounts/${account?.id}/`, values);
        toast.success('Business information updated successfully!');
      }
      
      // If successful, update the local state and navigate back
      setAccount(response.data);
      setIsNewAccount(false);
      navigate('/my-business');
      
    } catch (error) {
      console.error(`Error ${isNewAccount ? 'creating' : 'updating'} business information:`, error);
      toast.error(`Failed to ${isNewAccount ? 'create' : 'update'} business information. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/my-business');
  };

  if (fetchLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" tip="Loading your business information..." />
      </div>
    );
  }

  if (!account) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" tip="Preparing the edit form..." />
      </div>
    );
  }

  return (
    <Card title={isNewAccount ? "Add Business Information" : "Edit Business Information"}>
      {duplicateError && (
        <div className="bg-amber-50 border border-amber-200 p-4 rounded mb-4">
          <p className="text-amber-800">
            A business with this name and address is already registered in our system. 
            If this is your business, please contact support.
          </p>
        </div>
      )}
      
      {!apiKeyStatus.isValid && apiKeyStatus.checked && (
        <div className="bg-amber-50 border border-amber-200 p-4 rounded mb-4">
          <p className="text-amber-800">
            Google Maps address suggestions are not available: {apiKeyStatus.message}
          </p>
        </div>
      )}
      
      {/* Debug information (hidden from view but kept for development purposes) */}
      {import.meta.env.MODE !== 'production' && (
        <div className="mb-4 p-2 border border-gray-200 rounded bg-gray-50 text-xs overflow-auto max-h-40" style={{ display: 'none' }}>
          <details>
            <summary className="font-bold cursor-pointer">Debug Info (click to expand)</summary>
            <pre>{JSON.stringify({formKey, account}, null, 2)}</pre>
          </details>
        </div>
      )}
      
      <AccountForm 
        ref={formRef}
        key={formKey} // Use dynamic key to force complete re-render with new initialValues
        initialValues={account} 
        onSubmit={handleSubmit} 
        onCancel={handleCancel} 
        loading={loading} 
        isAdmin={false} // Regular business users are not admins
      />
    </Card>
  );
};

export default MyBusinessEditPage; 