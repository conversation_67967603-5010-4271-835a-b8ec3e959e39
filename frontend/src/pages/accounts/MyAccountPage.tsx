import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Spin, Empty, Card, Typography, Descriptions, Button, Divider, Progress } from 'antd';
import { EditOutlined, MailOutlined, PhoneOutlined, HomeOutlined, GlobalOutlined, TeamOutlined } from '@ant-design/icons';
import { toast } from 'sonner';
import api from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';
import { Account } from '../../types/account';
import { getIndustryLabel } from '../../utils/industries';

const { Title, Text } = Typography;

/**
 * MyAccountPage is designed for regular business users to view and manage
 * their single business account, showing detailed information about the business
 * they're associated with.
 */
const MyAccountPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [account, setAccount] = useState<Account | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchUserAccount = async () => {
      try {
        console.log('MyAccountPage - Fetching user account');
        console.log('Current auth header:', api.defaults.headers.common['Authorization']);
        
        // Fetch accounts associated with the current user
        const response = await api.get('/api/accounts/');
        console.log('Accounts API response:', response.data);
        
        if (response.data && response.data.length > 0) {
          // For regular users, we show only the first account
          setAccount(response.data[0]);
          console.log('Set account data:', response.data[0]);
        } else {
          console.log('No accounts found for this user');
        }
      } catch (error: any) {
        console.error('Failed to fetch user account:', error);
        
        // More detailed error logging
        if (error.response) {
          console.error('API Error Response:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('API Request Error - No Response:', error.request);
        } else {
          console.error('Error Setting Up Request:', error.message);
        }
        
        toast.error('Failed to load your business account. Please try refreshing the page.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserAccount();
  }, []);

  const getTeamMemberLimit = (tier?: string) => {
    switch(tier) {
      case 'premium': return Infinity; // Unlimited
      case 'pro': return 5;
      case 'basic':
      default: return 1;
    }
  };

  // Get team member count with a minimum of 1 for Basic tier
  const getTeamMemberCount = (account: Account, tier?: string) => {
    const count = account.users?.length || 0;
    return (tier === 'basic' || !tier) ? Math.max(1, count) : count;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" tip="Loading your business account..." />
      </div>
    );
  }

  if (!account) {
    return (
      <Empty
        description={
          <span>
            You don't have a business account yet. 
            <br />
            <Link to="/my-business/edit" className="text-blue-600 hover:underline">
              Add your business details here
            </Link>
          </span>
        }
        className="my-16"
      />
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="m-0">My Business</Title>
      </div>

      <Card className="mb-6 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <Title level={3} className="mb-1">{account.account_name}</Title>
            <Text type="secondary">Industry: {
              typeof account.industry_name === 'string' 
                ? getIndustryLabel(account.industry_name) 
                : Array.isArray(account.industry_name) 
                  ? account.industry_name.map(getIndustryLabel).join(', ') 
                  : 'Not specified'
            }</Text>
          </div>
          <Link to="/my-business/edit">
            <Button type="primary" icon={<EditOutlined />}>
              Edit Business Details
            </Button>
          </Link>
        </div>
        
        <Divider />
        
        <Descriptions title="Business Information" column={{ xs: 1, sm: 2, md: 3 }} layout="vertical">
          <Descriptions.Item label="Primary Contact">{account.primary_contact}</Descriptions.Item>
          <Descriptions.Item label="Email">
            <div className="flex items-center">
              <MailOutlined className="mr-2" />
              <a href={`mailto:${account.primary_email}`}>{account.primary_email}</a>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="Phone">
            <div className="flex items-center">
              <PhoneOutlined className="mr-2" />
              {account.primary_phone}
            </div>
          </Descriptions.Item>
        </Descriptions>
        
        <Divider />
        
        <Descriptions title="Location" column={{ xs: 1, sm: 1, md: 2 }} layout="vertical">
          <Descriptions.Item label="Address">
            <div className="flex items-start">
              <HomeOutlined className="mr-2 mt-1" />
              <div>
                {account.address_line1}<br />
                {account.address_line2 && <>{account.address_line2}<br /></>}
                {account.city}, {account.state} {account.zip_code}
              </div>
            </div>
          </Descriptions.Item>
          
          {account.websites && account.websites.length > 0 && (
            <Descriptions.Item label="Website">
              <div className="flex items-center">
                <GlobalOutlined className="mr-2" />
                <a 
                  href={`https://${account.websites[0].domain}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  {account.websites[0].domain}
                </a>
              </div>
            </Descriptions.Item>
          )}
        </Descriptions>
        
        <Divider />
        
        <Descriptions title="Account Status" column={{ xs: 1, sm: 2 }}>
          <Descriptions.Item label="Status">
            <div className="flex items-center">
              <span className={`px-2 py-1 rounded ${
                account.status === 'active' ? 'bg-green-100 text-green-800' : 
                account.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                'bg-red-100 text-red-800'
              }`}>
                {account.status.charAt(0).toUpperCase() + account.status.slice(1)}
              </span>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="Subscription Tier">
            <div className="flex items-center">
              <span className={`px-2 py-1 rounded ${
                account.subscription_tier === 'premium' ? 'bg-purple-100 text-purple-800' : 
                account.subscription_tier === 'pro' ? 'bg-blue-100 text-blue-800' : 
                'bg-gray-100 text-gray-800'
              }`}>
                {account.subscription_tier 
                  ? account.subscription_tier.charAt(0).toUpperCase() + account.subscription_tier.slice(1)
                  : 'Basic'}
              </span>
              <Link to="/subscription" className="ml-2 text-sm text-blue-600 hover:underline">
                View Plans
              </Link>
            </div>
          </Descriptions.Item>
        </Descriptions>
        
        <Divider />
        
        <Descriptions title="Team Information" column={{ xs: 1, sm: 1 }}>
          <Descriptions.Item label="Team Members">
            <div className="space-y-2">
              <div className="flex items-center">
                <TeamOutlined className="mr-2" />
                <span className="flex items-center">
                  {getTeamMemberCount(account, account.subscription_tier)} of {getTeamMemberLimit(account.subscription_tier) === Infinity 
                    ? "Unlimited" 
                    : getTeamMemberLimit(account.subscription_tier)}
                </span>
              </div>
              
              {account.subscription_tier !== 'premium' && (
                <div className="w-full max-w-xs">
                  <Progress 
                    percent={Math.min(100, (getTeamMemberCount(account, account.subscription_tier) / getTeamMemberLimit(account.subscription_tier)) * 100)} 
                    status={
                      // For Basic tier with 1 of 1, show success instead of exception
                      account.subscription_tier === 'basic' && getTeamMemberCount(account, account.subscription_tier) === 1 && getTeamMemberLimit(account.subscription_tier) === 1
                        ? "success"
                        : getTeamMemberCount(account, account.subscription_tier) >= getTeamMemberLimit(account.subscription_tier) 
                          ? "exception" 
                          : "normal"
                    }
                    showInfo={false}
                    className={
                      account.subscription_tier === 'basic' && getTeamMemberCount(account, account.subscription_tier) === 1
                        ? "ant-progress-status-success" // Additional class for styling
                        : ""
                    }
                  />
                  {getTeamMemberCount(account, account.subscription_tier) >= getTeamMemberLimit(account.subscription_tier) && (
                    <div className="text-xs mt-1 flex items-center">
                      {account.subscription_tier === 'basic' ? (
                        <span className="text-green-600">
                          Basic plan includes 1 team member
                        </span>
                      ) : (
                        <span className="text-red-600">
                          Team member limit reached. 
                          <Link to="/subscription" className="ml-1 text-blue-600 hover:underline">
                            Upgrade your plan
                          </Link>
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default MyAccountPage; 