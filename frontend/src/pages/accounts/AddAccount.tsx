import React, { useState } from 'react';
import { Card, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import AccountForm from '../../components/accounts/AccountForm';
import api from '../../utils/api';

const AddAccount: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      await api.post('/api/accounts/', values);
      message.success('Account created successfully');
      navigate('/accounts');
    } catch (error: any) {
      message.error('Failed to create account');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    navigate('/accounts');
  }

  return (
    <Card title="Add New Account">
      <AccountForm
        onSubmit={handleSubmit}
        onCancel={handleCancel} 
        loading={loading}
      />
    </Card>
  );
};

export default AddAccount; 