import React, { useState, useEffect } from 'react';
import { Card, message, Spin } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import AccountForm from '../../components/accounts/AccountForm';
import api from '../../utils/api';
import { Account } from '../../types/account';

const EditAccount: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [account, setAccount] = useState<Account | null>(null);
  const [fetchLoading, setFetchLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  useEffect(() => {
    const fetchAccount = async () => {
      try {
        const response = await api.get(`/api/accounts/${id}/`);
        setAccount(response.data);
      } catch (error) {
        message.error('Failed to fetch account');
        navigate('/accounts');
      } finally {
        setFetchLoading(false);
      }
    };
    fetchAccount();
  }, [id, navigate]);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      await api.put(`/api/accounts/${id}/`, values);
      message.success('Account updated successfully');
      navigate('/accounts');
    } catch (error: any) {
      message.error('Failed to update account');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    navigate('/accounts');
  }

  if (fetchLoading) {
    return <Spin size="large" />;
  }

  if (!account) {
    return null;
  }

  return (
    <Card title="Edit Account">
      <AccountForm 
        initialValues={account} 
        onSubmit={handleSubmit} 
        onCancel={handleCancel} 
        loading={loading} 
      />
    </Card>
  );
};

export default EditAccount; 