import React, { useState, useEffect } from 'react';
import { Spin } from 'antd';
import AccountList from '../../components/accounts/AccountList';
import api from '../../utils/api';
import { Account } from '../../types/account';

const AccountsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [accounts, setAccounts] = useState<Account[]>([]);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        console.log('Fetching accounts...');
        const response = await api.get('/api/accounts/');
        console.log('Accounts response:', response.data);
        setAccounts(response.data);
      } catch (error) {
        console.error('Failed to fetch accounts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAccounts();
  }, []);

  return (
    <AccountList 
      accounts={accounts} 
      loading={loading}
      onDelete={(id) => {
        setAccounts(accounts.filter(a => a.id !== id));
      }}
    />
  );
};

export default AccountsPage; 