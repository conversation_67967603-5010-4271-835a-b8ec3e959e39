import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import api from "@/utils/api";
import { config } from '../../config';
import styles from './DemoPage.module.css';

interface Account {
  id: number;
  uuid: string;
  account_name: string;
  industry_name: string;
  status: string;
  chat_icon?: string;
}

export default function DemoPage() {
  const [searchParams] = useSearchParams();
  const [account, setAccount] = useState<Account | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const accountUuid = searchParams.get('account');

  useEffect(() => {
    const loadAccount = async () => {
      if (!accountUuid) {
        setError('No account specified');
        setLoading(false);
        return;
      }

      try {
        const response = await api.get(`/api/accounts/by-uuid/${accountUuid}/`);
        setAccount(response.data);
      } catch (err) {
        setError('Failed to load account information');
        console.error('Error loading account:', err);
      } finally {
        setLoading(false);
      }
    };

    loadAccount();
  }, [accountUuid]);

  // Load AIDA widget scripts and styles
  useEffect(() => {
    if (account?.uuid) {
      // Add CSS
      const link = document.createElement('link');
      link.href = `/widget/${account.uuid}/assets/index.css`;
      link.rel = 'stylesheet';
      link.type = 'text/css';
      document.head.appendChild(link);

      // Add JavaScript
      const script = document.createElement('script');
      script.src = `/widget/${account.uuid}/assets/main.js`;
      script.async = true;
      script.setAttribute('data-aida-mode', 'demo');
      script.setAttribute('data-aida-account', account.uuid);
      script.setAttribute('data-aida-baseUrl', config.API_BASE_URL);
      document.body.appendChild(script);

      // Add root div for widget
      const widgetRoot = document.createElement('div');
      widgetRoot.id = 'aida-root';
      document.body.appendChild(widgetRoot);

      // Cleanup function
      return () => {
        document.head.removeChild(link);
        document.body.removeChild(script);
        if (widgetRoot.parentNode) {
          document.body.removeChild(widgetRoot);
        }
      };
    }
  }, [account?.uuid]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-[250px]" />
            <Skeleton className="h-4 w-[350px] mt-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !account) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>{error || 'Account not found'}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className={styles.demoContent}>
        <h1>Welcome to the AIDA Demo</h1>
        <p>
          This is a demonstration of how AIDA will appear on your website. 
          The chat widget should appear in the bottom-right corner of this page.
        </p>
        <p>
          Try asking questions about your organization to see how AIDA responds!
        </p>
        <h2>Features:</h2>
        <ul>
          <li>Natural language understanding</li>
          <li>Context-aware responses</li>
          <li>Customizable appearance</li>
          <li>Easy integration</li>
        </ul>
      </div>
    </div>
  );
} 