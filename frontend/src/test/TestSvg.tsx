import React from 'react';

// Method 1: Direct import as URL
// @ts-ignore
import aida1Url from '../../images/aida1.svg?url';

// Method 2: Direct SVG component import using SVGR (if available)
// @ts-ignore
import Aida1Component from '../../images/aida1.svg?react';

// Method 3: Dynamic import with ?url suffix 
const aida3ImgUrl = new URL('../../images/aida3.svg', import.meta.url).href;

const TestSvg: React.FC = () => {
  return (
    <div className="p-10 space-y-10">
      <h1 className="text-2xl font-bold">SVG Test Methods</h1>
      
      <div className="space-y-4">
        <h2 className="text-xl">Current Method (img with path)</h2>
        <div className="flex space-x-4">
          <div>
            <p>Path: /images/aida1.svg</p>
            <img src="/images/aida1.svg" alt="AIDA Logo 1" className="h-16 w-auto" />
          </div>
          <div>
            <p>Path: ./images/aida1.svg</p>
            <img src="./images/aida1.svg" alt="AIDA Logo 1" className="h-16 w-auto" />
          </div>
          <div>
            <p>Path: ../images/aida1.svg</p>
            <img src="../images/aida1.svg" alt="AIDA Logo 1" className="h-16 w-auto" />
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h2 className="text-xl">Method 1: Import as URL</h2>
        <div>
          <p>Path: {aida1Url || 'URL import not supported'}</p>
          {aida1Url && <img src={aida1Url} alt="AIDA Logo (URL import)" className="h-16 w-auto" />}
        </div>
      </div>
      
      <div className="space-y-4">
        <h2 className="text-xl">Method 2: Import as React Component</h2>
        <div>
          <p>Component: {Aida1Component ? 'Available' : 'Not supported'}</p>
          {Aida1Component && <Aida1Component className="h-16 w-auto" />}
        </div>
      </div>
      
      <div className="space-y-4">
        <h2 className="text-xl">Method 3: Dynamic Import</h2>
        <div>
          <p>Path: {aida3ImgUrl}</p>
          <img src={aida3ImgUrl} alt="AIDA Logo (dynamic import)" className="h-16 w-auto" />
        </div>
      </div>
      
      <div className="space-y-4">
        <h2 className="text-xl">Method 4: Public URL (in index.html)</h2>
        <div>
          <p>Base URL: {window.location.origin}</p>
          <img 
            src={`${window.location.origin}/images/aida3.svg`} 
            alt="AIDA Logo (public URL)" 
            className="h-16 w-auto" 
          />
        </div>
      </div>
      
      <div className="space-y-4">
        <h2 className="text-xl">Debug Info</h2>
        <div className="bg-gray-100 p-4 rounded overflow-auto">
          <p>Import Meta URL: {import.meta.url}</p>
          <p>Window Location: {window.location.href}</p>
          <p>Document Base URI: {document.baseURI}</p>
        </div>
      </div>
    </div>
  );
};

export default TestSvg;
