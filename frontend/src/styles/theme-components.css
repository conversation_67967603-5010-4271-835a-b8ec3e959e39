/* Theme Component Overrides */

/* ShadCn Toggle Switch Styling for all themes */
[role="switch"][data-state="checked"],
button[role="switch"][data-state="checked"],
.peer[data-state="checked"],
.data-\[state\=checked\]\:bg-primary[data-state="checked"],
.peer.inline-flex[data-state="checked"],
.peer.inline-flex.data-\[state\=checked\]\:bg-primary[data-state="checked"],
.peer.inline-flex.items-center.rounded-full.border-2.data-\[state\=checked\]\:bg-primary[data-state="checked"],
button.peer.inline-flex.h-6.w-11.shrink-0.cursor-pointer.items-center.rounded-full.border-2.border-transparent.transition-colors.focus-visible\:outline-none.focus-visible\:ring-2.focus-visible\:ring-ring.focus-visible\:ring-offset-2.focus-visible\:ring-offset-background.disabled\:cursor-not-allowed.disabled\:opacity-50.data-\[state\=checked\]\:bg-primary.data-\[state\=unchecked\]\:bg-input {
  background-color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
}

/* Direct class match for the toggle */
button[type="button"][role="switch"].peer.inline-flex.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
}

/* Fix for explicit shadcn toggle syntax */
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary)) !important;
}

/* Focus styles for toggles */
button[role="switch"]:focus-visible {
  outline-color: hsl(var(--primary)) !important;
  ring-color: hsl(var(--primary)) !important;
}

/* Theme-specific toggle styles */
.theme-modern button[role="switch"][data-state="checked"],
.theme-modern .peer[data-state="checked"],
.theme-modern .data-\[state\=checked\]\:bg-primary[data-state="checked"],
.theme-modern button[type="button"][role="switch"].peer.inline-flex[data-state="checked"] {
  background-color: #171717 !important; /* Changed to black */
  border-color: #171717 !important;
}

.theme-ocean button[role="switch"][data-state="checked"],
.theme-ocean .peer[data-state="checked"],
.theme-ocean .data-\[state\=checked\]\:bg-primary[data-state="checked"],
.theme-ocean button[type="button"][role="switch"].peer.inline-flex[data-state="checked"] {
  background-color: #0891b2 !important;
  border-color: #0891b2 !important;
}

/* Ant Design Button Overrides - with more specific selectors */
/* Modern theme */
.theme-modern .ant-btn-primary,
.theme-modern .ant-btn-primary.ant-btn-color-primary,
.theme-modern .ant-btn-primary.ant-btn-variant-solid,
.theme-modern [class*="css-dev-only-do-not-override"].ant-btn-primary,
.theme-modern .ant-btn-primary[class*="css-dev-only-do-not-override"],
.theme-modern .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid,
.theme-modern .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary,
.theme-modern .ant-btn.css-dev-only-do-not-override-8db58i.ant-btn-primary {
  background-color: #171717 !important; /* Changed to black */
  border-color: #171717 !important;
}

.theme-modern .ant-btn-primary:hover,
.theme-modern .ant-btn-primary.ant-btn-color-primary:hover,
.theme-modern .ant-btn-primary.ant-btn-variant-solid:hover,
.theme-modern [class*="css-dev-only-do-not-override"].ant-btn-primary:hover,
.theme-modern .ant-btn-primary[class*="css-dev-only-do-not-override"]:hover,
.theme-modern .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary:hover,
.theme-modern .ant-btn.css-dev-only-do-not-override-8db58i.ant-btn-primary:hover {
  background-color: #2e2e2e !important; /* Slightly lighter black for hover */
  border-color: #2e2e2e !important;
}

/* Ocean theme */
.theme-ocean .ant-btn-primary,
.theme-ocean .ant-btn-primary.ant-btn-color-primary,
.theme-ocean .ant-btn-primary.ant-btn-variant-solid,
.theme-ocean [class*="css-dev-only-do-not-override"].ant-btn-primary,
.theme-ocean .ant-btn-primary[class*="css-dev-only-do-not-override"],
.theme-ocean .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid,
.theme-ocean .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary,
.theme-ocean .ant-btn.css-dev-only-do-not-override-8db58i.ant-btn-primary {
  background-color: #0891b2 !important;
  border-color: #0891b2 !important;
}

.theme-ocean .ant-btn-primary:hover,
.theme-ocean .ant-btn-primary.ant-btn-color-primary:hover,
.theme-ocean .ant-btn-primary.ant-btn-variant-solid:hover,
.theme-ocean [class*="css-dev-only-do-not-override"].ant-btn-primary:hover,
.theme-ocean .ant-btn-primary[class*="css-dev-only-do-not-override"]:hover,
.theme-ocean .ant-btn.css-dev-only-do-not-override-7njbxw.ant-btn-primary:hover,
.theme-ocean .ant-btn.css-dev-only-do-not-override-8db58i.ant-btn-primary:hover {
  background-color: #06748e !important; /* Slightly darker cyan for hover */
  border-color: #06748e !important;
} 