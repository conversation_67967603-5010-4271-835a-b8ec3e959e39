/* Theme preview styles */
.theme-preview {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.light {
  background-color: #ffffff;
  color: #333333;
}

.dark {
  background-color: #222222;
  color: #f5f5f5;
}

.preview-header {
  padding: 12px;
  font-weight: bold;
  background-color: #4a90e2;
  color: white;
}

.dark .preview-header {
  background-color: #5f9ee9;
}

.preview-messages {
  padding: 12px;
  height: calc(100% - 44px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.preview-message {
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  max-width: 80%;
  font-size: 14px;
}

.preview-message.assistant {
  background-color: #e3f2fd;
  align-self: flex-start;
}

.preview-message.user {
  background-color: #f0f0f0;
  align-self: flex-end;
}

.dark .preview-message.assistant {
  background-color: #2e4a6a;
}

.dark .preview-message.user {
  background-color: #444444;
}

/* Preview container for the actual chat widget */
.preview-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.preview-container.dark-theme {
  background-color: #222222;
  color: #f5f5f5;
  border-color: #444444;
} 