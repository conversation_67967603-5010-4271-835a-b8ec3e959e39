@import './theme-components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default status colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* Default radius */
  --radius: 0.5rem;

  /* CSS HSL color conversion helper */
  --hsl-to-rgb: 1, 1, 1;
}

/* Utility to convert HSL to RGB for tailwind */
@layer utilities {
  /* Generate the RGB values from HSL for use with tailwind's RGB syntax */
  :root {
    --primary-rgb: calc(var(--primary) / 100) * 255, 
                   calc(var(--primary) / 100) * 255, 
                   calc(var(--primary) / 100) * 255;
  }
}

/* Light themes: base variables */
.light {
  /* Base colors */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  
  /* Card colors */
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  
  /* Popover colors */
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  
  /* UI element colors */
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  
  /* Utility colors */
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 10% 3.9%;
  
  /* Destructive colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  
  /* Shadow */
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  /* Status colors */
  --success-light: 150 89% 95%;
  --warning-light: 40 89% 95%;
  --error-light: 0 89% 95%;
  --info-light: 220 89% 95%;
  
  /* Grays for charts, tables, etc */
  --gray-50: 210 20% 98%;
  --gray-100: 220 14% 96%;
  --gray-200: 220 13% 91%;
  --gray-300: 216 12% 84%;
  --gray-400: 218 11% 65%;
  --gray-500: 220 9% 46%;
  --gray-600: 215 14% 34%;
  --gray-700: 217 19% 27%;
  --gray-800: 215 28% 17%;
  --gray-900: 221 39% 11%;
}

/* MODERN Light theme - The new default */
.theme-modern {
  --primary: 0 0% 9%; /* #171717 - Black */
  --primary-foreground: 0 0% 98%;
  
  /* Layout with neutral colors for better contrast */
  --header-bg: 210 20% 98%;
  --header-fg: 215 14% 34%;
  --nav-bg: 0 0% 100%;
  --nav-fg: 215 14% 34%;
  --footer-bg: 220 13% 91%;
  --footer-fg: 215 28% 17%;
}

/* Modern theme button styling override */
.theme-modern button.bg-primary,
.theme-modern .bg-primary.text-primary-foreground,
.theme-modern .bg-primary {
  background-color: #171717 !important; /* Direct hex color */
  color: white !important;
  border-color: #171717 !important;
}

.theme-modern button.hover\:bg-primary\/90:hover,
.theme-modern .hover\:bg-primary\/90:hover {
  background-color: #2e2e2e !important; /* Slightly lighter for hover */
}

/* OCEAN Light theme */
.theme-ocean {
  --primary: 190 91.3% 36.4%; /* #0891b2 - Cyan-600 */
  --primary-foreground: 0 0% 98%;
  
  /* Set more prominent accent color */
  --accent: var(--accent-color, 190 93.5% 50%); /* Brighter cyan */
  --accent-foreground: 0 0% 98%;
  
  /* Customize other UI elements with cyan shades */
  --ring: 190 91.3% 36.4%;
  
  /* Layout with neutral colors for better contrast */
  --header-bg: 210 20% 98%;
  --header-fg: 215 14% 34%;
  --nav-bg: 0 0% 100%;
  --nav-fg: 215 14% 34%;
  --footer-bg: 220 13% 91%;
  --footer-fg: 215 28% 17%;

  /* Font pairing for Ocean theme */
  --font-body: "Nunito Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-heading: "Space Grotesk", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.theme-ocean h1,
.theme-ocean h2,
.theme-ocean h3,
.theme-ocean h4,
.theme-ocean h5,
.theme-ocean h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.theme-ocean p,
.theme-ocean span,
.theme-ocean div,
.theme-ocean button,
.theme-ocean input,
.theme-ocean select,
.theme-ocean textarea {
  font-family: var(--font-body);
  letter-spacing: 0.01em;
  line-height: 1.5;
}

/* Adjust font sizes for better hierarchy */
.theme-ocean h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.theme-ocean h2 {
  font-size: 2rem;
  margin-bottom: 1.25rem;
}

.theme-ocean h3 {
  font-size: 1.75rem;
  margin-bottom: 1rem;
}

.theme-ocean h4 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.theme-ocean h5 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.theme-ocean h6 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

/* Body text adjustments */
.theme-ocean p {
  font-size: 1rem;
  margin-bottom: 1rem;
}

/* Small text for UI elements */
.theme-ocean .text-sm,
.theme-ocean .text-xs {
  font-size: 0.875rem;
  line-height: 1.25;
}

/* Button text */
.theme-ocean button {
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Input text */
.theme-ocean input,
.theme-ocean select,
.theme-ocean textarea {
  font-size: 0.875rem;
}

/* Navigation text */
.theme-ocean nav a,
.theme-ocean nav button {
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Card titles */
.theme-ocean .ant-card-head-title {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: -0.02em;
}

/* Table headers */
.theme-ocean .ant-table-thead th {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Table content */
.theme-ocean .ant-table-tbody td {
  font-family: var(--font-body);
  font-size: 0.875rem;
}

/* Form labels */
.theme-ocean label {
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Badge and tag text */
.theme-ocean .ant-tag,
.theme-ocean .ant-badge {
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Specific Ocean theme button styling */
.theme-ocean button.bg-primary,
.theme-ocean .bg-primary.text-primary-foreground,
.theme-ocean .bg-primary {
  background-color: #0891b2 !important; /* Direct hex color */
  color: white !important;
  border-color: #0891b2 !important;
}

.theme-ocean button.hover\:bg-primary\/90:hover,
.theme-ocean .hover\:bg-primary\/90:hover {
  background-color: #06748e !important; /* Slightly darker for hover */
}

/* Font variations based on selection */
[data-font="inter"] {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

[data-font="poppins"] {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

[data-font="space-grotesk"] {
  font-family: "Space Grotesk", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

[data-font="nunito"] {
  /* Nunito Sans for body text - clean and readable */
  font-family: "Nunito Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Logo variations based on selection */
[data-logo="logo-png"] .logo-png {
  display: block;
}
[data-logo="logo-png"] .logo-svg,
[data-logo="logo-png"] [class^="logo-aida"] {
  display: none;
}

[data-logo="logo"] .logo-svg {
  display: block;
}
[data-logo="logo"] .logo-png,
[data-logo="logo"] [class^="logo-aida"] {
  display: none;
}

[data-logo="aida1"] .logo-aida1 {
  display: block;
}
[data-logo="aida1"] .logo-png,
[data-logo="aida1"] .logo-svg,
[data-logo="aida1"] [class^="logo-aida"]:not(.logo-aida1) {
  display: none;
}

[data-logo="aida1-inverse"] .logo-aida1-inverse {
  display: block;
}
[data-logo="aida1-inverse"] .logo-png,
[data-logo="aida1-inverse"] .logo-svg,
[data-logo="aida1-inverse"] [class^="logo-aida"]:not(.logo-aida1-inverse) {
  display: none;
}

[data-logo="aida3"] .logo-aida3 {
  display: block;
}
[data-logo="aida3"] .logo-png,
[data-logo="aida3"] .logo-svg,
[data-logo="aida3"] [class^="logo-aida"]:not(.logo-aida3) {
  display: none;
}

[data-logo="aida4"] .logo-aida4 {
  display: block;
}
[data-logo="aida4"] .logo-png,
[data-logo="aida4"] .logo-svg,
[data-logo="aida4"] [class^="logo-aida"]:not(.logo-aida4) {
  display: none;
}

[data-logo="aida4c"] .logo-aida4c {
  display: block;
}
[data-logo="aida4c"] .logo-png,
[data-logo="aida4c"] .logo-svg,
[data-logo="aida4c"] [class^="logo-aida"]:not(.logo-aida4c) {
  display: none;
}

/* Set font on the document */
body {
  font-family: var(--font-sans);
}

/* Logo container styles based on provided HTML structure */
.logo-container {
  position: relative;
  height: 2.5rem; /* h-10 */
  width: 10rem; /* w-40 */
}

/* Logo styles based on provided HTML structure */
[class^="logo-"], 
.logo-png, 
.logo-svg {
  position: absolute;
  inset: 0;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  display: flex;
  align-items: center;
  justify-content: center;
}

[data-logo="logo-png"] .logo-png,
[data-logo="logo"] .logo-svg,
[data-logo="aida1"] .logo-aida1,
[data-logo="aida1-inverse"] .logo-aida1-inverse,
[data-logo="aida3"] .logo-aida3,
[data-logo="aida4"] .logo-aida4,
[data-logo="aida4c"] .logo-aida4c {
  opacity: 1;
  z-index: 10;
}

[class^="logo-"] img, 
.logo-png img, 
.logo-svg img,
[class^="logo-"] svg, 
.logo-png svg, 
.logo-svg svg {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

/** 
 * COMPONENT OVERRIDES
 * This section contains all necessary overrides to make things look right
 * when the CSS variables in themes are changed.
 */

/* ShadCn Base Overrides */
@layer base {
  :root {
    /* These values match our theme structure */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    /* Base primary color that will be overridden by themes */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

/* Layout Elements with Neutral Colors and Better Contrast */
header {
  background-color: white !important;
  color: #333 !important;
}

header .border-b {
  border-color: #e5e7eb !important;
}

header button.text-muted-foreground,
header a.text-muted-foreground {
  color: #6b7280 !important;
}

nav.border-t {
  background-color: white !important;
  color: #333 !important;
  border-color: #e5e7eb !important;
}

nav button, 
nav a {
  color: #6b7280 !important;
}

nav button:hover, 
nav a:hover {
  color: hsl(var(--primary)) !important;
}

footer.border-t {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
  border-color: #e5e7eb !important;
}

footer a {
  color: #4b5563 !important;
}

footer a:hover {
  color: hsl(var(--primary)) !important;
}

/* Button Overrides for ShadCn Integration */
.bg-primary {
  background-color: hsl(var(--primary)) !important;
}

.text-primary {
  color: hsl(var(--primary)) !important;
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground)) !important;
}

.border-primary {
  border-color: hsl(var(--primary)) !important;
}

/* ShadCn Button Component Override - This targets the exact button class structure */
button.bg-primary,
button[class*="bg-primary"],
.bg-primary.text-primary-foreground,
.bg-primary {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border-color: hsl(var(--primary)) !important;
}

/* Handle hover states */
button.hover\:bg-primary:hover,
.hover\:bg-primary:hover {
  background-color: hsl(var(--primary)) !important;
}

button.hover\:bg-primary\/90:hover,
.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9) !important;
}

/* Handle focus states */
button.focus\:bg-primary:focus,
.focus\:bg-primary:focus {
  background-color: hsl(var(--primary)) !important;
}

.ring-primary,
.focus\:ring-primary:focus,
.focus-visible\:ring-primary:focus-visible {
  --tw-ring-color: hsl(var(--primary)) !important;
}

/* Tailwind Hardcoded Blue Color Overrides */
.bg-blue-500,
.bg-blue-600,
.bg-blue-700 {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.hover\:bg-blue-600:hover,
.hover\:bg-blue-700:hover,
.hover\:bg-blue-800:hover {
  background-color: hsl(var(--primary) / 0.9) !important;
}

.text-blue-500,
.text-blue-600,
.text-blue-700 {
  color: hsl(var(--primary)) !important;
}

.hover\:text-blue-600:hover,
.hover\:text-blue-700:hover,
.hover\:text-blue-800:hover {
  color: hsl(var(--primary) / 0.9) !important;
}

.border-blue-500,
.border-blue-600,
.border-blue-700 {
  border-color: hsl(var(--primary)) !important;
}

.hover\:border-blue-600:hover,
.hover\:border-blue-700:hover,
.hover\:border-blue-800:hover {
  border-color: hsl(var(--primary) / 0.9) !important;
}

/* Ant Design Component Overrides */
.ant-btn-primary {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border-color: hsl(var(--primary)) !important;
}

.ant-btn-primary:hover {
  background-color: hsl(var(--primary) / 0.9) !important;
  border-color: hsl(var(--primary) / 0.9) !important;
}

.ant-btn.ant-btn-default:not(.ant-btn-dangerous) {
  border-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary)) !important;
}

.ant-btn.ant-btn-default:not(.ant-btn-dangerous):hover {
  border-color: hsl(var(--primary) / 0.8) !important;
  color: hsl(var(--primary) / 0.8) !important;
}

.ant-pagination-item:hover {
  border-color: hsl(var(--primary)) !important;
}

.ant-pagination-item-active {
  border-color: hsl(var(--primary)) !important;
}

.ant-pagination-item-active a {
  color: hsl(var(--primary)) !important;
}

/* Table styling without transitions */
.ant-table-wrapper .ant-table {
  background-color: white !important;
  color: #374151 !important;
  border-radius: var(--radius);
  overflow: hidden;
}

.ant-table-thead .ant-table-cell {
  background-color: #f9fafb !important;
  color: #6b7280 !important;
}

.ant-table-tbody .ant-table-row:nth-child(even) > td {
  background-color: #f9fafb !important;
}

/* Card styling without transitions */
.ant-card {
  background-color: white !important;
  border-color: #e5e7eb !important;
  border-radius: var(--radius);
}

.ant-card-head {
  border-color: #e5e7eb !important;
}

.ant-tag-blue {
  color: hsl(var(--primary)) !important;
  background: hsl(var(--primary) / 0.15) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
}

/* ShadCn component direct override */
.inline-flex.items-center.justify-center.whitespace-nowrap.rounded-md.text-sm.font-medium:not(.bg-destructive):not(.bg-secondary):not(.bg-accent):not(.bg-muted):not([class*="ghost"]):not([class*="outline"]) {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* Color showcase elements */
.color-showcase-primary {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  margin: 0.25rem;
  display: inline-block;
}

.color-showcase-accent {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  margin: 0.25rem;
  display: inline-block;
} 