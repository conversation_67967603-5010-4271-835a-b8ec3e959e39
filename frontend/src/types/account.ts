import { UserType } from './user';

export interface AccountWebsite {
  id?: number;
  domain: string;
  url_patterns?: string;
}

export interface Account {
  id: number;
  uuid?: string;
  account_name: string;
  industry_name: string | string[];
  primary_contact: string;
  primary_phone: string;
  primary_email: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  zip_code: string;
  status: string;
  created_at?: string;
  updated_at?: string;
  websites?: AccountWebsite[];
  users?: any[]; // Array of AccountUser objects
  // AnythingLLM integration
  llm_id?: string;
  llm_slug?: string;
  llm_folder?: string;
  config?: AccountConfig;
  // Subscription information
  subscription_tier?: 'basic' | 'pro' | 'premium';
  subscription_start_date?: string;
  subscription_renewal_date?: string;
  subscription_status?: 'active' | 'trial' | 'expired' | 'canceled';
}

export interface AccountConfig {
  id?: number;
  account: number;
  chat_icon?: string | null;
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    text_color?: string;
    background_color?: string;
  };
  aida_enabled?: boolean;
  order_taking_enabled?: boolean;
  order_taking_email?: string;
  created_at?: string;
  updated_at?: string;
}
