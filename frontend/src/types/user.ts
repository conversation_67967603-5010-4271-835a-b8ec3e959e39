export enum UserType {
  AIDA_ADMIN = "AIDA Admin",
  BUSINESS_ADMIN = "Business Admin",
  BUSINESS_USER = "Business User"
}

export interface BaseUser {
  id: number | string;
  firstName: string;
  lastName: string;
  email: string;
  userType: UserType;
  groups?: string[];
  is_staff?: boolean;
  is_superuser?: boolean;
}

export interface AidaAdmin extends BaseUser {
  userType: UserType.AIDA_ADMIN;
}

export interface BusinessAdmin extends BaseUser {
  userType: UserType.BUSINESS_ADMIN;
  businessId: number | string;
  businessName: string;
}

export interface BusinessUser extends BaseUser {
  userType: UserType.BUSINESS_USER;
  businessId: number | string;
  businessName: string;
}

// Type guard functions
export const isAidaAdmin = (user: BaseUser): user is AidaAdmin => {
  return user.userType === UserType.AIDA_ADMIN;
};

export const isBusinessAdmin = (user: BaseUser): user is BusinessAdmin => {
  return user.userType === UserType.BUSINESS_ADMIN;
};

export const isBusinessUser = (user: BaseUser): user is BusinessUser => {
  return user.userType === UserType.BUSINESS_USER;
}; 