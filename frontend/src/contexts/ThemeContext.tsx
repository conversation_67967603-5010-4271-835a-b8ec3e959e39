import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { ConfigProvider, theme as antTheme } from 'antd';

// Theme options
export type ThemeMode = 'light' | 'dark' | 'system';
export type PresetTheme = 'modern' | 'ocean';

// Preset theme configuration
export const PRESET_THEMES = [
  // Light themes
  { 
    id: 'modern', 
    name: 'Modern', 
    logo: 'aida4', 
    font: 'inter',
    primaryColor: '#171717', // Black
    mode: 'light',
    logoIsPng: false
  },
  { 
    id: 'ocean', 
    name: 'Ocean', 
    logo: 'aida4', 
    font: 'nunito',
    primaryColor: '#0891b2', // Cyan-600
    accentColor: '#06b6d4', // Cyan-500
    mode: 'light',
    logoIsPng: false
  }
];

// ThemeContext interface
interface ThemeContextType {
  themeMode: ThemeMode;
  presetTheme: PresetTheme;
  setThemeMode: (mode: ThemeMode) => void;
  setPresetTheme: (theme: PresetTheme) => void;
  refreshTheme: () => void;
}

// Create ThemeContext with default values
export const ThemeContext = createContext<ThemeContextType>({
  themeMode: 'system',
  presetTheme: 'modern',
  setThemeMode: () => {},
  setPresetTheme: () => {},
  refreshTheme: () => {},
});

// ThemeProvider props
interface ThemeProviderProps {
  children: ReactNode;
}

// Convert hex color to RGB array
const hexToRgb = (hex: string): [number, number, number] => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return [r, g, b];
};

// Apply theme settings directly to the DOM
const applyThemeToDOM = (mode: ThemeMode, preset: PresetTheme) => {
  const root = document.documentElement;
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const selectedTheme = PRESET_THEMES.find(t => t.id === preset);
  
  if (!selectedTheme) return;
  
  // 1. Apply theme class (light/dark)
  // Always use the theme's own mode (no longer using system preference here)
  root.classList.remove('light', 'dark');
  root.classList.add(selectedTheme.mode);
  
  // 2. Apply preset theme class
  PRESET_THEMES.forEach(theme => {
    root.classList.remove(`theme-${theme.id}`);
  });
  root.classList.add(`theme-${preset}`);
  
  // 3. Set logo attribute
  const logoValue = selectedTheme.logoIsPng ? 'logo-png' : selectedTheme.logo;
  
  // Only change logo if it's different to prevent flickering
  if (root.getAttribute('data-logo') !== logoValue) {
    root.setAttribute('data-logo', logoValue);
  }
  
  // 4. Set font attribute
  root.setAttribute('data-font', selectedTheme.font);

  // 5. Set accent color if available
  if (selectedTheme.accentColor) {
    root.style.setProperty('--accent-color', selectedTheme.accentColor);
  } else {
    root.style.removeProperty('--accent-color');
  }
  
  console.log('Applied theme to DOM:', {
    mode: selectedTheme.mode,
    preset,
    logo: logoValue,
    font: selectedTheme.font,
    accent: selectedTheme.accentColor,
    classList: Array.from(root.classList),
    dataAttributes: {
      'data-logo': root.getAttribute('data-logo'),
      'data-font': root.getAttribute('data-font')
    }
  });
};

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Initialize state from localStorage or use defaults
  const [themeMode, setThemeModeState] = useState<ThemeMode>(() => {
    const savedMode = localStorage.getItem('ui-theme-mode') as ThemeMode;
    return savedMode || 'system';
  });
  
  const [presetTheme, setPresetThemeState] = useState<PresetTheme>(() => {
    const savedTheme = localStorage.getItem('ui-preset-theme') as PresetTheme;
    return savedTheme || 'modern';
  });

  // Get selected theme object
  const selectedTheme = PRESET_THEMES.find(t => t.id === presetTheme);
  
  // Determine effective mode (for system preference)
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const effectiveMode = themeMode === 'system' 
    ? (prefersDark ? 'dark' : 'light')
    : themeMode;
  
  // Create Ant Design theme
  const antDesignTheme = {
    token: {
      colorPrimary: selectedTheme?.primaryColor || '#171717',
      borderRadius: 4,
      colorBgBase: effectiveMode === 'dark' ? '#18181b' : '#ffffff',
      colorTextBase: effectiveMode === 'dark' ? '#fafafa' : '#18181b',
      // Add accent colors for component theming
      colorInfo: selectedTheme?.accentColor || selectedTheme?.primaryColor || '#171717',
      colorSuccess: 'var(--success)',
      colorWarning: 'var(--warning)',
      colorError: 'var(--error)',
    },
    algorithm: effectiveMode === 'dark' ? antTheme.darkAlgorithm : antTheme.defaultAlgorithm,
    components: {
      Card: {
        colorBgContainer: effectiveMode === 'dark' ? '#27272a' : '#ffffff',
      },
      Table: {
        colorBgContainer: effectiveMode === 'dark' ? '#27272a' : '#ffffff',
        colorFillAlter: effectiveMode === 'dark' ? '#3f3f46' : '#f9fafb',
      },
      Button: {
        // Enhance buttons with theme colors
        colorPrimary: selectedTheme?.primaryColor || '#171717',
        colorPrimaryHover: selectedTheme?.accentColor || selectedTheme?.primaryColor || '#171717',
      },
      Tag: {
        // Enhance tags with theme colors
        colorPrimary: selectedTheme?.primaryColor || '#171717',
      }
    }
  };

  // Force refresh of theme
  const refreshTheme = () => {
    applyThemeToDOM(themeMode, presetTheme);
  };

  // Apply theme settings whenever they change
  useEffect(() => {
    // 1. Save to localStorage
    localStorage.setItem('ui-theme-mode', themeMode);
    localStorage.setItem('ui-preset-theme', presetTheme);
    
    // 2. Apply to DOM
    applyThemeToDOM(themeMode, presetTheme);
    
  }, [themeMode, presetTheme]);

  // Initial application of theme on mount
  useEffect(() => {
    refreshTheme();
    
    // Set up system theme listener if using system theme
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      // If we use the system theme, we need to check what preset theme is selected
      // and ensure it's using the correct mode (light/dark) based on system preference
      if (themeMode === 'system') {
        const currentTheme = PRESET_THEMES.find(t => t.id === presetTheme);
        if (currentTheme) {
          // If the system preference changed, we need to find an equivalent theme in the other mode
          const systemPreferredMode = e.matches ? 'dark' : 'light';
          if (currentTheme.mode !== systemPreferredMode) {
            // Find the first theme in the preferred mode
            const alternateTheme = PRESET_THEMES.find(t => t.mode === systemPreferredMode);
            if (alternateTheme) {
              setPresetThemeState(alternateTheme.id as PresetTheme);
            }
          }
        }
      }
    };
    
    const systemThemeMedia = window.matchMedia('(prefers-color-scheme: dark)');
    systemThemeMedia.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      systemThemeMedia.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  // Set theme mode with localStorage persistence
  const setThemeMode = (newMode: ThemeMode) => {
    setThemeModeState(newMode);
  };
  
  // Set preset theme with localStorage persistence
  const setPresetTheme = (newPreset: PresetTheme) => {
    setPresetThemeState(newPreset);
  };

  return (
    <ThemeContext.Provider
      value={{
        themeMode,
        presetTheme,
        setThemeMode,
        setPresetTheme,
        refreshTheme,
      }}
    >
      <ConfigProvider theme={antDesignTheme}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => useContext(ThemeContext); 