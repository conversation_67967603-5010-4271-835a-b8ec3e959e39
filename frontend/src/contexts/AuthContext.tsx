import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import * as jwtDecode from 'jwt-decode';
import api from '../utils/api';
import { UserType } from '../types/user';

interface User {
  user_id: number;
  email: string;
  name: string;
  is_staff: boolean;
  is_superuser: boolean;
  groups: string[];
  exp?: number;
}

interface AuthContextValue {
  isAuthenticated: boolean;
  user: User | null;
  userType: UserType | null;
  isInitialized: boolean;
  login: (token: string) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use refs to avoid dependency cycles with logout function
  const userRef = useRef<User | null>(null);
  const userTypeRef = useRef<UserType | null>(null);
  const isAuthenticatedRef = useRef<boolean>(false);
  const hasInitializedRef = useRef<boolean>(false);
  
  // State for React rendering
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Initialize from localStorage on first render
    const token = localStorage.getItem('token');
    if (!token) return false;
    
    try {
      const decoded = jwtDecode.jwtDecode<User>(token);
      const currentTime = Date.now() / 1000;
      return decoded.exp ? decoded.exp > currentTime : false;
    } catch (e) {
      return false;
    }
  });
  
  const [user, setUser] = useState<User | null>(null);
  const [userType, setUserType] = useState<UserType | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const navigate = useNavigate();

  const determineUserType = useCallback((user: User): UserType => {
    if (user.is_superuser) return UserType.AIDA_ADMIN;
    if (user.groups.includes('Business Admins')) return UserType.BUSINESS_ADMIN;
    if (user.groups.includes('Business Regular Users')) return UserType.BUSINESS_USER;
    return UserType.BUSINESS_USER;
  }, []);

  // Define logout before it's used in useEffect
  const logout = useCallback(() => {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
    
    // Update refs immediately
    isAuthenticatedRef.current = false;
    userRef.current = null;
    userTypeRef.current = null;
    
    // Then update state
    setIsAuthenticated(false);
    setUser(null);
    setUserType(null);
    
    // Only navigate if we're initialized (prevents redirects during initial load)
    // Use a timeout to ensure state updates have been processed
    if (isInitialized) {
      // Small timeout to ensure state updates have propagated
      setTimeout(() => {
        navigate('/login', { state: { session: 'expired' } });
      }, 10);
    }
  }, [navigate, isInitialized]);

  const login = useCallback((token: string) => {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    try {
      const decoded = jwtDecode.jwtDecode<User>(token);
      const userType = determineUserType(decoded);
      
      // Update refs immediately
      userRef.current = decoded;
      userTypeRef.current = userType;
      isAuthenticatedRef.current = true;
      
      // Then update state
      setUser(decoded);
      setUserType(userType);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Failed to decode token:', error);
      logout();
    }
  }, [determineUserType, logout]);

  // Initialize auth state - this runs once on component mount
  useEffect(() => {
    // Prevent duplicate initialization in React StrictMode or on remounts
    if (hasInitializedRef.current) {
      return;
    }
    
    hasInitializedRef.current = true;
    
    const initializeAuth = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setIsInitialized(true);
        return;
      }
      
      try {
        // First do a local validation of the token
        const decoded = jwtDecode.jwtDecode<User>(token);
        const currentTime = Date.now() / 1000;
        
        if (decoded.exp && decoded.exp <= currentTime) {
          // Don't call logout() directly as it might try to navigate before initialization
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          setIsInitialized(true);
          return;
        }
        
        // Set auth header for API calls
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        // Update refs immediately
        const userType = determineUserType(decoded);
        userRef.current = decoded;
        userTypeRef.current = userType;
        isAuthenticatedRef.current = true;
        
        // Update state for React rendering
        setUser(decoded);
        setUserType(userType);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Token validation error:', error);
        localStorage.removeItem('token');
        delete api.defaults.headers.common['Authorization'];
      } finally {
        // Always set initialized to true when we're done
        setIsInitialized(true);
      }
    };
    
    // Start the initialization process
    initializeAuth();
    
    // Cleanup function
    return () => {};
  }, []); // Empty dependency array to ensure it only runs once
  
  // Handle storage events for multi-tab support
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token') {
        if (!e.newValue) {
          logout();
        } else if (e.newValue !== localStorage.getItem('token')) {
          window.location.reload();
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [logout]);

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      user, 
      userType, 
      isInitialized,
      login, 
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
};
