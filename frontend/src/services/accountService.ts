import api from '../utils/api';

export interface Account {
  id: number;
  account_name: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  domain: string;
  industry_name: string;
  primary_phone: string;
  secondary_phone?: string;
  primary_contact: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  zip_code: string;
  url_patterns: string;
  created_at: string;
  updated_at: string;
}

export const accountService = {
  getAll: async () => {
    const response = await api.get('/api/accounts/');
    return response.data;
  },

  getById: async (id: number) => {
    const response = await api.get(`/api/accounts/${id}/`);
    return response.data;
  },

  create: async (account: Partial<Account>) => {
    const response = await api.post('/api/accounts/', account);
    return response.data;
  },

  update: async (id: number, account: Partial<Account>) => {
    const response = await api.put(`/api/accounts/${id}/`, account);
    return response.data;
  },

  delete: async (id: number) => {
    await api.delete(`/api/accounts/${id}/`);
  }
}; 