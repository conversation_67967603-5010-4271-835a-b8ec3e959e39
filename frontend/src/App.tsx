import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { Toaster } from '@/components/ui/toaster';
import MainLayout from './components/Layout/MainLayout';
import Home from './components/Home';
import About from './components/About';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import BusinessRegistration from './components/auth/BusinessRegistration';
import ForgotPassword from './components/auth/ForgotPassword';
import ResetPassword from './components/auth/ResetPassword';
import SubscriptionPage from './components/Subscription';
import AccountsPage from './pages/accounts/AccountsPage';
import AddAccount from './pages/accounts/AddAccount';
import EditAccount from './pages/accounts/EditAccount';
import MyAccountPage from './pages/accounts/MyAccountPage';
import MyBusinessEditPage from './pages/accounts/MyBusinessEditPage';
import ApiCheckPage from './pages/debug/ApiCheckPage';
import UsersPage from './pages/users/UsersPage';
import AccountSettings from './pages/users/AccountSettings';
import AidaSetup from './pages/setup/AidaSetup';
import DocumentList from './pages/documents/DocumentList';
import PrivacyPolicy from './pages/PrivacyPolicy';
import Legal from './pages/Legal';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminRoute from './components/auth/AdminRoute';
import DemoPage from "@/pages/demo/DemoPage";
import BusinessUserRoute from './components/auth/BusinessUserRoute';
import { UserType } from './types/user';
import './styles/theme.css';

// Conditional route component to direct users based on their user type
const AccountRouteHandler = () => {
  const { userType, user } = useAuth();
  
  // Regular business users should be redirected to their dedicated view
  if (userType === UserType.BUSINESS_USER && 
      user?.groups?.includes('Business Regular Users') && 
      !user?.groups?.includes('Business Admins')) {
    return <Navigate to="/my-business" replace />;
  }
  
  // Business Admins and AIDA Admins see the accounts list
  return <AccountsPage />;
};

// Main app content with auth context
const AppContent = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: 1,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <MainLayout>
          <Routes>
            {/* Auth Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/register/business" element={<BusinessRegistration />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />
            
            {/* Public Routes */}
            <Route path="/about" element={<About />} />
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/legal" element={<Legal />} />
            
            {/* Protected Routes */}
            <Route path="/" element={<ProtectedRoute><Home /></ProtectedRoute>} />
            <Route path="/subscription" element={<SubscriptionPage />} />
            
            {/* My Business Pages - For regular business users */}
            <Route 
              path="/my-business" 
              element={<ProtectedRoute requireRegularUser={true}><MyAccountPage /></ProtectedRoute>} 
            />
            <Route 
              path="/my-business/edit" 
              element={<ProtectedRoute requireRegularUser={true}><MyBusinessEditPage /></ProtectedRoute>} 
            />
            
            {/* API Check Route - For diagnosing auth issues */}
            <Route 
              path="/api-check" 
              element={<ProtectedRoute><ApiCheckPage /></ProtectedRoute>} 
            />
            
            {/* Account Routes - Smart routing based on user type */}
            <Route
              path="/accounts"
              element={<ProtectedRoute><AccountRouteHandler /></ProtectedRoute>}
            />
            
            {/* Admin-only Account Routes */}
            <Route
              path="/accounts/add"
              element={<ProtectedRoute><BusinessUserRoute fallbackPath="/"><AddAccount /></BusinessUserRoute></ProtectedRoute>}
            />
            <Route
              path="/accounts/:id/edit"
              element={<ProtectedRoute><BusinessUserRoute fallbackPath="/"><EditAccount /></BusinessUserRoute></ProtectedRoute>}
            />
            
            {/* User Security & Preferences */}
            <Route
              path="/settings"
              element={<ProtectedRoute><AccountSettings /></ProtectedRoute>}
            />
            
            {/* Aida Setup */}
            <Route
              path="/setup"
              element={<ProtectedRoute><AidaSetup /></ProtectedRoute>}
            />
            
            {/* Admin Routes */}
            <Route
              path="/users"
              element={<AdminRoute><UsersPage /></AdminRoute>}
            />
            
            {/* Document Routes */}
            <Route
              path="/documents/:accountId"
              element={<ProtectedRoute><DocumentList /></ProtectedRoute>}
            />
            <Route
              path="/accounts/:accountId/documents"
              element={<ProtectedRoute><DocumentList /></ProtectedRoute>}
            />

            <Route path="/demo" element={<DemoPage />} />
          </Routes>
        </MainLayout>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <Toaster />
    </AuthProvider>
  );
}

export default App;
