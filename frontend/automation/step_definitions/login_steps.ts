import { Before, After, Given, When, Then } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world.js';
import { expect } from 'chai';

let world: CustomWorld;

Before(async function(this: CustomWorld) {
  world = this;
  await world.init();
});

After(async function() {
  await world.close();
});

Given('I am on the login page', async function() {
  await world.page.goto('http://localhost:3000/login');
});

When('I enter {string} as username', async function(username: string) {
  await world.page.type('input[name="username"]', username);
});

When('I enter {string} as password', async function(password: string) {
  await world.page.type('input[name="password"]', password);
});

When('I click the login button', async function() {
  await Promise.all([
    world.page.waitForNavigation(),
    world.page.click('button[type="submit"]')
  ]);
});

Then('I should see the dashboard page', async function() {
  const title = await world.page.title();
  expect(title).to.equal('Dashboard');
});

Then('I should see an error message', async function() {
  const errorMessage = await world.page.$eval('.error-message', (el: Element) => el.textContent);
  expect(errorMessage).to.exist;
});
