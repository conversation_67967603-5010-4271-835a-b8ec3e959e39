const puppeteer = require('puppeteer');

(async () => {
  try {
    console.log('Launching headless browser...');
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-gpu']
    });

    const page = await browser.newPage();
    console.log('Navigating to frontend...');
    await page.goto('http://localhost:3000/');

    // Take screenshot of the page
    await page.screenshot({ path: '/tmp/frontend_screenshot.png' });
    console.log('Screenshot saved to frontend_screenshot.png');

    // Get page title
    const title = await page.title();
    console.log('Page title:', title);

    await browser.close();
    console.log('<PERSON><PERSON><PERSON> closed successfully');
  } catch (error) {
    console.error('Error during headless browser operation:', error);
    process.exit(1);
  }
})();
