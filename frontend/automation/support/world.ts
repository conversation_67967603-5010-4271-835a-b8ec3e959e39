import { setWorldConstructor, World as CucumberWorld, IWorldOptions } from '@cucumber/cucumber';
import * as puppeteer from 'puppeteer';

export interface World extends CucumberWorld {
  browser: puppeteer.Browser;
  page: puppeteer.Page;
}

export class CustomWorld extends CucumberWorld implements World {
  browser: puppeteer.Browser;
  page: puppeteer.Page;

  constructor(options: IWorldOptions) {
    super(options);
    this.browser = null as unknown as puppeteer.Browser;
    this.page = null as unknown as puppeteer.Page;
  }

  async init(): Promise<void> {
    this.browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-gpu'],
      slowMo: 100
    });
    this.page = await this.browser.newPage();
  }

  async close(): Promise<void> {
    await this.browser.close();
  }
}

setWorldConstructor(CustomWorld);
