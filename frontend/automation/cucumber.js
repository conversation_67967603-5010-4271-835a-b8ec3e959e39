import { defineConfig } from '@cucumber/cucumber';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  requireModule: ['ts-node/register'],
  require: [
    path.join(__dirname, 'step_definitions/**/*.ts'),
    path.join(__dirname, 'support/**/*.ts')
  ],
  import: [
    path.join(__dirname, 'features/**/*.feature')
  ],
  format: ['progress', 'html:reports/cucumber-report.html'],
  parallel: 1,
  retry: 0,
  retryTagFilter: '',
  tags: '',
  worldParameters: {}
});
