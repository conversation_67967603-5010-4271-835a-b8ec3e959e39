Feature: Login functionality
  As an admin user
  I want to login to the application
  So that I can access the admin dashboard

  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I enter "admin" as username
    And I enter "admin123" as password
    And I click the login button
    Then I should see the dashboard page

  Scenario: Failed login with invalid credentials
    Given I am on the login page
    When I enter "wronguser" as username
    And I enter "wrongpass" as password
    And I click the login button
    Then I should see an error message
