# AIDA Frontend

## Overview

This is the frontend application for AIDA (AI-powered Digital Assistant), a chatbot and business management platform.

## Documentation

- **[Industry Values Management](/docs/industries.md)** - Explains our centralized approach for handling industry values across the application
- More documentation will be added here as it becomes available

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm

### Installation

1. Clone the repository
2. Navigate to the frontend directory:
   ```
   cd frontend
   ```
3. Install dependencies:
   ```
   npm install
   ```

### Development

Start the development server:

```
npm run dev
```

### Building for Production

```
npm run build
```

## Recent Changes

- **Business Information Workflow Improvement**: Users who haven't added business details can now add them directly from the /my-business page by clicking "Add your business details here". This eliminates the need to navigate to settings.

## Known Issues & TODOs

### Performance Issues

- **MyBusinessEditPage Performance Issue**: When clicking "Edit Biz Info," there is a significant performance problem causing a spinning wheel and excessive console logging. The issue appears to be related to:
  - Multiple re-renders and form resets
  - Repeated account list fetching
  - Redundant computations of transformed values
  - Multiple initializations of Google Maps Autocomplete
  - Excessive form key changes forcing re-renders

  This needs optimization to reduce unnecessary rendering cycles and API calls.

## Project Structure

- `src/components` - Reusable UI components
- `src/pages` - Page components
- `src/utils` - Utility functions and helpers
- `src/contexts` - React context providers
- `src/types` - TypeScript type definitions
- `src/hooks` - Custom React hooks 