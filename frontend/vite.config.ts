import { defineConfig } from 'vite'
import path from "path"
import react from '@vitejs/plugin-react'
import { viteStaticCopy } from 'vite-plugin-static-copy'

// https://vitejs.dev/config/
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          react: ['react', 'react-dom'],
          reactRouter: ['react-router-dom'],
          ui: [
            '@radix-ui/react-accordion',
            '@radix-ui/react-dialog',
            '@radix-ui/react-tabs'
          ],
          vendor: ['axios', 'dayjs']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        {
          src: 'images/*',
          dest: 'images'
        },
        {
          src: 'public/images/*',
          dest: 'images'
        },
        {
          src: '../js_component/dist/*',
          dest: 'aida_current'
        },
        {
          src: '../js_component/releases/*',
          dest: 'aida_releases'
        }
      ]
    })
  ],
  server: {
    port: 3000,
    fs: {
      // Allow serving files from the entire project
      allow: ['..'],
    },
    proxy: {
      // Proxy API requests to backend server
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      // Proxy media file requests to backend server
      '/media': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    }
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
    alias: {
      "@": path.resolve(__dirname, "./src")
    }
  },
  // Configure asset handling
  assetsInclude: ['**/*.svg'],
})
