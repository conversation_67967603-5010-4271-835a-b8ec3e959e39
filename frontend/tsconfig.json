{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src", "src/types"], "exclude": ["node_modules"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "references": [{"path": "./tsconfig.node.json"}]}