# File Organization and Naming Conventions

**Version**: 1.0.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Overview

This document outlines the file organization structure and naming conventions for the AIDA project. Following these conventions ensures consistency across the codebase and makes it easier for developers to navigate and understand the project structure.

## Directory Structure

### Root Level

```
/
├── api_gateway/      # Kong API Gateway configuration
├── backend/          # Django backend API
├── frontend/         # React frontend application
├── js_component/     # Embeddable JS widget
├── demo_sites/       # Example integrations
├── docs/             # Project documentation
└── ...
```

### Backend Structure

```
backend/
├── main/             # Main Django app
│   ├── models/       # Database models
│   ├── serializers/  # API serializers
│   ├── views/        # API views
│   ├── utils/        # Utility functions
│   └── ...
├── worker/           # Celery worker configuration
├── authentication/   # Authentication logic
└── ...
```

### Frontend Structure

```
frontend/
├── src/
│   ├── components/   # React components
│   │   ├── auth/     # Authentication components
│   │   ├── layout/   # Layout components
│   │   ├── ui/       # UI components
│   │   └── views/    # Page views
│   ├── utils/        # Utility functions
│   ├── types/        # TypeScript type definitions
│   ├── hooks/        # Custom React hooks
│   └── ...
└── ...
```

### Documentation Structure

```
docs/
├── README.md         # Documentation index
├── architecture.md   # System architecture overview
├── user_types.md     # User types documentation
├── [feature].md      # Feature-specific documentation
└── ...
```

## Naming Conventions

### Files

1. **React Components**: PascalCase for component files
   - `Button.tsx`, `UserProfile.tsx`, `AccountList.tsx`

2. **Utility Files**: camelCase for utility files
   - `apiClient.ts`, `formatDate.ts`, `normalizeAddress.ts`

3. **Constants**: SNAKE_CASE for constant files
   - `API_CONSTANTS.ts`, `COLOR_PALETTE.ts`

4. **Backend Models**: PascalCase for model files
   - `Account.py`, `UserProfile.py`

5. **Backend Views**: snake_case for view files
   - `account_views.py`, `auth_views.py`

6. **Documentation**: snake_case for documentation files
   - `user_types.md`, `subscription_tiers.md`

### Functions and Variables

1. **React Components**: PascalCase for component names
   - `const UserProfile = () => { ... }`

2. **Functions**: camelCase for function names
   - `const formatDate = (date) => { ... }`

3. **Constants**: UPPER_SNAKE_CASE for constants
   - `const API_URL = 'https://api.example.com'`

4. **Django Models**: PascalCase for model classes
   - `class Account(models.Model): ...`

5. **Database Fields**: snake_case for database fields
   - `first_name = models.CharField(...)`

## Best Practices

1. **Component Organization**:
   - Group related components in directories
   - Create index.ts files for cleaner imports
   - Separate container components from presentational components

2. **Utility Functions**:
   - Group related utilities in namespaced files
   - Export utilities as named exports for better tree-shaking
   - Provide clear documentation for each utility function

3. **Documentation**:
   - Follow the template defined in `docs/template.md`
   - Include version information at the top of each document
   - Update the main README.md when adding new documentation

4. **Code Style**:
   - Follow ESLint rules for JavaScript/TypeScript
   - Follow PEP 8 for Python code
   - Use TypeScript types for all React components and functions

## Related Documentation

- [Architecture](architecture.md) - Overall system architecture

## Changelog

### 1.0.0 (2025-03-05)
- Initial file organization and naming conventions document 