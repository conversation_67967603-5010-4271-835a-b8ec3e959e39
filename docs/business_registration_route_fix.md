# Business Registration Route Fix

**Version**: 1.0.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Issue Overview

The business registration process was failing with a 404 error when users attempted to register a business after creating a user account. The frontend form was correctly collecting the business information, but the registration request was failing to reach the backend API properly, resulting in:

```
POST /api/auth/register/business/ HTTP/1.1 404 51
```

## Root Causes

Through investigation, multiple issues were identified:

1. **Missing API Route Prefixes**: The Kong API Gateway was configured to forward requests with an `/api` prefix, but the Django backend only had routes configured without the `/api` prefix.

2. **Missing User Email in Payload**: The backend's `register_business` function was looking for a user with the email provided in the request, but the frontend wasn't including the user's email in the submission data, resulting in:
   ```
   "User not found. Please register first."
   ```

3. **Authentication Token Issues**: The frontend was having trouble ensuring proper authentication before submitting the business registration.

## Solutions Implemented

### 1. Backend Route Configuration

The `backend/main/urls.py` file was updated to handle both prefixed and non-prefixed API routes:

```python
# Before
path('auth/register-business/', register_business, name='register-business'),
path('auth/register/business/', register_business, name='register-business-alt'),

# After
path('auth/register-business/', register_business, name='register-business'),
path('auth/register/business/', register_business, name='register-business-alt'),
path('api/auth/register/business/', register_business, name='register-business-api'),
path('api/auth/register-business/', register_business, name='register-business-api-alt'),
```

This ensures that regardless of how the frontend forms the URL (with or without the `/api` prefix, and with slash or hyphen formatting), the request will be routed to the correct backend handler.

### 2. Including User Email in Submission

The `frontend/src/components/auth/BusinessRegistration.tsx` file was modified to include the user's email in the business registration submission:

```typescript
// Get user email from registration data
const registrationDataStr = localStorage.getItem('registrationData');
let userEmail = '';

if (registrationDataStr) {
  try {
    const registrationData = JSON.parse(registrationDataStr);
    userEmail = registrationData.email || '';
  } catch (e) {
    console.error('Error parsing registration data:', e);
  }
}

if (!userEmail) {
  toast.error('User email not found. Please register again.');
  navigate('/register');
  setLoading(false);
  return;
}

// Add normalized fields to the form data
const submissionData = {
  ...formData,
  normalized_name: normalizeBusinessName(formData.business_name),
  normalized_address: normalizeAddress(formData.address_line1),
  email: userEmail // Include the user's email in the submission
};
```

This change ensures that the backend can identify the user associated with the business being registered.

### 3. Improved Authentication Flow

The `ensureAuthenticated` function in the BusinessRegistration component was enhanced to:
- Properly check for stored credentials
- Make an authentication API call if needed
- Store the received token in localStorage
- Add comprehensive error handling and logging

## Testing and Verification

The fix was verified by:
1. Testing direct API calls to all variations of the business registration endpoint
2. Verifying that the Kong API Gateway properly forwards requests to the correct backend routes
3. Confirming the frontend correctly includes the user's email in the submission data
4. Testing the complete user journey from registration to business creation

## Technical Context

### Request Flow

```
Frontend (React) → Kong API Gateway (8080) → Django Backend (8000)
```

1. Frontend makes requests to `http://localhost:8080/api/...`
2. Kong forwards to `http://host.docker.internal:8000/api/...`
3. Django handles the request based on URL patterns in `urls.py`

### Authentication Flow

1. User registers with email/password
2. Registration data is stored in localStorage
3. Before business registration, frontend authenticates using stored credentials
4. Authentication token is included in the business registration request

## Lessons Learned

1. **API Gateway Configuration**: Ensure consistent URL pattern handling between frontend, API gateway, and backend.
2. **Debugging Tools**: Implement detailed logging in both frontend and backend to track request/response cycles.
3. **Data Validation**: Verify all required data is included in API requests, especially user identifiers.
4. **Route Flexibility**: Design routes with flexibility to handle different formatting patterns.

## Future Recommendations

1. Standardize URL patterns across the application to improve maintainability
2. Add automated tests for critical user flows like registration and business creation
3. Enhance error handling and user feedback for failed API requests
4. Consider implementing OpenAPI/Swagger documentation for API endpoints 