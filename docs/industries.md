# Industry Values Management

**Version**: 1.0.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Overview

This document explains our centralized approach to handling industry values throughout the application. We've implemented a unified system to ensure consistent display and handling of industry values across all components.

## Problem Solved

Previously, the application had several issues with industry values:

- Backend values with underscores and lowercase formatting (e.g., `food_service`) were directly shown to users
- Different components displayed industry values inconsistently
- Industry options were duplicated across multiple components
- Handling of single vs. multiple industry values was inconsistent

## Centralized Solution

We've created a centralized utility file for managing all industry-related functionality:

```
frontend/src/utils/industries.ts
```

This file provides:

1. A standardized `INDUSTRY_OPTIONS` array with properly formatted display labels
2. Functions for converting backend values to user-friendly formats
3. Utilities for handling both single and multiple industry values

## Key Components

### INDUSTRY_OPTIONS

A master list of industry options with backend values and display labels:

```typescript
export const INDUSTRY_OPTIONS: IndustryOption[] = [
  { value: 'tech', label: 'Technology' },
  { value: 'finance', label: 'Finance' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'education', label: 'Education' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'food_service', label: 'Food Service' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'hospitality', label: 'Hospitality' },
  { value: 'restaurant', label: 'Restaurant / Dining' },
  { value: 'other', label: 'Other' }
];
```

### Utility Functions

- `getIndustryLabel(value: string)`: Converts backend values to user-friendly display labels
- `parseIndustryValues(industryString: string)`: Converts comma-separated industry strings to arrays
- `capitalizeWords(str: string)`: Helps with proper text formatting

## Integration in Components

The centralized industry utility is used in the following components:

1. **BusinessRegistration** - For selecting industries during registration
2. **AccountForm** - For editing industry values in account settings
3. **AccountView** - For displaying formatted industry values
4. **AccountList** - For displaying industry values in the accounts table
5. **MyBusinessEditPage** - For handling industry values during business profile editing
6. **MyAccountPage** - For displaying industry values on account pages
7. **AidaSetup** - For showing industry values in account selection

## How to Use

### Importing the Utility

```typescript
import { INDUSTRY_OPTIONS, getIndustryLabel, parseIndustryValues } from '../../utils/industries';
```

### For Displaying Industry Values

Use the `getIndustryLabel` function to convert backend values to user-friendly labels:

```typescript
// For a single industry value
<span>{getIndustryLabel(account.industry_name)}</span>

// For handling both string and array formats
<span>
  {typeof account.industry_name === 'string' 
    ? getIndustryLabel(account.industry_name) 
    : Array.isArray(account.industry_name) 
      ? account.industry_name.map(getIndustryLabel).join(', ') 
      : ''}
</span>
```

### For Form Options

Use the `INDUSTRY_OPTIONS` array directly in your select components:

```typescript
<Select
  mode="tags"
  placeholder="Select industry"
  options={INDUSTRY_OPTIONS}
/>
```

### For Form Submission

Convert array values to comma-separated strings for backend submission:

```typescript
if (Array.isArray(values.industry_name)) {
  values.industry_name = values.industry_name.join(', ');
}
```

### For Form Initialization

Convert comma-separated industry strings to arrays for form initialization:

```typescript
if (typeof formValues.industry_name === 'string' && formValues.industry_name) {
  formValues.industry_name = parseIndustryValues(formValues.industry_name);
}
```

## Extending the Industry Options

To add new industry options:

1. Open `frontend/src/utils/industries.ts`
2. Add new entries to the `INDUSTRY_OPTIONS` array:
   ```typescript
   { value: 'new_backend_value', label: 'Display Label' }
   ```

## Benefits

- **Consistency**: All components use the same industry options and display them uniformly
- **Maintainability**: Adding or changing industry options only requires updating one file
- **User Experience**: Backend values are never shown directly to users
- **Flexibility**: Handles both single and multiple industry values consistently 