# AIDA System Architecture

**Version**: 1.0.1  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Overview

This document provides a comprehensive overview of the AIDA system architecture, consolidating information from various sources into a single reference. AIDA is a multi-component system consisting of an admin interface, API backend, and embeddable JavaScript component.

## System Components

### Frontend (Admin Interface)
- **Technology**: React + Vite
- **Purpose**: Provides administrative dashboard interface
- **Location**: `/frontend` directory
- **Key Features**:
  - User management with different user types (AIDA Admin, Business Admin, Business User)
  - Business account management
  - Subscription tier management
  - Widget configuration

### Backend (API Server)
- **Technology**: Django REST API
- **Purpose**: Provides data and business logic
- **Location**: `/backend` directory
- **Key Features**:
  - Authentication and authorization
  - Business data management
  - Subscription management
  - Widget API endpoints
  - Uses Celery for asynchronous tasks (document processing)

### JS Component (Embeddable Widget)
- **Technology**: Svelte
- **Purpose**: Lightweight, embeddable JavaScript component for third-party websites
- **Location**: `/js_component` directory
- **Key Features**:
  - AI chat capabilities
  - Customizable UI based on subscription tier
  - Document context awareness
  - Optimized bundle size

### API Gateway
- **Technology**: Kong
- **Purpose**: API routing and management
- **Location**: `/api_gateway` directory
- **Key Features**:
  - Request routing between frontend and backend
  - URL pattern normalization
  - Authentication token handling

## Data Flow Architecture

### User Registration Flow
1. User provides personal information (Register.tsx)
2. User provides business information (BusinessRegistration.tsx)
3. Backend creates user and business accounts
4. User assigned to appropriate Django group based on role

### Authentication Flow
1. User registers with email/password
2. Registration data is stored in localStorage
3. User authenticates using stored credentials
4. Authentication token included in subsequent requests

### Request Flow
```
Frontend (React) → Kong API Gateway (8080) → Django Backend (8000)
```

1. Frontend makes requests to `http://localhost:8080/api/...`
2. Kong forwards to `http://host.docker.internal:8000/api/...`
3. Django handles the request based on URL patterns in `urls.py`

## Business Model

### Subscription Tiers
The system implements three subscription tiers for businesses:

1. **Basic Tier (Free)**
   - 1 team member
   - Basic chat widget
   - Standard AI model
   - Community support

2. **Pro Tier ($79/month)**
   - 5 team members
   - Advanced chat widget customization
   - Document upload (50 documents)
   - URL scraping (5 pages)
   - Priority email support

3. **Premium Tier ($149/month)**
   - Unlimited team members
   - Complete chat widget customization
   - Unlimited document uploads
   - Unlimited URL scraping
   - Advanced AI model access
   - 24/7 priority support

> **Implementation Note**: Team member limits are currently enforced only through frontend validation. Backend validation to enforce these limits is planned but not yet implemented.

### User Types
The system implements three user types with different permissions:

1. **AIDA Admin**
   - Full system access
   - Manages Business Admin accounts
   - Access to system configuration

2. **Business Admin**
   - Business-level configuration
   - Manages business settings
   - Access to advanced business features
   - Can create accounts within their business

3. **Business User**
   - Simplified business operations
   - Basic usage rights
   - Cannot create new accounts

## Development Setup

### Prerequisites
- Node.js 16+
- Python 3.8+
- Docker and Docker Compose
- Redis (for Celery message broker)

### Quick Start
1. Clone the repository
2. Install Redis
3. Start all development services: `npm run dev`

### Component-specific Setup

#### API Gateway
```bash
cd api_gateway
./start.sh
```

#### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

#### Backend Development
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

#### Celery Worker
```bash
cd backend
celery -A worker worker -l info
```

## Deployment

### Docker Setup
The project uses Docker for consistent development and deployment environments:

- Frontend container: Serves the React admin interface
- Backend container: Runs the Django API server
- Database container: PostgreSQL for development and production
- Nginx container: Handles routing and static file serving

### Running with Docker
```bash
# Build and start all services
docker-compose up --build
```

## Integration Guide

### Embedding JS Component
```html
<div id="js-component"></div>
<script src="https://your-domain.com/js-component.js"></script>
<script>
  initComponent({
    containerId: 'js-component',
    apiKey: 'your-api-key'
  });
</script>
```

## Related Documentation

- [User Types](user_types.md) - Detailed information about user types and permissions
- [Subscription Tiers](subscription_tiers.md) - Detailed information about subscription tiers
- [Business Duplicate Detection](business_duplicate_detection.md) - Information about duplicate business detection

## Changelog

### 1.0.1 (2025-03-05)
- Added implementation note regarding subscription tier team member enforcement

### 1.0.0 (2025-03-05)
- Initial consolidated architecture document 