# Business Subscription Tiers

**Version**: 1.1.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Overview
This document outlines the implementation of subscription tiers in our application. Subscriptions are managed at the business level rather than individual user accounts, providing all team members access to the same features while simplifying administration and billing.

## Business Tiers and User Groups Relationship

```mermaid
classDiagram
    class BusinessAccount {
        +string name
        +subscription_tier
        +manage_users()
        +manage_subscription()
    }
    
    class SubscriptionTier {
        <<enumeration>>
        BASIC
        PRO
        PREMIUM
    }
    
    class User {
        +string name
        +string email
        +access_features()
    }
    
    class BusinessAdminUser {
        +manage_team_members()
        +change_subscription_plans()
        +configure_widgets()
        +add_remove_users()
    }
    
    class BusinessRegularUser {
        +use_available_features()
        +limited_configuration()
    }
    
    class BasicTier {
        +Free
        +1 team member
        +Basic chat widget
        +Standard AI model
        +Community support
    }
    
    class ProTier {
        +$79/month
        +5 team members
        +Advanced customization
        +Document upload (50)
        +URL scraping (5 pages)
        +Priority email support
    }
    
    class PremiumTier {
        +$149/month
        +Unlimited team members
        +Complete customization
        +Unlimited documents
        +Unlimited URL scraping
        +Premium AI model
        +24/7 support
    }
    
    BusinessAccount "1" -- "1" SubscriptionTier : has
    BusinessAccount "1" -- "1..n" User : contains
    User <|-- BusinessAdminUser : is a
    User <|-- BusinessRegularUser : is a
    SubscriptionTier <|-- BasicTier : type
    SubscriptionTier <|-- ProTier : type
    SubscriptionTier <|-- PremiumTier : type
```

### Key Points:

1. **Subscription Tier Applies to the Entire Business**
   - All users within a business have access to the same tier features
   - Tier limits and capabilities are set at the business level

2. **User Role Determines Administrative Capabilities**
   - Business Admins: Can manage users and subscription settings
   - Regular Users: Can use features but cannot change business settings

3. **Feature Access is Determined by Tier, Not User Role**
   - All users get the same feature access based on business tier
   - Team member limits apply to the business as a whole

4. **Permission Model**
   - Administrative permissions: Controlled by user role
   - Feature access: Controlled by business subscription tier

## Subscription Tiers

### Basic Tier (Free)
- **Price**: Free
- **Team Member Limit**: 1 team member
- **Features**:
  - AI chat assistant on your website
  - Basic chat widget (light & dark themes)
  - Standard AI model
  - Community support
  - Self-service setup and management

### Pro Tier ($79/month)
- **Price**: $79/month
- **Team Member Limit**: 5 team members
- **Features**:
  - All Basic tier features
  - Advanced chat widget customization
  - Document upload (50 documents)
  - URL scraping (5 pages)
  - Custom chat widget icon
  - Priority email support

### Premium Tier ($149/month)
- **Price**: $149/month
- **Team Member Limit**: Unlimited team members
- **Features**:
  - All Pro tier features
  - Complete chat widget customization
  - Unlimited document uploads
  - Unlimited URL scraping
  - Advanced AI model access
  - Booking & ordering through chat
  - 24/7 priority support
  - Premium AI model with enhanced capabilities

## Technical Implementation

### Database Schema
Subscription information is stored in the `Account` model:

```python
class Account(models.Model):
    SUBSCRIPTION_TIERS = [
        ('basic', 'Basic'),
        ('pro', 'Pro'),
        ('premium', 'Premium'),
    ]

    # Subscription Information
    subscription_tier = models.CharField(
        max_length=20,
        choices=SUBSCRIPTION_TIERS,
        default='basic',
        help_text="The subscription tier for this business account"
    )
    subscription_start_date = models.DateField(null=True, blank=True)
    subscription_renewal_date = models.DateField(null=True, blank=True)
    subscription_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('trial', 'Trial'),
            ('expired', 'Expired'),
            ('canceled', 'Canceled'),
        ],
        default='active',
        null=True,
        blank=True
    )
```

### Team Member Limit Implementation

#### Current Implementation Status

| Feature | Status | Notes |
|---------|--------|-------|
| Database Model | ✅ Complete | Account model includes subscription tier fields |
| Frontend Validation | ✅ Complete | UI enforces team member limits with visual indicators |
| Backend Validation | ❌ Not Implemented | Server does not currently validate team member limits |

> **Important**: The system currently relies solely on frontend validation for enforcing team member limits. Backend validation is planned but not yet implemented.

#### Frontend Validation
The application enforces team member limits with frontend validation:

```javascript
const getTeamMemberLimit = (tier: string) => {
  switch(tier.toLowerCase()) {
    case 'premium': return Infinity; // Unlimited
    case 'pro': return 5;
    case 'basic':
    default: return 1;
  }
};
```

#### Team Member Display
The UI provides different visual states based on subscription tier and usage:

1. **Basic Tier (1 member)**
   - Shows "1 of 1" with green success state
   - Progress bar in green
   - Message: "Basic plan includes 1 team member"

2. **Pro Tier (5 members)**
   - Shows "X of 5" with blue progress bar
   - Warning state (red) when limit reached
   - Message: "Team member limit reached"
   - Upgrade button to Premium tier

3. **Premium Tier (Unlimited)**
   - Shows "Unlimited" with no progress bar
   - No limit warnings

### Feature Access Control

The system controls access to various features based on subscription tier:

1. **Document Upload Limits**:
   - Basic: Not available
   - Pro: 50 documents
   - Premium: Unlimited

2. **URL Scraping Limits**:
   - Basic: Not available
   - Pro: 5 pages
   - Premium: Unlimited

3. **Chat Widget Customization**:
   - Basic: Basic themes only
   - Pro: Advanced customization
   - Premium: Complete customization

4. **AI Model Access**:
   - Basic: Standard model
   - Pro: Standard model
   - Premium: Premium model with enhanced capabilities

5. **Support Level**:
   - Basic: Community support
   - Pro: Priority email support
   - Premium: 24/7 priority support

## User Experience

### Business Administrators
- Can view current subscription status
- See team member usage with visual indicators
- Access upgrade options
- Manage subscription changes

### Team Members
- Can view current subscription status
- See team member usage
- Cannot manage subscriptions (messaging to contact admin)

## Upgrade Flow

1. Only business administrators can initiate upgrades
2. When limits are reached, the UI displays warning states with upgrade buttons
3. Clicking upgrade displays payment options (implemented through `handleUpgradeClick`)
4. Subscriptions are billed monthly at the business level

## Rationale for Business-Level Subscriptions

1. **Resource Allocation**: Features like storage, API calls, and message quotas are shared across a business
2. **Billing Efficiency**: One subscription per business is more straightforward than per-user billing
3. **Consistent User Experience**: All users within a business have access to the same feature set
4. **Role-Based Access Control**: User roles control permissions, not feature availability
5. **Simplified Upselling**: Easier to manage upgrade paths at the business level

## Future Enhancements

1. **Backend Validation**: Implement server-side validation to enforce team member limits
   - **Priority**: High - Current frontend-only validation can be bypassed
   - **Proposed Implementation**: Create backend service to validate team size against subscription tier
   - **Impact**: Critical for ensuring subscription tier integrity

2. **Billing Integration**: Connect with payment processors like Stripe to manage subscriptions
3. **Usage Tracking**: Monitor resource usage (documents, API calls) per account
4. **Automated Notifications**: Alert admins when approaching limits
5. **Trial Periods**: Implement time-limited trials of higher-tier features

## FAQ for End Users

### How do business subscriptions work?
Subscriptions are tied to your business account, not individual users. When your business upgrades to a higher tier, all team members automatically gain access to the new features.

### How does billing work?
Your business is billed monthly on the date you upgrade. When you upgrade, we prorate the amount for the remainder of your current billing period.

### Can we cancel anytime?
Yes, your business administrator can cancel your subscription at any time. Your team will continue to have access to your plan features until the end of your current billing period.

### What are document uploads and URL scraping?
Document uploads allow you to train the AI with your business-specific information by uploading files. URL scraping automatically extracts content from your website pages to train the AI.

### What's the difference between standard and premium AI models?
Standard models provide excellent general-purpose AI capabilities. Premium models offer enhanced reasoning abilities, better context understanding, and more sophisticated responses.
