# AIDA Documentation

## Overview

This directory contains all technical documentation for the AIDA project. This README serves as an index to help navigate the various documentation files and establishes a "single source of truth" for project knowledge.

## Documentation Version

**Current Version**: 1.0.1  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Documentation Index

### System Architecture
- [Project Architecture](../README.md) - Overall project structure, components, and setup instructions
- [System Architecture](architecture.md) - Comprehensive architecture document that consolidates information from various sources
- [File Organization](file_organization.md) - File organization structure and naming conventions

### User Types and Permissions
- [User Types](user_types.md) - User type hierarchy, access patterns, and implementation details
  - Version: 1.0.0
  - Last Updated: 2025-03-05
  - Covers: User type definitions, Django group integration, and permission hierarchy

### Business Features
- [Business Registration Route Fix](business_registration_route_fix.md) - Technical details about fixing business registration routes
  - Version: 1.0.0
  - Last Updated: 2025-03-05
  - Covers: API route configuration, authentication flow, and route issues

- [Business Duplicate Detection](business_duplicate_detection.md) - Implementation of the business duplicate detection system
  - Version: 1.0.0
  - Last Updated: 2025-03-05
  - Covers: Normalization utilities, similarity functions, and duplicate detection APIs

- [Industries](industries.md) - Centralized approach to handling industry values
  - Version: 1.0.0
  - Last Updated: 2025-03-05
  - Covers: Industry options management, display formatting, and integration in components

### Subscription and Tiers
- [Subscription Tiers](subscription_tiers.md) - Business subscription tier implementation
  - Version: 1.1.0
  - Last Updated: 2025-03-05
  - Covers: Tier features, pricing, implementation details, and validation status

- [Diagram](diagram.md) - Visual representation of subscription tiers and business relationships
  - Version: 1.1.0
  - Last Updated: 2025-03-05
  - Covers: Business tiers relationship diagrams and technical implementation

## Documentation Standards

### Versioning
All documentation files should include:
- Version number (following semantic versioning)
- Last updated date
- Owner/maintainer information

### File Structure
Documentation files should follow this structure:
1. Title and overview
2. Problem statement (if applicable)
3. Solution/implementation details
4. Technical specifications
5. Usage examples
6. References to related documentation

### Update Process
When updating documentation:
1. Increment the version number following semantic versioning:
   - MAJOR: Significant changes to functionality
   - MINOR: New features or non-breaking changes
   - PATCH: Bug fixes or minor updates
2. Update the "Last Updated" date
3. Update the main README.md with the new version information
4. Add a changelog entry for significant changes

## Upcoming Documentation

The following documentation is planned for future updates:
- API Specifications
- Widget Configuration Guide
- AnythingLLM Integration
- Deployment Guide
- Testing Strategy

## Changelog

### 1.0.1 (2025-03-05)
- Updated subscription_tiers.md to version 1.1.0 with accurate implementation status
- Updated diagram.md to version 1.1.0 with team member validation implementation details

### 1.0.0 (2025-03-05)
- Initial creation of centralized documentation structure
- Added versioning information to existing documents
- Created comprehensive documentation index 