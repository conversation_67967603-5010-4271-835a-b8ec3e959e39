# Theme Customization System

## Overview

The theme customization system provides a comprehensive solution for users to customize the UI appearance of the AIDA application. This system has been streamlined to offer preset themes that combine color schemes, logo choices, and fonts into cohesive design options.

## Architecture

The system follows a React Context-based architecture with the following components:

1. **ThemeContext**: A React context that manages the theme state and provides theme-related functions
2. **CSS Variables**: A set of CSS variables that define the theme colors, fonts, and other styling properties
3. **ThemeCustomizer Component**: A UI component for selecting preset themes
4. **Logo Component**: A specialized component for rendering SVG logos with proper formatting

## Current Implementation

### ThemeContext

The `ThemeContext` provides a central store for theme-related data and functions:

```typescript
// Updated theme options
export type ThemeMode = 'light' | 'dark' | 'system';
export type PresetTheme = 'modern' | 'ocean';

// Preset theme configuration
export const PRESET_THEMES = [
  // Light themes only
  { 
    id: 'modern', 
    name: 'Modern', 
    logo: 'aida4', 
    font: 'inter',
    primaryColor: '#171717', // Black
    mode: 'light',
    logoIsPng: false
  },
  { 
    id: 'ocean', 
    name: 'Ocean', 
    logo: 'aida4', 
    font: 'nunito',
    primaryColor: '#0891b2', // Cyan-600
    accentColor: '#06b6d4', // Cyan-500
    mode: 'light',
    logoIsPng: false
  }
];
```

The context uses React's useState and useEffect hooks to manage the theme state and persist it in localStorage.

### Preset Themes

Each preset theme combines specific attributes:

1. **Modern** (Default)
   - Logo: AIDA 4 (aida4.svg)
   - Primary Color: #171717 (Black)
   - Font: Inter
   - Mode: Light

2. **Ocean**
   - Logo: AIDA 4 (aida4.svg)
   - Primary Color: #0891b2 (Cyan-600)
   - Accent Color: #06b6d4 (Cyan-500)
   - Font: Nunito Sans (body) + Space Grotesk (headings)
   - Mode: Light

### Font System

The theme system implements a sophisticated font pairing approach:

1. **Modern Theme**
   - Primary Font: Inter
   - Usage: Clean, modern interface with excellent readability

2. **Ocean Theme**
   - Body Font: Nunito Sans
     - Clean, readable design
     - Good x-height for better legibility
     - Weights: 400 (regular), 600 (semibold), 700 (bold)
   - Heading Font: Space Grotesk
     - Geometric sans-serif for visual interest
     - Creates contrast with body text
     - Weights: 400-700 range

### Typography Hierarchy

The system implements a clear typography hierarchy:

```css
/* Headings */
h1 { font-size: 2.5rem; line-height: 1.2; }
h2 { font-size: 2rem; line-height: 1.2; }
h3 { font-size: 1.75rem; line-height: 1.2; }
h4 { font-size: 1.5rem; line-height: 1.2; }
h5 { font-size: 1.25rem; line-height: 1.2; }
h6 { font-size: 1rem; line-height: 1.2; }

/* Body Text */
p { font-size: 1rem; line-height: 1.5; }

/* UI Elements */
.text-sm { font-size: 0.875rem; line-height: 1.25; }
```

### ThemeCustomizer Component

The UI component has been simplified to:

- Only display light themes
- Show a visual preview of each theme with sample elements
- Allow users to preview themes before saving changes
- Apply the selected theme with the "Save Changes" button

```jsx
export const ThemeCustomizer: React.FC = () => {
  const { themeMode, presetTheme, setPresetTheme, refreshTheme } = useTheme();
  
  // Temp values for preview
  const [tempTheme, setTempTheme] = useState<PresetTheme>(presetTheme);
  const [previewedTheme, setPreviewedTheme] = useState<PresetTheme | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Save function - apply changes to context and refresh
  const saveTheme = () => {
    setPresetTheme(tempTheme);
    refreshTheme();
    setHasUnsavedChanges(false);
    setPreviewedTheme(tempTheme);
  };
  
  // Preview function - temporarily show theme without saving
  const previewTheme = (theme: PresetTheme) => {
    // ...preview implementation...
  };
  
  return (
    <div className="p-4 bg-card rounded-xl shadow-md border border-border">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h2 className="text-lg font-semibold">Theme Customization</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Click a theme to preview. Changes won't be applied until you save.
          </p>
        </div>
        
        {hasUnsavedChanges && (
          <Button size="sm" onClick={saveTheme} className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            <span>Save Changes</span>
          </Button>
        )}
      </div>
      
      <div className="space-y-6">
        {/* Themes Section */}
        <div>
          <h3 className="text-sm font-medium mb-3">Available Themes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {themes.map(theme => (
              <ThemeCard 
                key={theme.id}
                theme={theme}
                isActive={tempTheme === theme.id}
              />
            ))}
          </div>
        </div>
      </div>
      
      {hasUnsavedChanges && (
        <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700/30 p-3 rounded-lg flex items-center text-sm">
          <CheckCircle2 className="h-5 w-5 text-yellow-500 mr-2" />
          <p className="text-yellow-800 dark:text-yellow-300">Theme previewed but not saved. Click "Save Changes" to apply permanently.</p>
        </div>
      )}
    </div>
  );
};
```

## CSS Implementation

The theme system uses CSS variables to define and apply themes. Each theme has its own set of CSS variables for customizing various aspects of the UI:

```css
/* MODERN Light theme - The new default */
.theme-modern {
  --primary: 0 0% 9%; /* #171717 - Black */
  --primary-foreground: 0 0% 98%;
  
  /* Layout with neutral colors for better contrast */
  --header-bg: 210 20% 98%;
  --header-fg: 215 14% 34%;
  --nav-bg: 0 0% 100%;
  --nav-fg: 215 14% 34%;
  --footer-bg: 220 13% 91%;
  --footer-fg: 215 28% 17%;
}

/* OCEAN Light theme */
.theme-ocean {
  --primary: 190 91.3% 36.4%; /* #0891b2 - Cyan-600 */
  --primary-foreground: 0 0% 98%;
  
  /* Set more prominent accent color */
  --accent: var(--accent-color, 190 93.5% 50%); /* Brighter cyan */
  --accent-foreground: 0 0% 98%;
  
  /* Customize other UI elements with cyan shades */
  --ring: 190 91.3% 36.4%;
  
  /* Layout with neutral colors for better contrast */
  --header-bg: 210 20% 98%;
  --header-fg: 215 14% 34%;
  --nav-bg: 0 0% 100%;
  --nav-fg: 215 14% 34%;
  --footer-bg: 220 13% 91%;
  --footer-fg: 215 28% 17%;

  /* Font pairing for Ocean theme */
  --font-body: "Nunito Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-heading: "Space Grotesk", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
```

## Logo Implementation

Logo display has been optimized to prevent layout shifts and improve rendering. The system currently supports the following logo variants:

1. **AIDA 4** (aida4.svg)
   - Default logo for both Modern and Ocean themes
   - Clean, modern design
   - Optimized for light themes

2. **AIDA 4 Color** (aida4c.svg)
   - Alternative color variant
   - Available for special use cases

```css
/* Logo container styles */
.logo-container {
  position: relative;
  height: 2.5rem; /* h-10 */
  width: 10rem; /* w-40 */
}

/* Logo styles */
[class^="logo-"], 
.logo-png, 
.logo-svg {
  position: absolute;
  inset: 0;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Logo visibility rules */
[data-logo="aida4"] .logo-aida4,
[data-logo="aida4c"] .logo-aida4c {
  opacity: 1;
  z-index: 10;
}

/* Logo image and SVG handling */
[class^="logo-"] img, 
.logo-png img, 
.logo-svg img,
[class^="logo-"] svg, 
.logo-png svg, 
.logo-svg svg {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
```

## Technical Challenges

### ShadCn Integration Issues

The integration with ShadCn UI components presents some challenges:

1. **Button Styling**: While we can define the primary color using CSS variables, ShadCn's button component doesn't always properly apply these variables. Direct overrides with hex colors were needed for specific themes like Ocean.

2. **CSS Variables Format**: ShadCn requires HSL notation for their CSS variables, which can make it challenging to fine-tune specific colors. For example:

```css
/* ShadCn requires this format */
--primary: 190 91.3% 36.4%; /* HSL notation */

/* But sometimes we need to use direct overrides */
.theme-ocean button.bg-primary {
  background-color: #0891b2 !important; /* Direct hex color */
}
```

3. **Specificity Issues**: In some cases, ShadCn's own styling specificity requires the use of `!important` flags to override their defaults.

## Current Status

1. ✅ Simplified theme options (removed legacy theme)
2. ✅ Implemented sophisticated font pairing system
3. ✅ Improved typography hierarchy
4. ✅ Enhanced theme preview UI
5. ✅ Fixed logo layout issues
6. ✅ Enhanced button styling for themes
7. ⚠️ Partial success with ShadCn integration - most components now correctly apply theme colors

## Future Improvements

1. Better integration with ShadCn without relying on direct overrides or !important flags
2. Consider adding a custom theme creation option
3. Continue refining the preview experience
4. Explore adding back dark themes with proper testing and integration
5. Consider adding more font pairing options for each theme
