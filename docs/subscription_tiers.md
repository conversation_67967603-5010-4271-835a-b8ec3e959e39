# Subscription Tier Implementation

**Version**: 1.1.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## Overview
This document outlines the implementation strategy for subscription tiers tied to businesses rather than individual user accounts. The system provides three tiers: Basic, Pro, and Premium, each with specific features and team member limits.

## Current State Analysis
- Subscriptions are managed at the business level
- Three tiers: Basic (Free), Pro ($79/month), and Premium ($149/month)
- Team member limits:
  - Basic: 1 team member
  - Pro: 5 team members
  - Premium: Unlimited team members
- Team member count display shows:
  - For Basic tier: Always shows at least 1 member (green success state)
  - For Pro tier: Shows current count with blue progress bar
  - For Premium tier: Shows "Unlimited" with no progress bar

### ⚠️ Implementation Status

| Feature | Status | Notes |
|---------|--------|-------|
| Database Model | ✅ Complete | Account model includes all required subscription fields |
| Frontend Display | ✅ Complete | UI correctly shows tiers, member counts and limits |
| Frontend Validation | ✅ Complete | UI provides appropriate warnings and indicators |
| Backend Validation | ❌ Not Implemented | Server does not currently enforce team member limits |
| Team Management API | ⚠️ Partial | Basic account-user association exists, but no dedicated team management endpoints |

> **Important Note**: The system currently relies solely on frontend validation for enforcing team member limits. Backend validation is needed to prevent users from bypassing these limits.

## Implementation Details

### Team Member Display
1. **Basic Tier (1 member)**
   - Shows "1 of 1" with green success state
   - Progress bar in green
   - Message: "Basic plan includes 1 team member" with checkmark icon
   - No warning state for Basic tier

2. **Pro Tier (5 members)**
   - Shows "X of 5" with blue progress bar
   - Warning state (red) when limit reached
   - Message: "Team member limit reached" with warning icon
   - Upgrade button to Premium tier

3. **Premium Tier (Unlimited)**
   - Shows "Unlimited" with no progress bar
   - No limit warnings
   - No upgrade options

### Visual Indicators
- **Success State (Green)**
  - Used for Basic tier with 1 member
  - Checkmark icon
  - Green progress bar
  - Positive messaging

- **Warning State (Red)**
  - Used for Pro tier when limit reached
  - Warning triangle icon
  - Red progress bar
  - Upgrade messaging

- **Normal State (Blue)**
  - Used for Pro tier when under limit
  - Blue progress bar
  - Shows remaining slots

### User Experience
1. **Business Administrators**
   - Can view current subscription status
   - See team member usage
   - Access upgrade options
   - Manage subscription changes

2. **Team Members**
   - Can view current subscription status
   - See team member usage
   - No access to upgrade options
   - Clear messaging about contacting admin for changes

## Benefits
1. **Simplified Management**
   - Single subscription per business
   - Centralized billing
   - Easy team member management

2. **Clear Upgrade Path**
   - Visual indicators of current usage
   - Clear upgrade messaging
   - Easy upgrade process for admins

3. **Consistent Experience**
   - All team members see same subscription status
   - Clear communication of limits
   - Professional visual presentation

## Future Considerations
1. **Usage Analytics**
   - Track team member usage patterns
   - Monitor feature adoption
   - Identify upgrade opportunities

2. **Automated Notifications**
   - Alert admins when approaching limits
   - Suggest upgrades based on usage
   - Provide usage reports

3. **Feature Expansion**
   - Additional team member slots
   - Enhanced feature sets
   - Custom enterprise solutions

## Why Business-Level Subscriptions

1. **Resource Allocation**: Features like storage, API calls, and message quotas are typically shared across a business.
2. **Billing Efficiency**: One subscription per business is more straightforward than per-user billing.
3. **Consistent User Experience**: All users within a business should have access to the same feature set.
4. **Role-Based Access Control**: User roles (admin vs. regular) should control permissions, not feature availability.
5. **Simplified Upselling**: Easier to manage upgrade paths when dealing with businesses rather than individual users.

## Implementation Plan

### 1. Database Changes

Add subscription fields to the `Account` model:

```python
# In backend/main/models/account.py
class Account(models.Model):
    # Existing fields...
    
    SUBSCRIPTION_TIERS = [
        ('basic', 'Basic'),
        ('pro', 'Pro'),
        ('premium', 'Premium'),
    ]
    
    subscription_tier = models.CharField(
        max_length=20,
        choices=SUBSCRIPTION_TIERS,
        default='basic',
        help_text="The subscription tier for this business account"
    )
    
    # Track subscription metadata
    subscription_start_date = models.DateField(null=True, blank=True)
    subscription_renewal_date = models.DateField(null=True, blank=True)
    subscription_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('trial', 'Trial'),
            ('expired', 'Expired'),
            ('canceled', 'Canceled'),
        ],
        default='active',
        null=True, 
        blank=True
    )
```

### 2. API Updates

Update the Account serializer to include subscription information:

```python
# In backend/main/serializers/account.py
class AccountSerializer(serializers.ModelSerializer):
    # Existing fields...
    
    class Meta:
        model = Account
        fields = [
            # Existing fields...
            'subscription_tier',
            'subscription_start_date',
            'subscription_renewal_date',
            'subscription_status',
        ]
```

### 3. Frontend Type Updates

Update the Account type definition:

```typescript
// In frontend/src/types/account.ts
export interface Account {
  // Existing fields...
  subscription_tier?: 'basic' | 'pro' | 'premium';
  subscription_start_date?: string;
  subscription_renewal_date?: string;
  subscription_status?: 'active' | 'trial' | 'expired' | 'canceled';
}
```

### 4. UI Implementation

The MainLayout component displays the subscription from the account:

```typescript
// In frontend/src/components/Layout/MainLayout.tsx
const [businessName, setBusinessName] = useState("");
const [subscriptionTier, setSubscriptionTier] = useState("Basic");

useEffect(() => {
  // Fetch business name and subscription tier when user is authenticated
  if (isAuthenticated && user) {
    const fetchAccountData = async () => {
      try {
        const response = await api.get('/api/accounts/');
        if (response.data?.length > 0) {
          const account = response.data[0];
          setBusinessName(account.account_name || "Your Business");
          
          // Set subscription tier (capitalize first letter)
          const tier = account.subscription_tier || 'basic';
          setSubscriptionTier(tier.charAt(0).toUpperCase() + tier.slice(1));
        }
      } catch (error) {
        console.error('Failed to fetch account data:', error);
        setBusinessName("Your Business");
      }
    };
    
    fetchAccountData();
  }
}, [isAuthenticated, user]);

// In JSX
<Button
  variant="ghost"
  className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
  onClick={() => navigate('/subscription')}
>
  {businessName || "Your Business"} • {subscriptionTier}
</Button>
```

### 5. WelcomeCard Component

The WelcomeCard component displays features based on the subscription tier:

```typescript
// In frontend/src/components/ui/WelcomeCard.tsx
const getSubscriptionFeatures = () => {
  switch (subscriptionTier) {
    case "Pro":
      return [
        "5 team member accounts",
        "Document upload (50 documents)",
        "URL scraping (5 pages)",
        "Advanced chat widget customization"
      ];
    case "Premium":
      return [
        "Unlimited team members",
        "Unlimited document upload",
        "Unlimited URL scraping",
        "Advanced AI model access",
        "Booking & ordering through chat"
      ];
    case "Basic":
    default:
      return [
        "1 team member account",
        "Basic chat widget",
        "Standard AI model",
        "Community support"
      ];
  }
};
```

### 6. Subscription Component

The Subscription component shows tier information and team member usage:

```typescript
// In frontend/src/components/Subscription.tsx
// Get team member limit based on subscription tier
const getTeamMemberLimit = (tier: string) => {
  switch(tier.toLowerCase()) {
    case 'premium': return Infinity; // Unlimited
    case 'pro': return 5;
    case 'basic':
    default: return 1;
  }
};
```

### 7. Backend Validation (Planned)

> **Note**: This section represents planned work that is not yet implemented.

The following backend validation needs to be implemented to enforce team member limits:

```python
# Backend validation to add in the future
def validate_team_size(account, adding=1):
    """Validate that adding new team members doesn't exceed subscription limits"""
    current_size = account.users.count()
    max_size = get_team_member_limit(account.subscription_tier)
    
    if max_size != float('inf') and (current_size + adding) > max_size:
        return False, f"Adding {adding} member(s) would exceed your plan limit of {max_size} members."
    
    return True, ""

def get_team_member_limit(tier):
    """Get team member limit based on subscription tier"""
    if tier.lower() == 'premium':
        return float('inf')  # Unlimited
    elif tier.lower() == 'pro':
        return 5
    else:  # basic
        return 1
```

## Current Support Levels

- **Basic**: Community forum access
- **Pro**: Email support within 48 hours
- **Premium**: Priority email support

## Future Enhancements

1. **Billing Integration**: Connect with a payment processor like Stripe to manage subscriptions.
2. **Usage Tracking**: Implement monitoring for resource usage (storage, API calls) per account.
3. **Upgrade Flow**: Create a seamless process for businesses to upgrade their subscription.
4. **Trial Period**: Implement time-limited trials of higher-tier features.
5. **Team Member Enforcement**: Backend validation to enforce team member limits based on subscription tier.
   - **Priority: High** - Current frontend-only validation can be bypassed
   - **Proposed Implementation**: Create AccountUserService with validation logic
   - **API Integration**: Add validation to user invitation and account association endpoints

## Benefits

- **Clear Business Model**: Subscription tiers provide a straightforward monetization strategy.
- **Feature-Based Upselling**: Teams can upgrade to access needed features as they grow.
- **Simplified Administration**: Business admins can manage one subscription for the entire team.
- **Predictable Revenue**: Monthly subscription model provides consistent income. 