# User Type Implementation

**Version**: 1.0.0  
**Last Updated**: 2025-03-05  
**Maintained by**: Frontend Team

## User Type Hierarchy

```
AIDA Admin
└── Business Admin
    └── Business User
```

## Access Patterns

- AIDA Admin
  - Full system access
  - Can manage Business Admin accounts
  - Access to system configuration
  - Can create any account
  
- Business Admin
  - Business-level configuration
  - Can manage their business settings
  - Access to advanced business features
  - Can create accounts within their business
  
- Business User
  - Simplified version of Business Admin view
  - Basic business operations
  - Optimized for lay users
  - View-only access to accounts
  - Cannot create new accounts

## Implementation Status

1. User Type Definition ✅
   - Created TypeScript interfaces for user types
   - Defined UserType enum with three types (AIDA_ADMIN, BUSINESS_ADMIN, BUSINESS_USER)
   - Implemented type guard functions
   - Base user interface with extensions for each type
   - Added proper permission checks for features

2. Integration with Existing Components ✅
   - Integrated with existing components:
     * ✅ Using existing MainLayout for structure
     * ✅ Using existing routing and protection
     * ✅ Using existing navigation system
     * ✅ Added user type-based permissions

3. User-Specific Views ✅
   - Created content variations for each user type:
     * ✅ AIDA Admin: Full system management view
     * ✅ Business Admin: Business management focus
     * ✅ Business User: Simplified operations view
   - Shared common components but vary:
     * ✅ Dashboard layouts
     * ✅ Available actions
     * ✅ Information density
     * ✅ Feature accessibility

## Current Status

- ✅ User type definitions completed
- ✅ Implemented user-specific views
- ✅ Integrated with existing auth system
- ✅ Navigation filtering based on user type
- ✅ Django group integration
- ✅ Two-step registration process (personal + business information)

## Django Group Integration

### Implementation Details

1. User Type Mapping
```typescript
// Backend to Frontend mapping
if (user.is_superuser) {
  return UserType.AIDA_ADMIN;
} else if (user.groups.includes('Business Admins')) {
  return UserType.BUSINESS_ADMIN;
} else if (user.groups.includes('Business Regular Users')) {
  return UserType.BUSINESS_USER;
} else {
  return UserType.BUSINESS_USER; // Default to business user
}
```

2. Group Structure
- AIDA Admins: Managed via `is_superuser` flag
- Business Admins: Django group 'Business Admins'
- Business Regular Users: Django group 'Business Regular Users'

3. Permission Hierarchy
```
AIDA Admin (superuser)
└── Business Admin (group member)
    └── Business User (group member)
```

4. Group Assignment Rules
- AIDA Admins: Set as superusers, no group required
- Business Admins: Must be in 'Business Admins' group
- Regular Users: Must be in 'Business Regular Users' group

### Migration Status

✅ Completed Migrations:
1. Group Creation
   - Created 'Business Admins' group
   - Created 'Business Regular Users' group

2. Role Migration
   - Mapped AccountUser roles to groups
   - Set superuser status for AIDA admins

3. Verification
   - Management command to verify mappings
   - Cross-reference with AccountUser roles
   - Group membership validation

Current State:
- AIDA Admins properly set as superusers
- Business admins assigned to correct group
- Regular users in appropriate group
- All mappings verified and correct

## Next Steps

1. ⏳ Add more user-specific views for other pages
   - Implement role-based dashboards
   - Add user type specific features
   - Create specialized admin views

2. ⏳ Add user type transition animations
   - Smooth transitions between views
   - Visual feedback for type changes
   - Loading states for permissions

3. ⏳ Implement role-based feature flags
   - Configure feature access by role
   - Add granular permissions
   - Create feature management UI

4. ⏳ Enhance user management
   - Add role transition workflows
   - Implement approval processes
   - Add audit logging

## Component Structure

```
src/
├── components/
│   └── views/ 
│       ├── AccountsView.tsx ✅
│       └── [Other views pending...]
└── types/
    └── user.ts ✅
```

## Example View Implementation

```typescript
// Example: Accounts page with different views per user type
const AccountsView: React.FC<AccountsViewProps> = ({ user }) => {
  // Now using actual group-based permissions
  if (user.is_superuser) {
    return <SystemWideView />;  // AIDA Admin view
  }
  
  if (user.groups.includes('Business Admins')) {
    return <BusinessView />;    // Business Admin view
  }
  
  if (user.groups.includes('Business Regular Users')) {
    return <SimplifiedView />;  // Regular User view
  }
  
  return <SimplifiedView />;    // Default to Business User view
};
```

## Business Registration Flow

The application now uses a two-step registration process:

1. Personal Information (Register.tsx)
   - Basic user details (name, email, password)
   - Progress indicator showing step 1 of 2
   - Stores user data temporarily in localStorage

2. Business Information (BusinessRegistration.tsx)
   - Business details (name, address, industry)
   - Address validation and formatting
   - Business name deduplication check
   - Creation of user account linked to business

This flow ensures proper grouping of users with their associated businesses while maintaining a clean user experience. 