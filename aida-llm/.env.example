# postgres
POSTGRES_DB=<postgres_db>
POSTGRES_USER=<postgres_usr>
POSTGRES_PASSWORD=<postgres_pwd>

# Qdrant
QDRANT__SERVICE__API_KEY=<qdrant_api_key>

# Ollama Configuration
OLLAMA_DOCKERFILE=Dockerfile.ollama.dev

# Optional host port overrides (docker-compose)
# If unset, defaults are used.
# Backend (container 8000)
AIDA_BACKEND_PORT=8000
# Postgres (container 5432)
AIDA_POSTGRES_PORT=5432
# Qdrant HTTP/GRPC (container 6333/6334)
AIDA_QDRANT_HTTP_PORT=6333
AIDA_QDRANT_GRPC_PORT=6334
# Nginx HTTP/HTTPS (container 80/443)
AIDA_NGINX_HTTP_PORT=8080
AIDA_NGINX_HTTPS_PORT=8443
