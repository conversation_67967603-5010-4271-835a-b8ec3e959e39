# aida-llm

AIDA (AI-powered Document Assistant) is an application that provides chat functionality with various LLM providers, including Retrieval-Augmented Generation (RAG) capabilities.

## Download Files
- nginx/.sslcerts/cert.pem
- nginx/.sslcerts/key.pem
- ollama/modelfile_restaurant_dev.md
- ollama/modelfile_restaurant.md
- backend/.env
- .env

## Setup

### Environment Configuration
This include .env and backend/.env

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Modify the `.env` file to configure:
   - Database connections (PostgreSQL)
   - Vector database (Qdrant)
   - Vector Dimension
   - LLM providers (OpenAI, Anthropic, Cohere, Ollama)
   - GPU settings for Ollama

### Choose yml file
- docker-compose.yml: production, only ports for nginx are exposed to outside
- docker-compose-dev.yml: development only, ports for all services are exposed to outside to easy access

### Port configuration via .env

You can override host port mappings through environment variables in the root `.env`:

- `AIDA_BACKEND_PORT` (default 8000) maps to backend container port 8000
- `AIDA_POSTGRES_PORT` (default 5432) maps to Postgres container port 5432
- `AIDA_QDRANT_HTTP_PORT` (default 6333) maps to Qdrant HTTP port 6333
- `AIDA_QDRANT_GRPC_PORT` (default 6334) maps to Qdrant gRPC port 6334
- `AIDA_NGINX_HTTP_PORT` (default 80) maps to Nginx container port 80
- `AIDA_NGINX_HTTPS_PORT` (default 443) maps to Nginx container port 443

Example `.env` snippet:

```
AIDA_BACKEND_PORT=8000
AIDA_POSTGRES_PORT=5432
AIDA_QDRANT_HTTP_PORT=6333
AIDA_QDRANT_GRPC_PORT=6334
AIDA_NGINX_HTTP_PORT=8080
AIDA_NGINX_HTTPS_PORT=8443
```

Then run:

```
docker compose -f docker-compose-dev.yml up -d --build
```

### Docker Setup

Run the application using Docker Compose:

```bash
docker-compose -f docker-compose.yml up -d --build
```

## Testing

### Unit Tests

Run unit tests to verify individual components:

```bash
python -m backend.tests.run_tests
```

### Integration Tests

Run integration tests to verify the system with real databases and LLM providers:

```bash
python -m backend.tests.integration.run_integration_tests
```

Integration tests require:
- Running PostgreSQL database
- Running Qdrant vector database
- Running Ollama server (for Ollama tests)
- OpenAI API key (for embedding generation)

For more details, see the [Integration Tests README](backend/tests/integration/README.md).

## Documentation

- [Backend README](backend/README.md): Backend structure and setup
- [Chat API Documentation](backend/main/service/README_CHAT_API.md): API endpoints for chat functionality

## Security

The following security enhancements are planned:

- Enable HTTPS on Ollama
- Enable HTTPS on Qdrant
- Enable HTTPS on backend
