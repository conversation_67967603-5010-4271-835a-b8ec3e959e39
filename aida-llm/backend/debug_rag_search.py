"""
Debug script to test RAG search functionality directly.
"""

import asyncio
import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'main.settings')
django.setup()

from main.service.core.rag_service import RAGService
from main.data.vector_db_utility import VectorDBUtility


async def test_rag_search():
    """Test RAG search functionality with the ThinkPad E16 G1 query."""
    
    organization_slug = "ipearl-88b5f0b2"
    query = "please send link to the cover for ThinkPad E16 G1"
    
    print(f"Testing RAG search for query: '{query}'")
    print(f"Organization: {organization_slug}")
    print("-" * 50)
    
    # Initialize RAG service
    rag_service = RAGService()
    print(f"RAG Service initialized:")
    print(f"  Similarity threshold: {rag_service.similarity_threshold}")
    print(f"  Max documents: {rag_service.max_documents}")
    print(f"  Embedding provider: {rag_service.default_embedding_provider}")
    print("-" * 50)
    
    # Test with different thresholds
    thresholds = [0.0, 0.3, 0.5, 0.7, 0.9]
    
    for threshold in thresholds:
        print(f"\nTesting with threshold: {threshold}")
        
        try:
            documents = await rag_service.search_relevant_documents(
                query=query,
                collection_name=organization_slug,
                threshold=threshold,
                limit=10
            )
            
            print(f"  Found {len(documents)} documents")
            
            for i, doc in enumerate(documents[:3]):
                print(f"    {i+1}. '{doc['title']}' (score: {doc['score']:.3f})")
                print(f"       Content preview: {doc['content'][:100]}...")
                
        except Exception as e:
            print(f"  Error: {str(e)}")
    
    # Test direct vector DB access
    print("\n" + "="*50)
    print("Testing direct vector database access")
    print("="*50)
    
    try:
        vector_db = VectorDBUtility()
        
        # Get embedding for the query
        from langchain_openai import OpenAIEmbeddings
        embeddings = OpenAIEmbeddings()
        query_embedding = await embeddings.aembed_query(query)
        
        print(f"Generated embedding vector of length: {len(query_embedding)}")
        
        # Search directly
        results = await vector_db.similarity_query(
            collection_name=organization_slug,
            query_vector=query_embedding,
            limit=10,
            score_threshold=0.0
        )
        
        print(f"Direct search found {len(results)} total documents")
        
        if results:
            print("\nTop 5 results:")
            for i, result in enumerate(results[:5]):
                score = result.get('score', 0.0)
                payload = result.get('payload', {})
                title = payload.get('title', 'No title')
                content = payload.get('content', 'No content')
                
                print(f"  {i+1}. Score: {score:.3f}")
                print(f"     Title: {title}")
                print(f"     Content preview: {content[:100]}...")
                print()
        
        # Check collection info
        await vector_db.close()
        
    except Exception as e:
        print(f"Direct vector DB test failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Close RAG service
    await rag_service.close()


async def test_query_variations():
    """Test different query variations for ThinkPad E16 G1."""
    
    organization_slug = "ipearl-88b5f0b2"
    queries = [
        "ThinkPad E16 G1 cover",
        "ThinkPad E16 G1",
        "E16 G1 cover",
        "Lenovo ThinkPad E16",
        "laptop cover ThinkPad",
        "cover for ThinkPad E16 G1"
    ]
    
    rag_service = RAGService()
    
    print("Testing query variations:")
    print("="*50)
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        
        try:
            documents = await rag_service.search_relevant_documents(
                query=query,
                collection_name=organization_slug,
                threshold=0.5,  # Use a more lenient threshold
                limit=5
            )
            
            print(f"  Found {len(documents)} documents")
            if documents:
                best = documents[0]
                print(f"  Best match: '{best['title']}' (score: {best['score']:.3f})")
                
        except Exception as e:
            print(f"  Error: {str(e)}")
    
    await rag_service.close()


if __name__ == "__main__":
    print("RAG Search Debug Tool")
    print("=" * 50)
    
    # Run the main test
    asyncio.run(test_rag_search())
    
    print("\n" + "="*50)
    print("Testing query variations")
    print("="*50)
    
    # Run query variations test
    asyncio.run(test_query_variations())