from django.urls import path
from main.service.views import (
    AuthView,
    OrganizationCreateView,
    OrganizationListView,
    OrganizationDetailView,
    OrganizationUpdateView,
    ChatThreadCreateView,
    ChatThreadUpdateView,
    ChatThreadDetailView,
    ChatThreadListView,
    ChatMessageListView,
    ChatView,
    ChatViewEnhanced,
    ChatViewGraph,
    ChatWithImageView,
    UnifiedChatView,
    DocumentView,
    DocumentEmbeddingUpdateView,
    UploadLinkView
)

urlpatterns = [
    # Authentication endpoint
    path('v1/auth', AuthView.as_view(), name='auth'),
    
    # Organization endpoints
    path('v1/organization/new', OrganizationCreateView.as_view(), name='organization-create'),
    path('v1/organizations', OrganizationListView.as_view(), name='organization-list'),
    path('v1/organization/<str:slug>', OrganizationDetailView.as_view(), name='organization-detail'),
    path('v1/organization/<str:slug>/update', OrganizationUpdateView.as_view(), name='organization-update'),
    
    # Document endpoints
    path('v1/organization/<str:slug>/document', DocumentView.as_view(), name='document-operations'),
    path('v1/organization/<str:slug>/upload_link', UploadLinkView.as_view(), name='upload-link'),
    path('v1/organization/<str:slug>/update-embeddings', DocumentEmbeddingUpdateView.as_view(), name='document-embeddings-update'),
    
    # Chat thread endpoints
    path('v1/organization/<str:slug>/thread/new', ChatThreadCreateView.as_view(), name='chat-thread-create'),
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/update', ChatThreadUpdateView.as_view(), name='chat-thread-update'),
    path('v1/organization/<str:slug>/thread/<str:thread_slug>', ChatThreadDetailView.as_view(), name='chat-thread-detail'),
    path('v1/organization/<str:slug>/chats', ChatThreadListView.as_view(), name='chat-thread-list'),
    
    # Chat message endpoints
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chats', ChatMessageListView.as_view(), name='chat-message-list'),
    
    # Unified chat endpoint (NEW - combines all chat capabilities)
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chatunified', UnifiedChatView.as_view(), name='chat-unified'),
    
    # Legacy chat endpoints (maintained for backward compatibility)
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chat', ChatView.as_view(), name='chat'),
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chatenhanced', ChatViewEnhanced.as_view(), name='chat-enhanced'),
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chatgraph', ChatViewGraph.as_view(), name='chat-graph'),
    path('v1/organization/<str:slug>/thread/<str:thread_slug>/chatwithimage', ChatWithImageView.as_view(), name='chat-with-image'),
] 
