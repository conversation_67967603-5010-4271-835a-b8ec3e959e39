"""
Web Scraping Service for URL-based document ingestion.

This service provides comprehensive web crawling capabilities with:
- Configurable depth control and page limits
- Robots.txt compliance and respect for crawl delays
- URL normalization and deduplication
- HTML to Markdown conversion with GitHub Flavored Markdown support
- Integration with existing Document model and vector embedding pipeline
"""

import asyncio
import logging
import re
import hashlib
from typing import Dict, List, Optional, Set, Any, Tuple
from urllib.parse import urljoin, urlparse, urlunparse, parse_qs, urlencode
from urllib.robotparser import RobotFileParser
import urllib.request
from dataclasses import dataclass
from datetime import datetime
import time

import aiohttp
from bs4 import BeautifulSoup, Comment
from markdownify import markdownify as md

logger = logging.getLogger(__name__)


@dataclass
class CrawlConfiguration:
    """Configuration for web crawling session."""
    max_depth: int = 2
    max_pages: int = 50
    crawl_delay: float = 1.0
    follow_external_links: bool = False
    respect_robots_txt: bool = True
    include_pattern: Optional[str] = None
    exclude_pattern: Optional[str] = None
    user_agent: str = "AIDA-Bot/1.0"
    session_id: Optional[str] = None
    max_page_size: int = 1024 * 1024  # 1MB per page
    request_timeout: int = 30
    total_timeout: int = 300  # 5 minutes max crawl time


@dataclass
class CrawlResult:
    """Result of a single page crawl."""
    url: str
    title: str
    content: str
    content_hash: str
    file_size: int
    success: bool
    error_message: Optional[str] = None
    processing_time: float = 0.0
    original_html_content: Optional[str] = None  # Store original HTML for link extraction


@dataclass
class CrawlStatistics:
    """Statistics for a crawling session."""
    pages_crawled: int = 0
    pages_successful: int = 0
    pages_failed: int = 0
    pages_skipped: int = 0
    total_size_bytes: int = 0
    processing_time_seconds: float = 0.0
    crawl_session_id: Optional[str] = None


class URLNormalizer:
    """Utility for URL normalization and deduplication."""
    
    @staticmethod
    def normalize_url(url: str) -> str:
        """
        Normalize URL by removing fragments, sorting query parameters, etc.
        
        Args:
            url: Raw URL to normalize
            
        Returns:
            Normalized URL string
        """
        parsed = urlparse(url)
        
        # Remove fragment
        parsed = parsed._replace(fragment='')
        
        # Sort query parameters for consistent comparison
        if parsed.query:
            query_params = parse_qs(parsed.query, keep_blank_values=True)
            sorted_params = sorted(query_params.items())
            normalized_query = urlencode(sorted_params, doseq=True)
            parsed = parsed._replace(query=normalized_query)
        
        # Ensure path ends with / for directory URLs
        path = parsed.path
        if not path or path == '':
            path = '/'
        elif path.endswith('/') and len(path) > 1:
            # Remove trailing slash except for root
            path = path.rstrip('/')
        
        parsed = parsed._replace(path=path)
        
        return urlunparse(parsed)
    
    @staticmethod
    def is_same_domain(url1: str, url2: str) -> bool:
        """Check if two URLs belong to the same domain."""
        domain1 = urlparse(url1).netloc.lower()
        domain2 = urlparse(url2).netloc.lower()
        return domain1 == domain2
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Validate URL format and security."""
        try:
            parsed = urlparse(url)
            
            # Must have scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Only allow http/https
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Block internal/private IP ranges
            hostname = parsed.hostname
            if hostname:
                # Block localhost, private IPs
                if hostname in ['localhost', '127.0.0.1', '0.0.0.0']:
                    return False
                
                # Block private IP ranges (simplified check)
                if hostname.startswith(('10.', '172.', '192.168.')):
                    return False
            
            return True
            
        except Exception:
            return False


class RobotsTxtChecker:
    """Utility for checking robots.txt compliance."""
    
    def __init__(self, user_agent: str = "AIDA-Bot/1.0"):
        self.user_agent = user_agent
        self._robots_cache: Dict[str, RobotFileParser] = {}
    
    async def can_fetch(self, url: str, session: aiohttp.ClientSession) -> bool:
        """
        Check if the URL can be fetched according to robots.txt.
        
        Args:
            url: URL to check
            session: aiohttp session for fetching robots.txt
            
        Returns:
            True if URL can be fetched, False if blocked by robots.txt
        """
        try:
            parsed = urlparse(url)
            base_url = f"{parsed.scheme}://{parsed.netloc}"
            robots_url = urljoin(base_url, '/robots.txt')
            
            # Check cache first
            if base_url not in self._robots_cache:
                await self._fetch_robots_txt(robots_url, session, base_url)
            
            rp = self._robots_cache.get(base_url)
            if rp:
                return rp.can_fetch(self.user_agent, url)
            
            # If no robots.txt or error, allow by default
            return True
            
        except Exception as e:
            logger.warning(f"Error checking robots.txt for {url}: {e}")
            return True
    
    async def _fetch_robots_txt(self, robots_url: str, session: aiohttp.ClientSession, base_url: str):
        """Fetch and parse robots.txt file."""
        try:
            async with session.get(robots_url, timeout=10) as response:
                if response.status == 200:
                    robots_content = await response.text()
                    
                    rp = RobotFileParser()
                    rp.set_url(robots_url)
                    rp.read_response(robots_content.splitlines())
                    self._robots_cache[base_url] = rp
                    logger.info(f"Loaded robots.txt for {base_url}")
                else:
                    # No robots.txt or error - allow all
                    rp = RobotFileParser()
                    rp.read_response([])  # Empty robots.txt allows everything
                    self._robots_cache[base_url] = rp
                    
        except Exception as e:
            logger.warning(f"Could not fetch robots.txt from {robots_url}: {e}")
            # Default to allow all if robots.txt is inaccessible
            rp = RobotFileParser()
            rp.read_response([])
            self._robots_cache[base_url] = rp


class MarkdownConverter:
    """Utility for converting HTML to GitHub Flavored Markdown."""
    
    @staticmethod
    def html_to_markdown(html_content: str, base_url: str) -> str:
        """
        Convert HTML content to clean GitHub Flavored Markdown.
        
        Args:
            html_content: Raw HTML content
            base_url: Base URL for resolving relative links
            
        Returns:
            Clean Markdown content
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove unwanted elements
            MarkdownConverter._clean_html(soup)
            
            # Convert relative URLs to absolute
            MarkdownConverter._resolve_links(soup, base_url)
            
            # Convert to markdown with custom options
            markdown_content = md(
                str(soup),
                heading_style="ATX",  # Use # style headers
                bullets="-",          # Use - for bullets
                code_language="",     # Don't specify language unless detected
                escape_misc=False,    # Don't escape markdown characters
                convert=["p", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ul", "ol", 
                        "a", "img", "strong", "em", "code", "pre", "blockquote", 
                        "table", "thead", "tbody", "tr", "th", "td", "br", "hr"]
            )
            
            # Post-process markdown
            markdown_content = MarkdownConverter._clean_markdown(markdown_content)
            
            return markdown_content
            
        except Exception as e:
            logger.error(f"Error converting HTML to Markdown: {e}")
            # Fallback to plain text extraction
            soup = BeautifulSoup(html_content, 'html.parser')
            return soup.get_text(separator='\n', strip=True)
    
    @staticmethod
    def _clean_html(soup: BeautifulSoup):
        """Remove unwanted HTML elements."""
        # Remove scripts, styles, and comments
        for element in soup(["script", "style", "noscript", "meta", "link"]):
            element.decompose()
        
        # Remove HTML comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # Remove common navigation and footer elements
        for selector in [
            'nav', 'footer', 'header', '.navigation', '.nav', '.footer', 
            '.header', '.sidebar', '.menu', '.breadcrumb', '.pagination',
            '.social-media', '.advertisement', '.ads', '[role="banner"]',
            '[role="navigation"]', '[role="contentinfo"]'
        ]:
            for element in soup.select(selector):
                element.decompose()
    
    @staticmethod
    def _resolve_links(soup: BeautifulSoup, base_url: str):
        """Convert relative URLs to absolute URLs."""
        for tag in soup.find_all(['a', 'img']):
            attr = 'href' if tag.name == 'a' else 'src'
            if tag.has_attr(attr):
                url = tag[attr]
                if url and not url.startswith(('http://', 'https://', 'mailto:', 'tel:')):
                    tag[attr] = urljoin(base_url, url)
    
    @staticmethod
    def _clean_markdown(content: str) -> str:
        """Clean up markdown content."""
        # Remove excessive blank lines
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # Clean up list formatting
        content = re.sub(r'\n\s*-\s*\n', '\n', content)
        
        # Remove leading/trailing whitespace
        content = content.strip()
        
        return content


class WebScrapingService:
    """
    Service for crawling web pages and converting them to documents.
    
    Provides comprehensive web scraping with respect for robots.txt,
    URL deduplication, and integration with the existing document pipeline.
    """
    
    def __init__(self):
        """Initialize the web scraping service."""
        self.url_normalizer = URLNormalizer()
        self.markdown_converter = MarkdownConverter()
        self._session: Optional[aiohttp.ClientSession] = None
        self._robots_checker: Optional[RobotsTxtChecker] = None
    
    async def scrape_url(
        self, 
        start_url: str, 
        organization_slug: str, 
        crawl_subpages: bool = False,
        config: Optional[CrawlConfiguration] = None
    ) -> Tuple[List[CrawlResult], CrawlStatistics, List[Dict[str, str]]]:
        """
        Scrape a URL and optionally its subpages.
        
        Args:
            start_url: Starting URL to scrape
            organization_slug: Organization slug for document storage
            crawl_subpages: Whether to crawl linked pages
            config: Crawl configuration options
            
        Returns:
            Tuple of (successful_results, statistics, errors)
        """
        if not config:
            config = CrawlConfiguration()
        
        # Validate and normalize start URL
        if not self.url_normalizer.is_valid_url(start_url):
            error = {"url": start_url, "error": "Invalid URL format"}
            stats = CrawlStatistics()
            return [], stats, [error]
        
        normalized_start_url = self.url_normalizer.normalize_url(start_url)
        
        # Initialize session and robots checker
        await self._initialize_session(config)
        
        # Track crawling state
        visited_urls: Set[str] = set()
        content_hashes: Set[str] = set()
        url_queue: List[Tuple[str, int]] = [(normalized_start_url, 0)]  # (url, depth)
        successful_results: List[CrawlResult] = []
        errors: List[Dict[str, str]] = []
        start_time = time.time()
        
        try:
            while url_queue and len(successful_results) < config.max_pages:
                # Check total timeout
                if time.time() - start_time > config.total_timeout:
                    logger.warning("Total crawl timeout reached")
                    break
                
                current_url, depth = url_queue.pop(0)
                
                # Skip if already visited
                if current_url in visited_urls:
                    continue
                
                visited_urls.add(current_url)
                
                # Check robots.txt compliance
                if config.respect_robots_txt:
                    can_fetch = await self._robots_checker.can_fetch(current_url, self._session)
                    if not can_fetch:
                        errors.append({
                            "url": current_url,
                            "error": "Blocked by robots.txt"
                        })
                        continue
                
                # Apply URL pattern filters
                if not self._matches_url_patterns(current_url, config):
                    continue
                
                # Crawl the page
                result = await self._crawl_single_page(current_url, config)
                
                if result.success:
                    # Check for duplicate content
                    if result.content_hash not in content_hashes:
                        content_hashes.add(result.content_hash)
                        successful_results.append(result)
                        
                        # Extract links for further crawling
                        if crawl_subpages and depth < config.max_depth:
                            # Use the original HTML content stored in the result
                            links = await self._extract_links(result.original_html_content, current_url, config)
                            for link in links:
                                if link not in visited_urls:
                                    url_queue.append((link, depth + 1))
                    else:
                        logger.info(f"Skipping duplicate content: {current_url}")
                else:
                    errors.append({
                        "url": current_url,
                        "error": result.error_message or "Unknown error"
                    })
                
                # Respect crawl delay
                if config.crawl_delay > 0:
                    await asyncio.sleep(config.crawl_delay)
            
            # Calculate statistics
            total_time = time.time() - start_time
            statistics = CrawlStatistics(
                pages_crawled=len(visited_urls),
                pages_successful=len(successful_results),
                pages_failed=len(errors),
                pages_skipped=len(visited_urls) - len(successful_results) - len(errors),
                total_size_bytes=sum(result.file_size for result in successful_results),
                processing_time_seconds=round(total_time, 2),
                crawl_session_id=config.session_id
            )
            
            return successful_results, statistics, errors
            
        finally:
            await self._cleanup_session()
    
    async def _initialize_session(self, config: CrawlConfiguration):
        """Initialize HTTP session and robots checker."""
        connector = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            keepalive_timeout=30
        )
        
        headers = {
            'User-Agent': config.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive'
        }
        
        self._session = aiohttp.ClientSession(
            connector=connector,
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=config.request_timeout)
        )
        
        self._robots_checker = RobotsTxtChecker(config.user_agent)
    
    async def _cleanup_session(self):
        """Clean up HTTP session."""
        if self._session:
            await self._session.close()
            self._session = None
        self._robots_checker = None
    
    def _matches_url_patterns(self, url: str, config: CrawlConfiguration) -> bool:
        """Check if URL matches include/exclude patterns."""
        if config.exclude_pattern:
            if re.search(config.exclude_pattern, url, re.IGNORECASE):
                return False
        
        if config.include_pattern:
            if not re.search(config.include_pattern, url, re.IGNORECASE):
                return False
        
        return True
    
    async def _crawl_single_page(self, url: str, config: CrawlConfiguration) -> CrawlResult:
        """
        Crawl a single page and convert to markdown.
        
        Args:
            url: URL to crawl
            config: Crawl configuration
            
        Returns:
            CrawlResult with page content or error
        """
        start_time = time.time()
        
        try:
            async with self._session.get(url) as response:
                if response.status != 200:
                    return CrawlResult(
                        url=url,
                        title="",
                        content="",
                        content_hash="",
                        file_size=0,
                        success=False,
                        error_message=f"HTTP {response.status}",
                        processing_time=time.time() - start_time
                    )
                
                # Check content size
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > config.max_page_size:
                    return CrawlResult(
                        url=url,
                        title="",
                        content="",
                        content_hash="",
                        file_size=0,
                        success=False,
                        error_message=f"Page too large (exceeds {config.max_page_size} bytes)",
                        processing_time=time.time() - start_time
                    )
                
                # Read content with proper encoding handling
                try:
                    # Try to get content with automatic encoding detection
                    html_content = await response.text()
                except UnicodeDecodeError:
                    # Fallback to reading raw bytes and handling encoding manually
                    raw_content = await response.read()
                    # Try different encodings
                    for encoding in ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']:
                        try:
                            html_content = raw_content.decode(encoding)
                            logger.info(f"Successfully decoded content using {encoding} for {url}")
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        # If all encodings fail, use utf-8 with error handling
                        html_content = raw_content.decode('utf-8', errors='replace')
                        logger.warning(f"Used UTF-8 with error replacement for {url}")
                except Exception as e:
                    logger.error(f"Error reading content from {url}: {e}")
                    return CrawlResult(
                        url=url,
                        title="",
                        content="",
                        content_hash="",
                        file_size=0,
                        success=False,
                        error_message=f"Failed to decode content: {str(e)}",
                        processing_time=time.time() - start_time
                    )
                
                # Check actual size
                if len(html_content.encode('utf-8')) > config.max_page_size:
                    return CrawlResult(
                        url=url,
                        title="",
                        content="",
                        content_hash="",
                        file_size=0,
                        success=False,
                        error_message=f"Page content too large (exceeds {config.max_page_size} bytes)",
                        processing_time=time.time() - start_time
                    )
                
                # Extract title
                soup = BeautifulSoup(html_content, 'html.parser')
                title = self._extract_title(soup, url)
                
                # Convert to markdown
                markdown_content = self.markdown_converter.html_to_markdown(html_content, url)
                
                # Add metadata header
                markdown_with_metadata = self._add_metadata_header(markdown_content, title, url)
                
                # Calculate content hash for deduplication
                content_hash = hashlib.md5(markdown_with_metadata.encode('utf-8')).hexdigest()
                
                return CrawlResult(
                    url=url,
                    title=title,
                    content=markdown_with_metadata,
                    content_hash=content_hash,
                    file_size=len(markdown_with_metadata.encode('utf-8')),
                    success=True,
                    processing_time=time.time() - start_time,
                    original_html_content=html_content  # Store original HTML for link extraction
                )
                
        except asyncio.TimeoutError:
            return CrawlResult(
                url=url,
                title="",
                content="",
                content_hash="",
                file_size=0,
                success=False,
                error_message="Request timeout",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return CrawlResult(
                url=url,
                title="",
                content="",
                content_hash="",
                file_size=0,
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def _extract_title(self, soup: BeautifulSoup, url: str) -> str:
        """Extract page title from HTML."""
        # Try title tag first
        title_tag = soup.find('title')
        if title_tag and title_tag.text.strip():
            return title_tag.text.strip()
        
        # Try h1 tag
        h1_tag = soup.find('h1')
        if h1_tag and h1_tag.text.strip():
            return h1_tag.text.strip()
        
        # Try meta title
        meta_title = soup.find('meta', property='og:title')
        if meta_title and meta_title.get('content'):
            return meta_title['content'].strip()
        
        # Fallback to URL path
        parsed_url = urlparse(url)
        path_parts = [part for part in parsed_url.path.split('/') if part]
        if path_parts:
            return path_parts[-1].replace('-', ' ').replace('_', ' ').title()
        
        return parsed_url.netloc
    
    def _add_metadata_header(self, content: str, title: str, url: str) -> str:
        """Add metadata header to markdown content."""
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
        
        header = f"""# {title}

**Source URL:** {url}  
**Scraped on:** {timestamp}

---

{content}"""
        
        return header
    
    async def _extract_links(self, html_content: str, base_url: str, config: CrawlConfiguration) -> List[str]:
        """
        Extract links from HTML content for further crawling.
        
        Args:
            html_content: HTML content to extract links from
            base_url: Base URL for resolving relative links
            config: Crawl configuration
            
        Returns:
            List of normalized URLs to crawl
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for link_tag in soup.find_all('a', href=True):
                href = link_tag['href']
                
                # Skip non-content links
                if href.startswith(('#', 'mailto:', 'tel:', 'javascript:')):
                    continue
                
                # Resolve relative URLs
                absolute_url = urljoin(base_url, href)
                
                # Validate URL
                if not self.url_normalizer.is_valid_url(absolute_url):
                    continue
                
                # Check domain restrictions
                if not config.follow_external_links:
                    if not self.url_normalizer.is_same_domain(absolute_url, base_url):
                        continue
                
                # Normalize and add to results
                normalized_url = self.url_normalizer.normalize_url(absolute_url)
                if normalized_url not in links:
                    links.append(normalized_url)
            
            return links
            
        except Exception as e:
            logger.error(f"Error extracting links from {base_url}: {e}")
            return []


# Export main service class
__all__ = ["WebScrapingService", "CrawlConfiguration", "CrawlResult", "CrawlStatistics"]