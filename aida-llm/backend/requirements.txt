aiohappyeyeballs==2.6.1
aiohttp==3.11.14
beautifulsoup4==4.12.3
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
argon2-cffi>=23.1.0
argon2-cffi-bindings>=21.2.0
asgiref==3.8.1
async-timeout==4.0.3
asyncclick==7.1.2.3
attrs==25.3.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
cohere==5.14.0
colored==1.4.4
colorful==0.5.6
cryptography>=42.0.0
dataclasses-json==0.6.7
distro==1.9.0
Django==5.1.7
djangorestframework==3.15.2
environs==9.3.5
exceptiongroup==1.2.2
fastavro==1.10.0
filelock==3.18.0
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
groq>=0.4.0
anthropic>=0.25.0
aioredis>=2.0.0
jsonschema>=4.17.0
grpcio==1.71.0
grpcio-tools==1.71.0
gunicorn==23.0.0
h11==0.14.0
h2==4.2.0
hiredis==3.1.0
hpack==4.1.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.3
hyperframe==6.1.0
idna==3.10
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.21
langchain-cohere==0.4.3
langchain-community==0.3.20
langchain-core==0.3.47
langchain-openai==0.3.9
langchain-text-splitters==0.3.7
langgraph==0.3.18
langgraph-checkpoint==2.0.21
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.58
langsmith==0.3.18
marshmallow==3.26.1
merge-args==0.1.5
msgpack==1.1.0
multidict==6.2.0
mypy-extensions==1.0.0
numpy==2.2.4
openai==1.68.0
orjson==3.10.15
packaging==24.2
Pillow>=9.0.0
portalocker==2.10.1
prettyprinter==0.18.0
propcache==0.3.0
protobuf==5.29.4
psycopg==3.2.6
psycopg2-binary==2.9.10
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
Pygments==2.19.1
python-dotenv==1.0.1
pytz==2025.1
PyYAML==6.0.2
qdrant-client==1.13.3
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
selenium==4.24.0
six==1.17.0
sniffio==1.3.1
sounddevice==0.5.1
SQLAlchemy==2.0.39
sqlparse==0.5.3
tenacity==9.0.0
tiktoken==0.9.0
tokenizers==0.21.1
tqdm==4.67.1
types-PyYAML==6.0.12.20241230
types-requests==2.32.0.20250306
typing-inspect==0.9.0
typing_extensions==4.12.2
urllib3==2.3.0
# uvicore removed (unused; conflicted with aioredis>=2)
yarl==1.18.3
zstandard==0.23.0

# Tesseract OCR Dependencies
pytesseract==0.3.10
opencv-python==*********

# Web Scraping Dependencies
markdownify==1.2.0

# Document Processing Dependencies
pypdf2==3.0.1          # PDF text extraction
python-docx==1.1.0     # Word document processing
openpyxl==3.1.2        # Excel file processing
pandas>=2.2.2          # Excel data manipulation (compat with NumPy 2)
chardet==5.2.0         # Character encoding detection
