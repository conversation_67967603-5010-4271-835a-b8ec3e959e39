services:
  aida-llm-postgres:
    image: postgres
    container_name: aida-llm-postgres
    ports:
      - "${AIDA_POSTGRES_PORT:-5432}:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - aida-llm-postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - private
  aida-llm-qdrant:
    image: qdrant/qdrant
    container_name: aida-llm-qdrant
    ports:
      - "${AIDA_QDRANT_HTTP_PORT:-6333}:6333"  # external communication, e.g. api/web
      - "${AIDA_QDRANT_GRPC_PORT:-6334}:6334"  # gRPC protocol, communication among qdrant cluster
    environment:
      - QDRANT__SERVICE__API_KEY=${QDRANT__SERVICE__API_KEY}
    volumes:
      - aida-llm-qdrant-data:/qdrant/storage:z
    restart: unless-stopped
    networks:
      - private
  
  aida-llm-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.backend
    image: aida-llm-backend
    container_name: aida-llm-backend
    ports:
      - "${AIDA_BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend/.env:/app/.env:ro
    depends_on:
      - aida-llm-postgres
      - aida-llm-qdrant
    restart: unless-stopped
    networks:
      - public
      - private

  aida-llm-nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile.nginx
    image: aida-llm-nginx
    container_name: aida-llm-nginx
    ports:
      - "${AIDA_NGINX_HTTPS_PORT:-443}:443"
      - "${AIDA_NGINX_HTTP_PORT:-80}:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/.sslcerts/cert.pem:/etc/nginx/certs/cert.pem:ro
      - ./nginx/.sslcerts/key.pem:/etc/nginx/certs/key.pem:ro
      - ./nginx/website:/etc/nginx/html:ro
    depends_on:
      - aida-llm-backend
    restart: unless-stopped
    networks:
      - public
  
networks:
  public:
  private:

volumes:
  aida-llm-qdrant-data:
  aida-llm-postgres-data:
