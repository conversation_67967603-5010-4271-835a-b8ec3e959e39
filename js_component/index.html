<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Demo showcase of Aida JS Component integration across different website examples">
  <title>Aida JS Component Demo</title>
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 2rem;
    }
    .demo-sites {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }
    .demo-site {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
    }
    .demo-site h2 {
      margin-top: 0;
      color: #444;
    }
    .demo-site p {
      color: #666;
      margin-bottom: 1.5rem;
    }
    .demo-site a {
      display: inline-block;
      padding: 0.5rem 1rem;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background 0.2s;
    }
    .demo-site a:hover {
      background: #0056b3;
    }
    .instructions {
      margin: 2rem 0;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 8px;
    }
    code {
      background: #eee;
      padding: 0.2rem 0.4rem;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Aida JS Component Demo</h1>
  
  <div class="instructions">
    <h2>Getting Started</h2>
    <p>
      This demo shows Aida running on different websites. The chat component is injected via JavaScript 
      and can be customized for each site.
    </p>
    <p>
      To run locally: <code>npm run dev</code>
    </p>
  </div>

  <div class="demo-sites" role="list">
    <div class="demo-site" role="listitem">
      <h2>McDonald's Demo</h2>
      <p>Example of Aida on a fast food website, helping with menu and ordering.</p>
      <a href="http://mcdonald.localhost:3300/demo_sites/mcdonalds/index.html" 
         aria-label="View McDonald's demo site with Aida integration" 
         rel="noopener">View Demo</a>
    </div>

    <div class="demo-site" role="listitem">
      <h2>RACL Demo</h2>
      <p>Aida assisting with legal information and document navigation.</p>
      <a href="http://racl.localhost:3300/demo_sites/racl/index.html"
         aria-label="View RACL demo site with Aida integration"
         rel="noopener">View Demo</a>
    </div>

    <div class="demo-site" role="listitem">
      <h2>Research Triangle AI</h2>
      <p>Aida on our main website, showcasing AI capabilities.</p>
      <a href="http://rtai.localhost:3300/demo_sites/rtai/index.html"
         aria-label="View Research Triangle AI demo site with Aida integration"
         rel="noopener">View Demo</a>
    </div>
  </div>

  <div id="aida-root"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>