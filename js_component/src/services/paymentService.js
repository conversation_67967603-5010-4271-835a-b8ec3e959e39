// Get API URL from environment variables with fallback
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

/**
 * Service for handling payment-related API calls
 */
export class PaymentService {
  constructor() {
    this.csrfToken = null;
  }

  /**
   * Get CSRF token for authentication
   */
  async getCsrfToken() {
    try {
      const response = await fetch(`${API_URL}/csrf/`, {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to get CSRF token: ${response.statusText}`);
      }

      const data = await response.json();
      this.csrfToken = data.csrfToken;
      return this.csrfToken;
    } catch (error) {
      console.error('Error getting CSRF token:', error);
      throw error;
    }
  }

  /**
   * Create a payment intent
   * @param {number} amount - The amount to charge in dollars
   * @returns {Promise<Object>} - The payment intent details
   */
  async createPaymentIntent(amount) {
    if (!this.csrfToken) {
      await this.getCsrfToken();
    }

    try {
      const response = await fetch(`${API_URL}/chat/payment/create-intent/`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken
        },
        body: JSON.stringify({ amount })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  /**
   * Process a payment method
   * @param {string} paymentMethodId - The Stripe payment method ID
   * @param {number} amount - The amount to charge in dollars
   * @param {string} returnUrl - The URL to return to after payment
   * @returns {Promise<Object>} - The payment processing result
   */
  async processPayment(paymentMethodId, amount, returnUrl = window.location.href) {
    // Validate inputs
    if (!paymentMethodId) {
      throw new Error('Payment method ID is required');
    }
    
    if (!amount || isNaN(amount) || amount <= 0) {
      console.error('Invalid amount provided to processPayment:', amount);
      throw new Error('A valid payment amount is required');
    }
    
    // Log payment request details
    console.log('Processing payment with details:', {
      paymentMethodId: paymentMethodId,
      amount: amount,
      returnUrl: returnUrl
    });

    if (!this.csrfToken) {
      await this.getCsrfToken();
    }

    try {
      // Ensure amount is a number and convert to string for JSON
      const amountValue = parseFloat(amount);
      
      const response = await fetch(`${API_URL}/chat/payment/process/`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken
        },
        body: JSON.stringify({
          paymentMethodId,
          amount: amountValue,
          returnUrl
        })
      });

      // Log the raw response for debugging
      console.log('Payment API response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Payment API error:', errorData);
        throw new Error(errorData.error || 'Failed to process payment');
      }

      const result = await response.json();
      console.log('Payment successful:', result);
      return result;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }
}

// Create a singleton instance
const paymentService = new PaymentService();
export default paymentService;
