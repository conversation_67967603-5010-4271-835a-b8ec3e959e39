const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';
const LLM_API_URL = import.meta.env.VITE_LLM_API_URL || 'http://localhost:8080/llm-api';

export class ChatService {
  constructor() {
    this.conversation_id = null;
    this.history = [];
    this.config = null;
    this.accountUuid = null;
    this.csrfToken = null;
    this.llm_slug = null;
    this.conversation_id = null;
    this.workspaceSlug = null;
    this.thread_slug = null;
    this.initialMessage = null;

    // Check for demo mode
    const scriptEl = document.currentScript || document.querySelector('script[data-aida-mode]');
    if (scriptEl?.getAttribute('data-aida-mode') === 'demo') {
      this.demoMode = true;
      this.demoBaseUrl = scriptEl.getAttribute('data-aida-baseUrl');
      this.demoAccountUuid = scriptEl.getAttribute('data-aida-account');
    }

    // Only load from session storage in production mode
    if (!this.demoMode) {
      this.loadFromSessionStorage();
    }
  }

  async getCsrfToken() {
    try {
      const response = await fetch(`${API_URL}/csrf/`, {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to get CSRF token');
      }
      const data = await response.json();
      this.csrfToken = data.csrfToken;
      return this.csrfToken;
    } catch (error) {
      console.error('CSRF token error:', error);
      throw error;
    }
  }

  async loadConfig(accountUuid) {
    if (this.demoMode && this.demoAccountUuid) {
      // In demo mode, always fetch fresh config
      console.log('Loading demo config for account:', this.demoAccountUuid);

      // Get auth token from local storage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No auth token found');
      }

      const response = await fetch(
        `${this.demoBaseUrl}/api/accounts/by-uuid/${this.demoAccountUuid}/config/`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      if (!response.ok) throw new Error('Failed to load demo config');
      const config = await response.json();
      console.log('Loaded demo config:', config);
      this.config = config;
      this.llm_slug = config.llm_slug;
      this.accountUuid = this.demoAccountUuid;
      return config;
    }

    // Production mode - use cache and local storage
    console.log('Loading production config');
    const cachedConfig = localStorage.getItem('aida_chat_config');
    if (cachedConfig) {
      const { config, timestamp } = JSON.parse(cachedConfig);
      console.log('Found cached config:', config);
      const age = Date.now() - timestamp;
      // Use cache if less than 24 hours old
      if (age < 24 * 60 * 60 * 1000) {
        this.config = config;
        this.llm_slug = config.llm_slug;
        this.accountUuid = accountUuid;
        return config;
      }
    }

    // Fetch fresh config for production
    try {
      const response = await fetch(`${API_URL}/chat/config/`, {
        headers: {
          'Referer': window.location.href
        }
      });
      if (!response.ok) {
        throw new Error('Failed to get chat configuration');
      }
      const config = await response.json();

      // Cache the config only in production mode
      localStorage.setItem('aida_chat_config', JSON.stringify({
        config,
        timestamp: Date.now()
      }));

      this.config = config;
      this.llm_slug = config.llm_slug;
      this.accountUuid = accountUuid;
      return config;
    } catch (error) {
      console.error('Failed to get chat config:', error);
      throw error;
    }
  }

  async ensureConfig() {
    if (!this.config) {
      await this.loadConfig();
    }
    return this.config;
  }

  async startNewConversation() {
    try {
      // Clear existing state
      if (!this.demoMode) {
        this.clearSessionStorage();
      }

      // Get config and initial message
      console.log('Starting new conversation in mode:', this.demoMode ? 'demo' : 'production');
      const config = await this.loadConfig();
      console.log('Using config for thread creation:', config);

      // Request a new thread from the LLM backend
      // Use aida-llm directly for thread creation
      const url = this.demoMode ? `${this.demoBaseUrl}/api/chat/thread/` : `${LLM_API_URL}/v1/organization/${config.llm_slug}/thread/new`;
      console.log('Creating thread at URL:', url);

      const threadPayload = this.demoMode ? {
        workspace_slug: config.llm_slug
      } : {
        name: 'Chat Session'
      };
      console.log('Thread creation payload:', threadPayload);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(threadPayload)
      });

      if (!response.ok) {
        throw new Error('Failed to create chat session');
      }

      const data = await response.json();
      console.log('Thread creation response:', data);
      
      if (this.demoMode) {
        // Demo mode - use existing logic
        if (!data.thread_id) {
          console.error('No thread_id returned from API');
          throw new Error('Failed to get a valid thread ID from the server');
        }
        this.conversation_id = data.thread_id;
      } else {
        // LLM API mode - use aida-llm response format
        if (!data.chatThread || !data.chatThread.threadSlug) {
          console.error('No thread slug returned from LLM API');
          throw new Error('Failed to get a valid thread slug from the LLM server');
        }
        this.conversation_id = data.chatThread.threadSlug;
        this.thread_slug = data.chatThread.threadSlug;
      }
      
      this.workspaceSlug = config.llm_slug;
      console.log('Using conversation_id:', this.conversation_id);

      // Save this ID to session storage immediately
      this.saveToSessionStorage();

      // Add initial message
      const initialMessage = config.initial_message || "Hello! How can I help you today?";
      this.addMessage('assistant', initialMessage);

      // Only save to session storage in production mode
      if (!this.demoMode) {
        this.saveToSessionStorage();
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
      throw error;
    }
  }

  async sendMessage(message, image = null) {
    try {
      // Always ensure fresh config in demo mode
      if (this.demoMode) {
        await this.loadConfig();
      }

      if (!this.conversation_id) {
        await this.startNewConversation();
      }

      if (this.demoMode) {
        // Demo mode doesn't support image uploads yet
        if (image) {
          throw new Error('Image uploads not supported in demo mode');
        }
        
        var url = `${this.demoBaseUrl}/api/chat/message/`;
        var payload = {
          thread_id: this.conversation_id,
          message: message,
          llm_slug: this.config.llm_slug
        };
        var body = JSON.stringify(payload);
        var headers = {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken
        };
      } else {
        // Use LLM API directly for messaging (supports images)
        var url = `${LLM_API_URL}/v1/organization/${this.config.llm_slug}/thread/${this.conversation_id}/chatunified`;
        
        if (image) {
          // Use FormData for image uploads
          const formData = new FormData();
          if (message) {
            formData.append('message', message);
          }
          formData.append('image', image);
          formData.append('mode', 'auto');
          
          var body = formData;
          var headers = {}; // Don't set Content-Type for FormData, browser will set it with boundary
        } else {
          // Use JSON for text-only messages
          var payload = {
            message: message,
            mode: 'auto'
          };
          var body = JSON.stringify(payload);
          var headers = {
            'Content-Type': 'application/json'
          };
        }
      }
      
      console.log('Request URL:', url);
      if (image) {
        console.log('Request includes image file:', image.name, image.type, image.size);
      }
      console.log('Request message:', message);

      // Ensure we have a CSRF token for demo mode requests
      if (!this.csrfToken && this.demoMode) {
        await this.getCsrfToken();
      }

      const response = await fetch(url, {
        method: 'POST',
        credentials: this.demoMode ? 'include' : 'omit',
        headers: headers,
        body: body,
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      console.log('Message response:', data);
      
      let responseText;
      let metrics = null;
      let laptopInfo = null;
      let products = null;
      let ocrConfidence = null;
      
      if (this.demoMode) {
        // Original backend response format
        if (!this.conversation_id && data.id) {
          console.log('Setting conversation_id from message response:', data.id);
          this.conversation_id = data.id;
        }
        responseText = data.textResponse;
        metrics = data.metrics;
      } else {
        // LLM API response format (chatunified)
        responseText = data.textResponse;
        metrics = data.metadata;
        laptopInfo = data.laptop_info;
        products = data.products;
        ocrConfidence = data.ocr_confidence;
      }
      
      const assistantMessage = {
        role: 'assistant',
        message: responseText,
        metrics: metrics
      };
      
      // Add image-specific data if available
      if (laptopInfo) {
        assistantMessage.laptop_info = laptopInfo;
      }
      if (products) {
        assistantMessage.products = products;
      }
      if (ocrConfidence) {
        assistantMessage.ocr_confidence = ocrConfidence;
      }
      
      this.history.push(assistantMessage);
      this.saveToSessionStorage();

      return {
        response: responseText,
        history: this.history,
        metrics: metrics,
        laptop_info: laptopInfo,
        products: products,
        ocr_confidence: ocrConfidence
      };
    } catch (error) {
      console.error('Chat error:', error);
      throw error;
    }
  }

  async getConversation() {
    if (!this.conversation_id) return null;

    try {
      const response = await fetch(`${API_URL}/chat/${this.conversation_id}/`);
      if (!response.ok) {
        throw new Error('Failed to get conversation');
      }

      const data = await response.json();
      this.history = data.history;
      this.saveToSessionStorage();
      return data.history;
    } catch (error) {
      console.error('Get conversation error:', error);
      throw error;
    }
  }

  addMessage(role, message) {
    this.history.push({
      role: role === 'bot' ? 'assistant' : role,
      message: message
    });
    this.saveToSessionStorage();
  }

  getMessage(index) {
    return this.history[index];
  }

  getAllMessages() {
    return this.history || [];
  }

  clearHistory() {
    this.history = [];
    this.saveToSessionStorage();
  }

  clearSessionStorage() {
    sessionStorage.removeItem('aida_chat_state');
    this.conversation_id = null;
    this.history = [];
    this.csrfToken = null;
    this.workspaceSlug = null;
    this.thread_slug = null;
  }

  saveToSessionStorage() {
    const state = {
      conversation_id: this.conversation_id,
      history: this.history,
      config: this.config,
      llm_slug: this.llm_slug,
      workspaceSlug: this.workspaceSlug,
      thread_slug: this.thread_slug
    };
    sessionStorage.setItem('aida_chat_state', JSON.stringify(state));
  }

  loadFromSessionStorage() {
    const savedState = sessionStorage.getItem('aida_chat_state');
    if (savedState) {
      const state = JSON.parse(savedState);
      this.conversation_id = state.conversation_id;
      this.history = state.history;
      this.config = state.config;
      this.llm_slug = state.llm_slug;
      this.conversation_id = state.conversation_id;
      this.workspaceSlug = state.workspaceSlug;
      this.thread_slug = state.thread_slug;
      return true;
    }
    return false;
  }

  async submitOrder(data) {
    try {
      console.log('ChatService - submitOrder received data:', JSON.stringify(data, null, 2));

      // Ensure we have a CSRF token
      if (!this.csrfToken) {
        await this.getCsrfToken();
      }

      // Prepare the order data with all the new fields
      console.log('ChatService - Original phone number from data:', data.phoneNumber);

      // Ensure phone number is properly formatted and not null
      let phoneNumber = data.phoneNumber ? data.phoneNumber.replace(/\D/g, '') : '';
      console.log('ChatService - Formatted phone number:', phoneNumber);

      // Make sure phone number is a string with at least 7 digits
      if (!phoneNumber || phoneNumber.length < 7) {
        console.error('ChatService - Phone number is invalid after formatting');
        throw new Error('Please enter a valid phone number with at least 7 digits');
      }

      const orderData = {
        phoneNumber: phoneNumber, // Changed from phone_number to phoneNumber to match backend expectation
        first_name: data.firstName,
        last_name: data.lastName,
        delivery_method: data.deliveryMethod,
        payment_method: data.paymentMethod,
        messages: data.messages || [],
        conversation_id: this.conversation_id || null
      };

      console.log('ChatService - Final order data with phone_number:', JSON.stringify(orderData, null, 2));

      // Add delivery information if delivery is selected
      if (data.deliveryMethod === 'delivery') {
        orderData.delivery_address_line1 = data.deliveryAddressLine1;
        orderData.delivery_address_line2 = data.deliveryAddressLine2;
        orderData.delivery_city = data.deliveryCity;
        orderData.delivery_state = data.deliveryState;
        orderData.delivery_zip_code = data.deliveryZipCode;
        orderData.delivery_charge = data.deliveryCharge;
        orderData.tip_amount = data.tipAmount;
      }

      // Add payment information if pay now is selected
      if (data.paymentMethod === 'pay_now') {
        orderData.payment_status = 'pending';
        // In a real implementation, we would include payment intent ID from Stripe
        // orderData.payment_intent_id = data.paymentIntentId;
      }

      console.log('Submitting order with data:', orderData);

      // Double-check that phoneNumber is properly included in the request
      if (!orderData.phoneNumber) {
        console.error('ChatService - phoneNumber is missing from orderData');
        throw new Error('Phone number is missing from order data');
      }

      // Log the actual request body being sent
      const requestBody = JSON.stringify(orderData);
      console.log('ChatService - Sending request body:', requestBody);

      const response = await fetch(
        `${API_URL}/chat/submit-order/`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.csrfToken,
            'Referer': window.location.href
          },
          body: requestBody
        }
      );

      console.log('ChatService - Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to submit order');
      }

      return await response.json();
    } catch (error) {
      console.error('Submit order error:', error);
      throw error;
    }
  }

  async emailChatHistory(data) {
    try {
      const response = await fetch(
        `${API_URL}/chat/email_to_user`,  // Remove trailing slash and 'api' prefix
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Referer': window.location.href
          },
          body: JSON.stringify(data)
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to send email');
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending chat history email:', error);
      throw error;
    }
  }
}

// Create a singleton instance to ensure consistent conversation_id across components
const chatService = new ChatService();
export default chatService;
