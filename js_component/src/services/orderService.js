// Order service for handling order-related API calls
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

export class OrderService {
  constructor() {
    this.conversation_id = null;
    this.csrfToken = null;
  }

  /**
   * Set the conversation ID for this service instance
   * @param {string} conversation_id - The ID of the current conversation
   */
  setConversationId(conversation_id) {
    console.log('OrderService: Setting conversation_id:', conversation_id);
    this.conversation_id = conversation_id;
  }

  /**
   * Get CSRF token for authentication
   * @returns {Promise<string>} The CSRF token
   */
  async getCsrfToken() {
    try {
      const response = await fetch(`${API_URL}/csrf/`, {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Failed to get CSRF token');
      }
      const data = await response.json();
      this.csrfToken = data.csrfToken;
      return this.csrfToken;
    } catch (error) {
      console.error('CSRF token error:', error);
      throw error;
    }
  }

  /**
   * Get the current order for a conversation
   * @returns {Promise<Object>} The order data
   */
  async getOrder() {
    try {
      if (!this.conversation_id) {
        console.error('OrderService: No conversation_id available when getting order');
        throw new Error('No conversation ID available');
      }
      console.log('OrderService: Getting order with conversation_id:', this.conversation_id);

      // Ensure we have a CSRF token
      if (!this.csrfToken) {
        await this.getCsrfToken();
      }

      const response = await fetch(`${API_URL}/chat/order/?conversation_id=${this.conversation_id}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken,
          'Referer': window.location.href
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch order: ${response.statusText}`);
      }

      const orderData = await response.json();
      return orderData;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  /**
   * Submit an order for processing
   * @param {Object} orderData - The order data to submit
   * @returns {Promise<Object>} The response from the server
   */
  async submitOrder(orderData) {
    try {
      // Ensure we have a CSRF token
      if (!this.csrfToken) {
        await this.getCsrfToken();
      }
      if (!this.conversation_id) {
        console.error('OrderService: No conversation_id available when submitting order');
        throw new Error('No conversation ID available');
      }
      console.log('OrderService: Submitting order with conversation_id:', this.conversation_id);

      const response = await fetch(`${API_URL}/chat/submit-order/`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken,
          'Referer': window.location.href
        },
        body: JSON.stringify({
          ...orderData,
          conversation_id: this.conversation_id
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to submit order: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error submitting order:', error);
      throw error;
    }
  }
}

// Create a singleton instance
const orderService = new OrderService();
export default orderService;
