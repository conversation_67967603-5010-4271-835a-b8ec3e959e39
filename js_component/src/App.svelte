<script>
  import ModernChat from './components/ModernChat.svelte';
  import { Bot } from 'lucide-svelte';
  import { fade } from 'svelte/transition';
  import { onMount } from 'svelte';
  import { ChatService } from './services/chatService';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  export let showChat = false;
  let configLoaded = false;
  let chatService = new ChatService();
  let container;
  let chatComponent;
  let mounted = false;
  let theme = 'light';
  let config = null;

  onMount(async () => {
    try {
      // Load config once and store it for use throughout the component
      config = await chatService.loadConfig();
      configLoaded = true;

      // Set the theme from the loaded config
      if (config && config.theme) {
        theme = config.theme;
        console.log('Applied theme from config:', theme);
      }

      // Only create UI after config is loaded
      if (configLoaded) {
        // Create shadow root on the container element
        const shadowRoot = container.attachShadow({ mode: 'open' });
        container.id = 'aida-shadow-host';

        // Create style element
        const style = document.createElement('style');
        style.textContent = `
          /* Base styles */
          :host {
            all: initial;
          }

          * {
            box-sizing: border-box;
            margin: 0;
            /* padding: 0; */
          }

          /* Theme Variables - Light (default) */
          :host {
            --chat-bg: #ffffff;
            --chat-text: #333333;
            --header-bg: #4a90e2;
            --header-text: #ffffff;
            --message-user-bg: #2563eb;
            --message-user-text: #ffffff;
            --message-bot-bg: #f3f4f6;
            --message-bot-text: #374151;
            --chat-container-bg: #f9fafb;
            --chat-container-border: #eee;
            --input-bg: #ffffff;
            --input-text: #333333;
            --input-border: #e5e7eb;
            --button-bg: #2563eb;
            --button-text: #ffffff;
            --shadow: rgba(0, 0, 0, 0.1);
          }

          /* Dark Theme Variables */
          .dark-theme {
            --chat-bg: #1f2937;
            --chat-text: #f9fafb;
            --header-bg: #111827;
            --header-text: #f9fafb;
            --message-user-bg: #3b82f6;
            --message-user-text: #ffffff;
            --message-bot-bg: #374151;
            --message-bot-text: #f3f4f6;
            --chat-container-bg: #1f2937;
            --chat-container-border: #888;
            --input-bg: #374151;
            --input-text: #f9fafb;
            --input-border: #4b5563;
            --button-bg: #3b82f6;
            --button-text: #ffffff;
            --shadow: rgba(0, 0, 0, 0.3);
          }

          /* Mount point styles */
          #aida-mount-point {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
          }

          /* Chat toggle button */
          .chat-toggle-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgb(59 130 246);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            transition: all 0.2s;
            z-index: 9999;
          }

          .chat-toggle-button:hover {
            background-color: rgb(37 99 235);
          }

          /* Utility classes */
          .w-6 {
            width: 24px;
          }

          .h-6 {
            height: 24px;
          }

          /* Chat window styles */
          .chat-window {
            width: 400px;
            max-width: calc(100vw - 40px);
            background-color: var(--chat-bg);
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 600px;
            box-shadow: 0 4px 6px -1px var(--shadow);
            transition: all 0.3s ease;
            position: fixed;
            bottom: 20px;
            right: 20px;
            color: var(--chat-text);
          }

          .chat-window.maximized {
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0;
            z-index: 99999;
          }

          .chat-header {
            padding: 16px;
            border-bottom: 1px solid var(--input-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--header-bg);
            color: var(--header-text);
          }

          .header-content {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .chat-icon {
            width: 24px;
            height: 24px;
            object-fit: contain;
          }

          .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--header-text);
            margin: 0;
          }

          .header-actions {
            display: flex;
            gap: 8px;
          }

          .header-button {
            padding: 4px;
            border-radius: 6px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: background-color 0.2s;
            color: var(--header-text);
          }

          .header-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }

          .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            background-color: var(--chat-container-bg);
            border: 1px var(--chat-container-border) solid;
          }

          .message {
            display: flex;
            justify-content: flex-start;
          }

          .message.user {
            justify-content: flex-end;
          }

          .message-content {
            max-width: 90%;
            padding: 12px;
            border-radius: 8px;
            background-color: var(--message-bot-bg);
            color: var(--message-bot-text);
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          }

          .message.user .message-content {
            background-color: var(--message-user-bg);
            color: var(--message-user-text);
          }

          .input-form {
            padding: 16px;
            border-top: 1px solid var(--input-border);
            display: flex;
            background-color: var(--chat-bg);
            border: 1px var(--chat-container-border) solid;
            border-top: unset;
          }

          .input-container {
            position: relative;
            flex: 1;
          }

          .input-container:hover .input-menu {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-100%);
          }

          .message-input {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--input-border);
            border-radius: 6px;
            outline: none;
            transition: all 0.2s;
            background-color: var(--input-bg);
            color: var(--input-text);
            position: relative;
            width: 100%;
            font-size: 14px;
            line-height: 20px;
          }

          .message-input:focus {
            border-color: rgb(59 130 246);
            box-shadow: 0 0 0 2px rgb(59 130 246 / 0.1);
          }

          .message-input:disabled {
            background-color: rgb(243 244 246);
            cursor: not-allowed;
          }

          .input-menu {
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%) translateY(-100%);
            background-color: var(--chat-bg);
            border: 1px solid var(--input-border);
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px var(--shadow);
            padding: 8px;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 10;
          }

          .send-button {
            padding: 8px;
            background-color: rgb(59 130 246);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .send-button:hover:not(:disabled) {
            background-color: rgb(37 99 235);
          }

          .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .send-button .icon {
            color: white;
          }

          /* Message content table styles */
          .message-content table {
            width: 100%;
            margin: 16px 0;
            border-collapse: collapse;
            font-size: 14px;
            line-height: 20px;
          }

          .message-content th {
            background-color: rgb(249 250 251);
            font-weight: 600;
            text-align: left;
            padding: 12px;
            border: 1px solid rgb(229 231 235);
          }

          .message-content td {
            padding: 12px;
            border: 1px solid rgb(229 231 235);
          }

          .message-content tr:nth-child(even) {
            background-color: rgb(249 250 251);
          }

          .message-content tr:hover {
            background-color: rgb(243 244 246);
          }

          /* Icons */
          .icon {
            width: 16px;
            height: 16px;
            color: rgb(107 114 128);
          }

          .icon.rotate-180 {
            transform: rotate(180deg);
          }

          /* Voice input and language select styles */
          .voice-button {
            padding: 4px;
            background: transparent;
            border: 0;
            border-radius: 6px;
            cursor: pointer;
            color: var(--primary-color, #007bff);
            transition: color 0.2s;
            position: relative;
          }

          .voice-button:hover {
            color: var(--primary-color-dark, #0056b3);
          }

          .voice-button.listening {
            color: #dc3545;
            animation: pulse 1.5s infinite;
          }

          .language-select {
            padding: 4px;
            border: 1px solid var(--input-border);
            border-radius: 6px;
            background-color: var(--input-bg);
            color: var(--input-text);
            font-size: 14px;
            margin-left: 0.5em;
          }

          .language-select:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .menu-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            color: var(--chat-text);
            font-size: 14px;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s;
            width: 100%;
            text-align: left;
            border: none;
            background: none;
          }

          .menu-item:hover {
            background-color: var(--message-bot-bg);
          }

          .menu-item:focus {
            outline: 2px solid rgb(59 130 246);
            outline-offset: -2px;
          }

          .menu-item svg {
            width: 16px;
            height: 16px;
          }

          /* Modal styles */
          .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999999;
            inset: 0;
            backdrop-filter: blur(2px);
          }

          .modal-content {
            background: var(--chat-bg);
            color: var(--chat-text);
            border-radius: 8px;
            max-width: 90%;
            width: 400px;
            position: relative;
            z-index: 1000000;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            font-family: system-ui, -apple-system, sans-serif;
          }

          /* Make modal wider on non-mobile devices */
          @media (min-width: 768px) {
            .modal-content {
              width: 600px;
            }
          }

          .modal-content h2 {
            color: var(--chat-text);
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
          }

          .modal-content p {
            color: var(--chat-text);
            font-size: 14px;
            margin-bottom: 16px;
            opacity: 0.8;
          }

          /* Input and button styles for modals */
          .phone-input,
          input[type="email"],
          input[type="tel"] {
            width: 100%;
            padding: 12px 16px;
            background-color: var(--input-bg);
            color: var(--input-text);
            border: 1px solid var(--input-border);
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 16px;
            transition: border-color 0.15s ease-in-out;
          }

          .phone-input:focus,
          input[type="email"]:focus,
          input[type="tel"]:focus {
            outline: none;
            border-color: var(--button-bg);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
          }

          .button-group {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
          }

          .button-group button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s ease-in-out;
            cursor: pointer;
          }

          .button-group button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .button-group .cancel {
            background: var(--message-bot-bg);
            color: var(--chat-text);
            border: 1px solid var(--input-border);
          }

          .button-group .submit {
            background: var(--button-bg);
            color: var(--button-text);
            border: 1px solid transparent;
          }

          .order-modal h3,
          .email-modal h3 {
            color: var(--chat-text);
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
          }

          .order-modal p,
          .email-modal p {
            color: var(--chat-text);
            opacity: 0.8;
          }

          /* Order form styles */
          .order-modal {
            max-width: 600px;
            width: 100%;
            padding: 16px;
            border-radius: 12px;
            background-color: var(--chat-bg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            max-height: 85vh;
            overflow-y: auto;
            animation: modalFadeIn 0.3s ease-out;
            margin: 0 auto;
          }

          /* Order summary styles */
          .order-summary {
            margin-bottom: 24px;
            background-color: var(--chat-container-bg);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }

          .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
          }

          .order-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
          }

          .order-status.pending {
            background-color: var(--chat-warning, #fb8c00);
            color: white;
          }

          .order-status.confirmed {
            background-color: var(--chat-success, #43a047);
            color: white;
          }

          .order-status.canceled {
            background-color: var(--chat-error, #e53935);
            color: white;
          }

          .order-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--chat-timestamp, #70757a);
            margin-bottom: 16px;
          }

          .order-id {
            font-weight: 500;
          }

          .summary-text {
            font-style: italic;
            margin-bottom: 16px;
            opacity: 0.8;
          }

          .order-items {
            margin-bottom: 16px;
          }

          .order-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--input-border);
          }

          .item-details {
            display: flex;
            gap: 8px;
            padding-right: 10px;
          }

          .item-name {
            font-weight: 500;
          }

          .item-quantity {
            opacity: 0.7;
          }

          .order-totals {
            margin-top: 16px;
            padding-top: 8px;
          }

          .total-line {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
          }

          .grand-total {
            font-weight: bold;
            font-size: 1.1em;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid var(--input-border);
          }

          .loading-indicator {
            text-align: center;
            padding: 16px;
            font-style: italic;
            opacity: 0.7;
          }

          @keyframes modalFadeIn {
            from {
              opacity: 0;
              transform: translateY(10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .order-modal h3 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: 700;
            color: var(--chat-text);
            text-align: center;
          }

          .order-modal p {
            margin-bottom: 24px;
            text-align: center;
            color: var(--chat-text);
            opacity: 0.8;
          }

          .form-section {
            padding: 16px;
            border-radius: 8px;
            background-color: var(--chat-container-bg);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            animation: sectionFadeIn 0.5s ease-out;
            animation-fill-mode: both;
          }

          @keyframes sectionFadeIn {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          /* Add staggered animation delay for sections */
          .form-section:nth-child(1) { animation-delay: 0.1s; }
          .form-section:nth-child(2) { animation-delay: 0.2s; }
          .form-section:nth-child(3) { animation-delay: 0.3s; }
          .form-section:nth-child(4) { animation-delay: 0.4s; }
          .form-section:nth-child(5) { animation-delay: 0.5s; }

          .form-section:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            transform: translateY(-2px);
          }

          .form-section h4 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 18px;
            font-weight: 600;
            color: var(--chat-text);
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .form-section h4::before {
            content: '';
            display: block;
            width: 4px;
            height: 18px;
            background-color: var(--button-bg);
            border-radius: 2px;
          }

          .form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
          }

          .form-group {
            margin-bottom: 12px;
            width: 100%;
          }

          .form-group label, .fee-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--chat-text);
          }

          .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1.5px solid var(--input-border);
            border-radius: 6px;
            background-color: var(--input-bg);
            color: var(--input-text);
            font-size: 15px;
            transition: all 0.2s ease;
          }

          .form-input:focus {
            outline: none;
            border-color: var(--button-bg);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
          }

          .form-input::placeholder {
            color: var(--chat-text);
            opacity: 0.5;
          }

          .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
          }

          .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1.5px solid var(--input-border);
            background-color: var(--input-bg);
            font-size: 14px;
            font-weight: 500;
            color: var(--input-text);
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .radio-label:hover {
            border-color: var(--button-bg);
            background-color: rgba(37, 99, 235, 0.1);
          }

          .radio-label input[type="radio"] {
            margin: 0;
            accent-color: var(--button-bg);
          }

          .radio-label input[type="radio"]:checked + span {
            color: var(--button-bg);
            font-weight: 600;
          }

          .tip-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
          }

          .tip-button {
            padding: 8px 12px;
            border: 1.5px solid var(--input-border);
            border-radius: 6px;
            background-color: var(--input-bg);
            color: var(--input-text);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
          }

          .tip-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
          }

          .tip-button:focus:not(:active)::after {
            animation: ripple 1s ease-out;
          }

          @keyframes ripple {
            0% {
              transform: scale(0, 0);
              opacity: 0.5;
            }
            20% {
              transform: scale(25, 25);
              opacity: 0.3;
            }
            100% {
              opacity: 0;
              transform: scale(40, 40);
            }
          }

          .tip-button:hover {
            border-color: var(--button-bg);
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--button-bg);
          }

          .tip-button:active {
            background-color: var(--button-bg);
            color: var(--button-text);
          }

          .credit-card-form {
            margin-top: 16px;
            padding: 16px;
            border-radius: 6px;
            border: 1.5px solid var(--input-border);
            background-color: var(--input-bg);
          }

          .card-element {
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            border: 1.5px dashed var(--input-border);
            padding: 12px;
            background-color: var(--chat-container-bg);
          }

          .placeholder-text {
            color: var(--chat-text);
            opacity: 0.5;
            font-style: italic;
            font-size: 14px;
            text-align: center;
          }

          .fee-label {
            font-weight: 600;
            color: var(--chat-text);
            font-size: 16px;
            margin-top: 8px;
          }

          .button-group {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
          }

          .cancel, .submit {
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
            text-align: center;
          }

          .cancel {
            background-color: transparent;
            border: 1.5px solid var(--input-border);
            color: var(--chat-text);
            opacity: 0.8;
          }

          .submit {
            background-color: var(--button-bg);
            border: 1.5px solid var(--button-bg);
            color: var(--button-text);
            min-width: 150px;
          }

          .cancel:hover {
            background-color: var(--chat-container-bg);
            color: var(--chat-text);
            opacity: 1;
          }

          .submit:hover {
            background-color: rgb(37, 99, 235);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .submit:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          .submit:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
          }

          .error {
            color: #dc2626;
            font-size: 14px;
            margin-top: 12px;
            padding: 8px 12px;
            background-color: rgba(220, 38, 38, 0.1);
            border-radius: 6px;
            border-left: 3px solid #dc2626;
          }

          /* Responsive adjustments */
          @media (max-width: 480px) {
            .form-row {
              flex-direction: column;
              gap: 8px;
            }

            .radio-group {
              flex-direction: column;
            }

            .button-group {
              flex-direction: column-reverse;
              align-items: center;
            }

            .cancel, .submit {
              width: 80%;
              max-width: 300px;
            }
          }

          /* Desktop optimizations */
          @media (min-width: 768px) {
            .order-modal {
              max-width: 1000px;
              margin: 0 auto;
            }

            .form-section h4 {
              font-size: 20px;
              margin-bottom: 12px;
            }

            .form-section h4::before {
              height: 20px;
              width: 5px;
            }

            .form-row {
              display: flex;
              flex-direction: row;
              gap: 16px;
              margin-bottom: 16px;
            }

            .form-group label {
              margin-bottom: 8px;
              font-size: 15px;
            }

            .form-input {
              padding: 12px 16px;
              font-size: 16px;
            }

            .radio-group {
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              gap: 20px;
              margin-bottom: 20px;
            }

            .radio-label {
              flex: 0 0 auto;
              padding: 10px 16px;
              font-size: 15px;
            }

            .tip-buttons {
              gap: 12px;
            }

            .tip-button {
              padding: 10px 16px;
              font-size: 15px;
            }

            .button-group {
              display: flex;
              flex-direction: row;
              justify-content: center;
              margin-top: 32px;
              gap: 24px;
            }

            .cancel, .submit {
              padding: 14px 28px;
              font-size: 16px;
            }

            .submit {
              min-width: 180px;
            }
          }

          /* Two-row input layout styles - Override existing input-form styles */
          .input-form {
            display: flex !important;
            flex-direction: column !important;
            width: 100% !important;
            padding: 16px;
            border-top: 1px solid var(--input-border);
            background-color: var(--chat-bg);
            border: 1px var(--chat-container-border) solid;
            border-top: unset;
          }
          
          .input-row {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-bottom: 8px;
            width: 100%;
          }

          .message-input {
            flex: 1;
          }

          .send-button {
            background-color: var(--chat-primary, #007bff);
            border: none;
            border-radius: 6px;
            padding: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.2s ease;
            min-width: 40px;
            height: 40px;
          }

          .send-button:hover:not(:disabled) {
            background-color: var(--chat-primary-dark, #0056b3);
          }

          .send-button:disabled {
            background-color: var(--chat-disabled, #ccc);
            cursor: not-allowed;
          }

          .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            width: 100%;
            flex-shrink: 0;
          }

          .left-controls {
            display: flex;
            gap: 8px;
          }

          .right-controls {
            display: flex;
            gap: 8px;
          }

          .control-button {
            background: none;
            border: 1px solid var(--input-border, #ddd);
            border-radius: 6px;
            padding: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--chat-text-secondary, #666);
            transition: all 0.2s ease;
            min-width: 36px;
            height: 36px;
          }

          .control-button:hover {
            background-color: var(--chat-hover, #f5f5f5);
            color: var(--chat-text, #333);
            border-color: var(--chat-primary, #007bff);
          }

          .control-button.listening {
            color: #ff4444;
            background-color: rgba(255, 68, 68, 0.1);
            border-color: #ff4444;
          }

          .input-menu {
            display: flex;
            gap: 8px;
            padding-top: 8px;
            border-top: 1px solid var(--input-border, #ddd);
            flex-wrap: wrap;
          }

          /* Image preview styles */
          .image-preview-container {
            position: relative;
            margin-bottom: 8px;
            border: 2px solid var(--chat-primary, #007bff);
            border-radius: 6px;
            overflow: hidden;
            max-width: 200px;
          }
          
          .image-preview {
            width: 100%;
            height: auto;
            max-height: 120px;
            object-fit: cover;
            display: block;
          }
          
          .remove-image-button {
            position: absolute;
            top: 4px;
            right: 4px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          
          .remove-image-button:hover {
            background-color: rgba(0, 0, 0, 0.9);
          }

          /* Chat message image styles */
          .message-image {
            margin-bottom: 8px;
          }
          
          .chat-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 6px;
            border: 1px solid var(--input-border, #ddd);
            object-fit: cover;
          }
          
          .laptop-info {
            margin-top: 8px;
            padding: 8px;
            background-color: var(--chat-info-bg, #f0f8ff);
            border-radius: 4px;
            font-size: 14px;
            border-left: 3px solid var(--chat-primary, #007bff);
          }
          
          .confidence {
            color: var(--chat-text-secondary, #666);
            font-weight: normal;
            font-size: 13px;
          }
          
          .products-list {
            margin-top: 8px;
            padding: 8px;
            background-color: var(--chat-success-bg, #f0fff4);
            border-radius: 4px;
            font-size: 14px;
            border-left: 3px solid var(--chat-success, #28a745);
          }
          
          .product-item {
            margin: 4px 0;
            font-size: 13px;
          }
        `;
        shadowRoot.appendChild(style);

        // Create mount point
        const mountPoint = document.createElement('div');
        mountPoint.id = 'aida-mount-point';
        shadowRoot.appendChild(mountPoint);

        // Apply theme class to the mount point
        if (theme === 'dark') {
          mountPoint.classList.add('dark-theme');
        }

        // Create new component instance with the theme
        chatComponent = new ModernChat({
          target: mountPoint,
          props: {
            show: showChat,
            theme: theme,
            config: config // Pass the full config to the ModernChat component
          }
        });

        // Listen for close events from the chat component
        chatComponent.$on('close', () => {
          showChat = false;
          dispatch('chatStateChange', { showChat });
        });

        mounted = true;
      }
    } catch (error) {
      console.error('Failed to load chat config:', error);
    }
  });

  function toggleChat() {
    showChat = !showChat;
    dispatch('chatStateChange', { showChat });
  }

  $: if (chatComponent && mounted) {
    chatComponent.$set({
      show: showChat,
      theme: theme
    });
    dispatch('chatStateChange', { showChat });
  }
</script>

<div bind:this={container}>
  {#if configLoaded && mounted && !showChat}
    <button
      class="chat-toggle-button"
      on:click={toggleChat}
      aria-label="Open chat"
      in:fade={{ duration: 200 }}
    >
      <Bot size={24} />
    </button>
  {/if}
</div>

<style>
  /* Global variables for reference only */
  /* These are applied in the shadow DOM */
  :root {
    /* Light theme reference */
    --light-bg: #ffffff;
    --light-text: #333333;
    --light-primary: #4a90e2;
  }


</style>
