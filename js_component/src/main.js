import App from './App.svelte';
import { ChatService } from './services/chatService';
import { basePath } from './utils/scriptUtils';

// Get the script element that loaded this script
const currentScript = document.currentScript;
const mode = currentScript?.getAttribute('data-aida-mode') || 'production';
const accountUuid = currentScript?.getAttribute('data-aida-account');

// Wrap all initialization in IIFE to prevent multiple loads
(() => {
  // Check if component is already loaded
  if (window.__AIDA_LOADED__) {
    console.warn('Aida component is already loaded');
    return;
  }

  // Initialize chat service
  const chatService = new ChatService();

  // Check for existing chat state
  const chatWindowOpen = sessionStorage.getItem('aida_chat_window_open') === 'true';

  // Create chat icon container (hidden initially)
  const chatIconContainer = document.createElement('div');
  chatIconContainer.id = 'aida-chat-icon';
  chatIconContainer.style.display = 'none';
  chatIconContainer.style.position = 'fixed';
  chatIconContainer.style.cursor = 'pointer';
  chatIconContainer.style.zIndex = '9998';
  document.body.appendChild(chatIconContainer);

  let chatIcon;

  // Initialize Svelte app
  chatService.loadConfig().then(config => {
    if (config.chat_icon) {
      chatIcon = basePath + config.chat_icon;
    }

    // Set icon position from config
    chatIconContainer.style.bottom = config.chat_position?.bottom || '20px';
    chatIconContainer.style.right = config.chat_position?.right || '20px';

    // Set icon image
    chatIconContainer.innerHTML = `
      <img src="${basePath + (config.chat_icon || '/images/chat.png')}"
           alt="Chat Icon"
           width="48"
           height="48" />
    `;

    // Show icon
    chatIconContainer.style.display = 'block';

    // Add click handler
    chatIconContainer.addEventListener('click', () => {
      app.$set({ showChat: true });
      sessionStorage.setItem('aida_chat_window_open', 'true');
    });

    // Create app instance
    const app = new App({
      target: document.body,
      props: {
        showChat: chatWindowOpen,
        mode: mode,
        accountUuid: accountUuid
      }
    });

    // Watch for chat window state changes
    app.$on('chatStateChange', (event) => {
      console.log('Main: chatStateChange event received', event.detail);
      const isOpen = event.detail.showChat;
      sessionStorage.setItem('aida_chat_window_open', isOpen.toString());
      console.log('Main: setting app showChat to:', isOpen);
      app.$set({ showChat: isOpen });
    });

    app.$on('close', () => {
      console.log('Main: close event received');
      console.log('Main: setting app showChat to false');
      app.$set({ showChat: false });
      sessionStorage.setItem('aida_chat_window_open', 'false');
    });

  }).catch(error => {
    console.error('Failed to load chat config:', error);

    // Create app with default icon if config fails
    const app = new App({
      target: document.body,
      props: {
        showChat: chatWindowOpen,
        mode: mode,
        accountUuid: accountUuid
      }
    });

    // Watch for chat window state changes (in error case)
    app.$on('chatStateChange', (event) => {
      console.log('Main: chatStateChange event received', event.detail);
      const isOpen = event.detail.showChat;
      sessionStorage.setItem('aida_chat_window_open', isOpen.toString());
      console.log('Main: setting app showChat to:', isOpen);
      app.$set({ showChat: isOpen });
    });

    app.$on('close', () => {
      console.log('Main: close event received');
      console.log('Main: setting app showChat to false');
      app.$set({ showChat: false });
      sessionStorage.setItem('aida_chat_window_open', 'false');
    });
  });

  // Set loaded flag
  window.__AIDA_LOADED__ = true;
})();

// Export mode for use in other components
export const AIDA_MODE = mode;
export const AIDA_ACCOUNT = accountUuid;

// You can use mode to modify behavior
if (mode === 'demo') {
  console.log('AIDA running in demo mode');
  // Add demo-specific initialization here
}
