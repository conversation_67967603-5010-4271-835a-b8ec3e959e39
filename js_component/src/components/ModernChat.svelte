<script>
  import { onMount, createEventDispatcher } from 'svelte';
  import chatService from '../services/chatService';
  import orderService from '../services/orderService.js';
  import paymentService from '../services/paymentService.js';
  import { fade } from 'svelte/transition';
  import { X, Maximize2, Minimize2 } from 'lucide-svelte';
  import { marked } from 'marked';
  import { basePath } from '../utils/scriptUtils';
  import Modal from './Modal.svelte';
  import StripePaymentForm from './StripePaymentForm.svelte';

  const dispatch = createEventDispatcher();

  export let show = false;
  export let theme = 'light'; // Default theme
  // export let mode = 'production';

  let config;
  let messages = [];
  let newMessage = '';
  let scrollArea;
  let lastMessageRef;  // Add this to track the latest message
  let isMaximized = false;
  let loading = false;
  let error = null;
  // Using the singleton chatService imported above
  let configLoaded = false;
  let chatIcon = null;
  let chatTitle = 'AiDA'; // Default title that will be overridden by config if available
  let recognition;
  let isListening = false;
  let currentLanguage = 'en-US';  // Default language
  let showOrderModal = false;
  let phoneNumber = '';
  let firstName = '';
  let lastName = '';
  let submittingOrder = false;
  let orderError = null;
  let deliveryMethod = 'pickup';
  let paymentMethod = 'pay_later';
  let order = null;
  let loadingOrder = false;
  let conversation_id = null; // Add conversation ID variable
  let deliveryAddressLine1 = '';
  let deliveryAddressLine2 = '';
  let deliveryCity = '';
  let deliveryState = '';
  let deliveryZipCode = '';
  let deliveryCharge = 5.00; // Default delivery charge
  let tipAmount = 0.00;
  let showEmailModal = false;
  let stripePaymentForm; // Reference to the Stripe payment form component
  let paymentMethodId = null; // To store the payment method ID from Stripe
  let stripeFormError = null; // To store any Stripe form errors

  // Handle successful payment method creation
  function handlePaymentSuccess(event) {
    const paymentResult = event.detail;
    console.log(`Payment method created with ID: ${paymentResult.paymentMethodId}`);
    console.log(`Card: ${paymentResult.brand} ending in ${paymentResult.last4}`);

    // Store the payment method ID and details
    paymentMethodId = paymentResult.paymentMethodId;

    // Update the UI to show the selected payment method
    selectedPaymentMethod = {
      id: paymentMethodId,
      brand: paymentResult.brand,
      last4: paymentResult.last4,
      name: paymentResult.billingDetails.name,
      postalCode: paymentResult.billingDetails.postalCode
    };

    // Clear any previous errors
    stripeFormError = null;
    orderError = null;

    // We don't automatically submit the order anymore
    // The user will click the Submit Order button which handles everything
  }
  let selectedPaymentMethod = null; // Will store card brand, last4, name, and postal code
  let emailAddress = '';
  let sendingEmail = false;
  
  // Image upload variables
  let selectedImage = null;
  let imagePreview = null;
  let fileInput = null;
  let emailError = null;

  // Camera variables
  let showCameraModal = false;
  let videoElement = null;
  let canvasElement = null;
  let stream = null;
  let cameraError = null;

  // Language options
  const languages = [
    { code: 'en-US', name: 'English' },
    { code: 'zh-CN', name: '中文' },
    { code: 'es-ES', name: 'Español' },
  ];

  onMount(async () => {
    try {
      // Load config first
      config = await chatService.loadConfig();
      configLoaded = true;
      chatIcon = basePath + (config.chat_icon || '/images/chat.png');
      
      // Load chat title from config if available
      if (config.chat_title) {
        chatTitle = config.chat_title;
        console.log(`Loaded chat title from config: ${chatTitle}`);
      }

      // Initialize orderService with CSRF token
      await orderService.getCsrfToken();

      // Set the theme from the config
      if (config.theme) {
        theme = config.theme;
        console.log(`Loaded theme from config: ${theme}`);
      }

      // Then restore messages if needed
      if (show && sessionStorage.getItem('aida_chat_window_open') === 'true') {
        const existingHistory = chatService.getAllMessages();
        if (existingHistory && existingHistory.length > 0) {
          messages = existingHistory.map((msg, index) => ({
            id: index + 1,
            text: msg.message,
            sender: msg.role === 'assistant' ? 'bot' : 'user'
          }));
        }
      }

      // if (mode === 'demo') {
      //   console.log('Chat running in demo mode');
      //   // Add demo-specific initialization
      //   messages = [
      //     {
      //       role: 'assistant',
      //       content: 'Welcome to the AIDA demo! Feel free to ask me any questions about your organization.'
      //     }
      //   ];
      // }

      // Check if browser supports speech recognition
      if ('webkitSpeechRecognition' in window) {
        recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = currentLanguage;

        recognition.onresult = (event) => {
          const transcript = event.results[0][0].transcript;
          newMessage = transcript;
          handleSubmit(); // Automatically send the transcribed message
        };

        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          isListening = false;
        };

        recognition.onend = () => {
          isListening = false;
        };
      }

      initSpeechRecognition();
    } catch (error) {
      console.error('Failed to load chat config:', error);
    }
  });

  // Watch for show changes
  $: if (show && configLoaded) {
    handleChatOpen();
  }

  async function handleChatOpen() {
    // Start new chat if:
    // 1. Chat window wasn't open, or
    // 2. No existing messages
    const wasOpen = sessionStorage.getItem('aida_chat_window_open') === 'true';
    const hasMessages = chatService.getAllMessages().length > 0;

    if (!wasOpen || !hasMessages) {
      await startNewChat();
    }

    // Update chat window state
    sessionStorage.setItem('aida_chat_window_open', 'true');
    dispatch('chatStateChange', { showChat: true });
    // Scroll to bottom after chat opens
    setTimeout(() => {
      if (scrollArea) {
        scrollArea.scrollTop = scrollArea.scrollHeight;
      }
    }, 100);
  }

  async function startNewChat() {
    try {
      // Start a new conversation which will add the greeting message internally
      await chatService.startNewConversation();

      // Get all messages including the greeting
      const history = chatService.getAllMessages();
      console.log('Chat history after starting new conversation:', history);

      // Map the messages to the format used by the UI
      messages = history.map((msg, index) => ({
        id: index + 1,
        text: msg.message,
        sender: msg.role === 'assistant' ? 'bot' : 'user'
      }));

      console.log('UI messages after mapping:', messages);

      // Scroll to bottom after loading history
      setTimeout(() => {
        if (scrollArea) {
          scrollArea.scrollTop = scrollArea.scrollHeight;
        }
      }, 100);
    } catch (error) {
      console.error('Error starting new chat:', error);
      error = 'Failed to initialize chat. Please try again.';
    }
  }

  function handleMaximize() {
    isMaximized = !isMaximized;
    if (scrollArea) {
      setTimeout(() => {
        scrollArea.scrollTop = scrollArea.scrollHeight;
      }, 300); // Wait for transition to complete
    }
  }

  function scrollToMessage(smooth = true) {
    if (lastMessageRef) {
      lastMessageRef.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end'
      });
    }
  }

  async function handleSubmit() {
    if ((!newMessage.trim() && !selectedImage) || loading) return;

    loading = true;
    error = null;
    const userMessage = newMessage;
    const imageToSend = selectedImage;
    newMessage = '';

    // Add user message to UI
    const userMessageObj = {
      id: messages.length + 1,
      text: userMessage || (imageToSend ? '[Image uploaded]' : ''),
      sender: 'user'
    };
    
    // Add image preview to message if image is selected
    if (imageToSend && imagePreview) {
      userMessageObj.image = imagePreview;
    }
    
    messages = [...messages, userMessageObj];
    
    // Clear image selection after adding to messages
    clearSelectedImage();
    
    // Scroll to user's message
    setTimeout(scrollToMessage, 100);

    // Add user message to chat service
    chatService.addMessage('user', userMessage || '[Image uploaded]');

    try {
      // For image-only uploads, provide a default prompt to guide the AI
      const messageToSend = userMessage || (imageToSend ? 'Please analyze this image and tell me what you see.' : '');
      const result = await chatService.sendMessage(messageToSend, imageToSend);

      // Add bot message to UI
      const botMessage = {
        id: messages.length + 1,
        text: result.response,
        sender: 'bot'
      };
      
      // Add any OCR or product data if available
      if (result.laptop_info) {
        botMessage.laptop_info = result.laptop_info;
      }
      if (result.products) {
        botMessage.products = result.products;
      }
      if (result.ocr_confidence) {
        botMessage.ocr_confidence = result.ocr_confidence;
      }
      
      messages = [...messages, botMessage];

      // Bot message is already added to chat service by sendMessage
    } catch (err) {
      error = 'Failed to send message. Please try again.';
      console.error('Chat error:', err);
    } finally {
      loading = false;
      // Scroll to bot's response
      setTimeout(scrollToMessage, 100);
    }
  }

  // Image upload functions
  function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        selectedImage = file;
        
        // Create image preview
        const reader = new FileReader();
        reader.onload = (e) => {
          imagePreview = e.target.result;
        };
        reader.readAsDataURL(file);
      } else {
        alert('Please select an image file.');
        event.target.value = '';
      }
    }
  }
  
  function clearSelectedImage() {
    selectedImage = null;
    imagePreview = null;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  // Camera functions
  async function openCamera() {
    try {
      showCameraModal = true;
      cameraError = null;
      
      // Request camera access
      stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment', // Use back camera by default
          width: { ideal: 1280 },
          height: { ideal: 720 }
        } 
      });
      
      // Wait for video element to be available
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (videoElement) {
        videoElement.srcObject = stream;
        videoElement.play();
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      cameraError = error.name === 'NotAllowedError' 
        ? 'Camera access denied. Please allow camera permissions and try again.'
        : error.name === 'NotFoundError'
        ? 'No camera found on this device.'
        : 'Error accessing camera: ' + error.message;
      showCameraModal = false;
    }
  }

  function capturePhoto() {
    if (!videoElement || !canvasElement) return;
    
    const context = canvasElement.getContext('2d');
    const videoWidth = videoElement.videoWidth;
    const videoHeight = videoElement.videoHeight;
    
    // Set canvas dimensions to match video
    canvasElement.width = videoWidth;
    canvasElement.height = videoHeight;
    
    // Draw current video frame to canvas
    context.drawImage(videoElement, 0, 0, videoWidth, videoHeight);
    
    // Convert canvas to blob
    canvasElement.toBlob((blob) => {
      if (blob) {
        // Create a File object from the blob
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([blob], `camera-photo-${timestamp}.jpg`, { type: 'image/jpeg' });
        
        selectedImage = file;
        
        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          imagePreview = e.target.result;
        };
        reader.readAsDataURL(file);
        
        // Close camera
        closeCamera();
      }
    }, 'image/jpeg', 0.8);
  }

  function closeCamera() {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      stream = null;
    }
    showCameraModal = false;
    cameraError = null;
  }

  function switchCamera() {
    if (!stream) return;
    
    closeCamera();
    
    // Try to switch between front and back camera
    setTimeout(async () => {
      try {
        const currentFacingMode = stream?.getVideoTracks()[0]?.getSettings()?.facingMode || 'environment';
        const newFacingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
        
        stream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            facingMode: newFacingMode,
            width: { ideal: 1280 },
            height: { ideal: 720 }
          } 
        });
        
        if (videoElement) {
          videoElement.srcObject = stream;
          videoElement.play();
        }
      } catch (error) {
        console.error('Error switching camera:', error);
        cameraError = 'Error switching camera: ' + error.message;
      }
    }, 100);
  }
  
  function handleClose() {
    console.log('ModernChat: handleClose called');
    messages = [];
    console.log('ModernChat: clearing session storage');
    chatService.clearSessionStorage();
    sessionStorage.removeItem('aida_chat_window_open');
    console.log('ModernChat: dispatching chatStateChange with showChat: false');
    dispatch('chatStateChange', { showChat: false });
    console.log('ModernChat: dispatching close event');
    dispatch('close');
  }

  function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  }

  function initSpeechRecognition() {
    if ('webkitSpeechRecognition' in window) {
      recognition = new webkitSpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = currentLanguage;

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        newMessage = transcript;
        handleSubmit();
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        isListening = false;
      };

      recognition.onend = () => {
        isListening = false;
      };
    }
  }

  function setLanguage(langCode) {
    currentLanguage = langCode;
    if (recognition) {
      recognition.lang = langCode;
    }
  }

  function toggleVoiceInput() {
    if (!recognition) {
      console.warn('Speech recognition not supported');
      return;
    }

    if (isListening) {
      recognition.stop();
    } else {
      recognition.lang = currentLanguage;
      isListening = true;
      recognition.start();
    }
  }

  function handleAction(action) {
    switch(action) {
      case 'send-order':
        showOrderModal = true;
        fetchOrder();
        break;
      case 'chat-history':
        showEmailModal = true;
        break;
    }
  }

  async function fetchOrder() {
    try {
      loadingOrder = true;
      // Get the conversation ID from the chat service
      conversation_id = chatService.conversation_id;
      console.log('Using conversation_id:', conversation_id);

      // Make sure we have a conversation ID
      if (!conversation_id) {
        console.error('No conversation_id available in chatService');
        throw new Error('No conversation ID available');
      }

      // Set the conversation ID in the order service
      orderService.setConversationId(conversation_id);

      // Fetch the order using the service
      order = await orderService.getOrder();
      console.log('Fetched order:', order);
    } catch (error) {
      console.error('Error fetching order:', error);
      orderError = 'Failed to load order details. Please try again.';
    } finally {
      loadingOrder = false;
    }
  }

  $: messageContent = (message) => {
    if (message.sender === 'bot') {
      // return DOMPurify.sanitize(marked(message.text));
      return marked(message.text);
    }
    return message.text;
  };

  function calculateTotal() {
    // Start with the full order total from the order data
    let total = 0;

    // If we have order data with a total, use that as the base amount
    if (order && order.total !== undefined) {
      // Use the full order.total which includes all items, tax, etc.
      total = order.total;
      console.log('Using order total from summary:', total);
    } else if (order && order.subtotal !== undefined && order.tax !== undefined) {
      // Fallback to subtotal + tax if total isn't available
      total = order.subtotal + order.tax;
      console.log('Fallback: Using subtotal + tax as base amount:', total);
    } else {
      console.warn('No order data available, using 0 as base amount');
    }

    // Add delivery charge if delivery is selected and not already included in the order total
    // Only add these if they're not already part of the order object
    if (deliveryMethod === 'delivery' && (!order || !order.delivery_charge)) {
      const deliveryChargeAmount = parseFloat(deliveryCharge) || 0;
      total += deliveryChargeAmount;
      console.log('Added delivery charge:', deliveryChargeAmount);

      // Only add tip if it's not already included in the order total
      if (!order || !order.tip_amount) {
        const tipAmountValue = parseFloat(tipAmount) || 0;
        total += tipAmountValue;
        console.log('Added tip amount:', tipAmountValue);
      }
    }

    console.log('Final total amount for payment:', total);
    return total;
  }

  function setTipPercentage(percentage) {
    // Calculate tip based on delivery charge
    const baseAmount = parseFloat(deliveryCharge) || 0;
    tipAmount = (baseAmount * (percentage / 100)).toFixed(2);
  }

  function isOrderFormValid() {
    // Basic validation
    if (!firstName || !lastName || !phoneNumber || phoneNumber.trim() === '') {
      return false;
    }

    // Ensure phone number is properly formatted (remove any non-digit characters)
    phoneNumber = phoneNumber.replace(/\D/g, '');

    // Validate delivery information if delivery is selected
    if (deliveryMethod === 'delivery') {
      if (!deliveryAddressLine1 || !deliveryCity || !deliveryState || !deliveryZipCode) {
        return false;
      }
    }

    // Validate payment information if pay now is selected
    if (paymentMethod === 'pay_now') {
      // If we have a selected payment method, it's valid
      if (selectedPaymentMethod) {
        return true;
      }

      // Otherwise, we need a valid payment form
      if (!stripePaymentForm) {
        return false;
      }

      // Check if the payment form is valid
      try {
        return stripePaymentForm.isValid();
      } catch (err) {
        console.error('Error validating payment form:', err);
        return false;
      }
    }

    return true;
  }

  async function handleSubmitOrder() {
    console.log('Submit order - Initial phone number:', phoneNumber);

    // Validate the form and show specific errors
    if (!firstName || !lastName || !phoneNumber || phoneNumber.trim() === '') {
      orderError = 'Please fill in your name and phone number';
      return;
    }

    // Format the phone number (remove any non-digit characters)
    phoneNumber = phoneNumber.replace(/\D/g, '');
    console.log('Submit order - Formatted phone number:', phoneNumber);

    // Ensure phone number is not empty after formatting and has a reasonable length
    if (!phoneNumber || phoneNumber.length < 7) {
      orderError = 'Please enter a valid phone number';
      return;
    }

    if (deliveryMethod === 'delivery') {
      if (!deliveryAddressLine1 || !deliveryCity || !deliveryState || !deliveryZipCode) {
        orderError = 'Please fill in all delivery address fields';
        return;
      }
    }

    if (paymentMethod === 'pay_now') {
      if (!selectedPaymentMethod) {
        if (!stripePaymentForm) {
          orderError = 'Payment form not initialized';
          return;
        }

        // Directly use the payment form's validation and createPaymentMethod
        try {
          // First validate the form fields
          if (!stripePaymentForm.isValid()) {
            // The validation will set cardError in the form
            orderError = 'Please check your payment information';
            return;
          }

          console.log('Payment form validated, creating payment method...');

          // Directly create the payment method
          const paymentResult = await stripePaymentForm.createPaymentMethod();

          if (!paymentResult.success) {
            // Payment method creation failed
            orderError = paymentResult.error || 'Payment processing failed';
            console.error('Payment method creation failed:', paymentResult.error);
            return;
          }

          console.log('Payment method created successfully:', paymentResult.paymentMethodId);

          // Store the payment method ID and details globally
          paymentMethodId = paymentResult.paymentMethodId;

          // Manually trigger the success handler with the payment result
          handlePaymentSuccess({
            detail: {
              paymentMethodId: paymentResult.paymentMethodId,
              brand: paymentResult.brand,
              last4: paymentResult.last4,
              billingDetails: paymentResult.billingDetails
            }
          });

          console.log('Payment method stored:', paymentMethodId);
        } catch (err) {
          console.error('Error processing payment:', err);
          orderError = err.message || 'Error processing payment';
          return;
        }
      }
    }

    submittingOrder = true;
    try {
      // Process payment if pay_now is selected
      if (paymentMethod === 'pay_now') {
        // We've already validated and created the payment method in the validation step above
        if (!selectedPaymentMethod) {
          console.error('No payment method selected');
          throw new Error('Please enter and validate your payment information');
        }

        // Double-check that we have a valid payment method ID
        if (!paymentMethodId) {
          // If selectedPaymentMethod exists, use its ID
          if (selectedPaymentMethod && selectedPaymentMethod.id) {
            paymentMethodId = selectedPaymentMethod.id;
          } else {
            console.error('No payment method ID available');
            throw new Error('Payment method not found. Please try again.');
          }
        }

        console.log(`Using payment method: ${paymentMethodId}`);

        try {
          // Calculate the total amount including base order amount, delivery charge, and tip
          const totalAmount = calculateTotal();

          // Ensure we have a valid amount
          if (!totalAmount || totalAmount <= 0) {
            throw new Error('Invalid order amount. Please try again.');
          }

          console.log('Processing payment for amount:', totalAmount, 'with payment method:', paymentMethodId);

          // Process the payment using our payment service
          const paymentProcessResult = await paymentService.processPayment(
            paymentMethodId,
            totalAmount,
            window.location.href
          );

          console.log('Payment processed:', paymentProcessResult);

          // Check if payment was successful
          if (paymentProcessResult.status !== 'succeeded') {
            throw new Error(`Payment status: ${paymentProcessResult.status}`);
          }
        } catch (paymentError) {
          console.error('Payment processing error:', paymentError);
          throw new Error(`Payment failed: ${paymentError.message}`);
        }
      }

      // Format phone number (remove any non-digit characters)
      const formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
      console.log('Submit order - Final formatted phone number:', formattedPhoneNumber);

      // Double-check phone number is valid
      if (!formattedPhoneNumber || formattedPhoneNumber.length < 7) {
        throw new Error('Please enter a valid phone number');
      }

      // Prepare order data
      const orderData = {
        phoneNumber: formattedPhoneNumber,
        firstName,
        lastName,
        deliveryMethod,
        paymentMethod,
        messages: messages.map(m => ({
          role: m.sender === 'bot' ? 'assistant' : 'user',
          text: m.text
        }))
      };

      console.log('Submitting order with data:', JSON.stringify(orderData, null, 2));

      // Add delivery information if delivery is selected
      if (deliveryMethod === 'delivery') {
        orderData.deliveryAddressLine1 = deliveryAddressLine1;
        orderData.deliveryAddressLine2 = deliveryAddressLine2;
        orderData.deliveryCity = deliveryCity;
        orderData.deliveryState = deliveryState;
        orderData.deliveryZipCode = deliveryZipCode;
        // Add delivery charge and tip to order data
        const deliveryChargeAmount = parseFloat(deliveryCharge) || 0;
        const tipAmountValue = parseFloat(tipAmount) || 0;
        orderData.deliveryCharge = deliveryChargeAmount;
        orderData.tipAmount = tipAmountValue;
        orderData.totalAmount = calculateTotal(); // Include total with delivery and tip
        orderData.tipAmount = parseFloat(tipAmount);
      }

      // Add payment information if pay now is selected
      // Always include the total amount in the order data
      orderData.totalAmount = calculateTotal();

      // Add payment information if pay now is selected
      if (paymentMethod === 'pay_now' && paymentMethodId) {
        orderData.paymentMethodId = paymentMethodId;
        orderData.paymentStatus = 'pending';
        orderData.paymentAmount = orderData.totalAmount;
      }

      const orderResponse = await chatService.submitOrder(orderData);
      console.log('Order submission response:', orderResponse);

      // Show success message with SMS notification info
      const smsNotificationSent = orderResponse?.notifications?.sms;
      const successMessage = `Order submitted successfully! ${smsNotificationSent ? 'A confirmation SMS has been sent to your phone.' : ''}`;

      // Add a bot message with order confirmation
      messages = [...messages, {
        id: messages.length + 1,
        sender: 'bot',
        text: `Thank you for your order, ${firstName}! ${successMessage}\n\nYour order details have been received and we'll process it right away.`
      }];

      // Scroll to the new message
      setTimeout(() => {
        if (scrollArea) scrollArea.scrollTop = scrollArea.scrollHeight;
      }, 100);

      showOrderModal = false;

      // Reset form fields
      phoneNumber = '';
      firstName = '';
      lastName = '';
      deliveryMethod = 'pickup';
      paymentMethod = 'pay_later';
      deliveryAddressLine1 = '';
      deliveryAddressLine2 = '';
      deliveryCity = '';
      deliveryState = '';
      deliveryZipCode = '';
      tipAmount = 0.00;
      orderError = null;

      // Show success message
      alert('Your order has been submitted successfully!');
    } catch (error) {
      orderError = error.message || 'Failed to submit order';
    } finally {
      submittingOrder = false;
    }
  }

  async function handleEmailChat() {
    if (!emailAddress) {
      emailError = 'Please enter an email address';
      return;
    }

    sendingEmail = true;
    try {
      await chatService.emailChatHistory({
        email: emailAddress,
        messages: messages.map(m => ({
          role: m.sender === 'bot' ? 'assistant' : 'user',
          text: m.text
        }))
      });
      showEmailModal = false;
      emailAddress = '';
      emailError = null;
      alert('Chat history has been sent to your email');
    } catch (error) {
      emailError = error.message || 'Failed to send email';
    } finally {
      sendingEmail = false;
    }
  }

  // Add computed class for theme-based styling
  $: containerClass = `chat-container ${theme}-theme`;
</script>



{#if show && configLoaded}
  <div
    class="chat-window {containerClass} {isMaximized ? 'maximized' : ''}"
    in:fade={{ duration: 300 }}
    out:fade={{ duration: 200 }}
  >
    <div class="chat-header">
      <div class="header-content">
        {#if chatIcon}
          <img src={chatIcon} alt="Chat Icon" class="header-icon" width="24" height="24" />
        {/if}
        <h2 class="header-title">{chatTitle}</h2>
      </div>
      <div class="header-actions">
        <button
          type="button"
          on:click={handleMaximize}
          class="header-button"
          aria-label={isMaximized ? 'Restore chat' : 'Maximize chat'}
        >
          {#if isMaximized}
            <Minimize2 class="icon" />
          {:else}
            <Maximize2 class="icon" />
          {/if}
        </button>
        <button
          type="button"
          on:click={handleClose}
          class="header-button"
          aria-label="Close chat"
        >
          <X class="icon" />
        </button>
      </div>
    </div>

    <div class="messages-container" bind:this={scrollArea}>
      {#if error}
        <div class="error-message">
          {error}
        </div>
      {/if}

      {#each messages as message (message.id)}
        <div
          class="message {message.sender}"
          bind:this={lastMessageRef}
        >
          <!-- Display image if present -->
          {#if message.image}
            <div class="message-image">
              <img src={message.image} alt="" class="chat-image" />
            </div>
          {/if}
          
          <div class="message-content">
            {@html messageContent(message)}
          </div>
          
          <!-- Display OCR info if available -->
          {#if message.laptop_info}
            <div class="laptop-info">
              <strong>Detected Laptop:</strong> {message.laptop_info.brand} {message.laptop_info.model}
              {#if message.ocr_confidence}
                <span class="confidence">({Math.round(message.ocr_confidence * 100)}% confidence)</span>
              {/if}
            </div>
          {/if}
          
          <!-- Display products if available -->
          {#if message.products && message.products.length > 0}
            <div class="products-list">
              <strong>Related Products:</strong>
              {#each message.products.slice(0, 3) as product}
                <div class="product-item">
                  • {product.brand || ''} {product.model || ''} {product.title || ''}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      {/each}

      {#if loading}
        <div
          class="message bot"
          bind:this={lastMessageRef}
        >
          <div class="message-content loading">
            Thinking...
          </div>
        </div>
      {/if}
    </div>

    <form class="input-form" on:submit|preventDefault={handleSubmit}>
      <!-- Image preview if selected -->
      {#if imagePreview}
        <div class="image-preview-container">
          <img src={imagePreview} alt="" class="image-preview" />
          <button
            type="button"
            class="remove-image-button"
            on:click={clearSelectedImage}
            aria-label="Remove image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" x2="6" y1="6" y2="18"/>
              <line x1="6" x2="18" y1="6" y2="18"/>
            </svg>
          </button>
        </div>
      {/if}
      
      <!-- Row 1: Input + Send button -->
      <div class="input-row">
        <input
          type="text"
          bind:value={newMessage}
          on:keydown={handleKeyDown}
          placeholder="Type your message..."
          class="message-input"
          disabled={loading}
        />
        <button
          type="submit"
          class="send-button"
          disabled={loading || (!newMessage.trim() && !selectedImage)}
          aria-label="Send message"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="22" x2="11" y1="2" y2="13"/>
            <polygon points="22,2 15,22 11,13 2,9"/>
          </svg>
        </button>
      </div>

      <!-- Row 2: Upload + Mic (right-aligned) -->
      <div class="controls-row">
        <div class="left-controls">
          <input
            bind:this={fileInput}
            type="file"
            id="file-upload"
            class="file-input"
            accept="image/*"
            style="display: none;"
            on:change={handleFileSelect}
          />
          <button
            type="button"
            class="control-button upload-button"
            on:click={() => fileInput.click()}
            aria-label="Upload file"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="17,8 12,3 7,8"/>
              <line x1="12" x2="12" y1="3" y2="15"/>
            </svg>
          </button>
          
          <button
            type="button"
            class="control-button camera-button"
            on:click={openCamera}
            aria-label="Take photo"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
              <circle cx="12" cy="13" r="4"/>
            </svg>
          </button>
        </div>
        <div class="right-controls">
          <button
            type="button"
            class="control-button voice-button"
            class:listening={isListening}
            on:click={toggleVoiceInput}
            disabled={loading}
            aria-label="Voice input"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
              <line x1="12" x2="12" y1="19" y2="22"/>
            </svg>
          </button>
        </div>
      </div>

      <div class="input-menu">
        {#if config.order_taking_enabled }
        <button
          type="button"
          class="menu-item"
          on:click|stopPropagation={() => handleAction('send-order')}
          on:keydown|stopPropagation={(e) => e.key === 'Enter' && handleAction('send-order')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
          </svg>
          Submit order
        </button>
        {/if}
        <button
          type="button"
          class="menu-item"
          on:click|stopPropagation={() => handleAction('chat-history')}
          on:keydown|stopPropagation={(e) => e.key === 'Enter' && handleAction('chat-history')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Email chat to myself
        </button>
      </div>
    </form>
  </div>
{/if}

{#if showOrderModal}
  <Modal on:close={() => showOrderModal = false}>
    <div class="order-modal {theme === 'dark' ? 'dark-theme' : ''}">
      <h3>Submit Order</h3>
      <p>Please provide your information to complete your order</p>

      <!-- Dummy Order Display -->
      {#if loadingOrder}
        <div class="form-section">
          <div class="loading-indicator">Loading order details...</div>
        </div>
      {:else if order}
        <div class="form-section order-summary">
          <div class="order-header">
            <h4>Order Summary</h4>
            {#if order.status}
              <div class="order-status {order.status}">
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </div>
            {/if}
          </div>

          <div class="order-meta">
            {#if order.id}
              <div class="order-id">Order #{order.id.slice(-6)}</div>
            {/if}
            {#if order.created_at}
              <div class="order-date">{new Date(order.created_at).toLocaleString()}</div>
            {/if}
          </div>

          <p class="summary-text">{order.order_summary}</p>

          <div class="order-items">
            {#each order.items as item}
              <div class="order-item">
                <div class="item-details">
                  <span class="item-name">{item.name}</span>
                  <span class="item-quantity">x{item.quantity}</span>
                </div>
                <span class="item-price">${item.subtotal.toFixed(2)}</span>
              </div>
            {/each}
          </div>

          <div class="order-totals">
            <div class="total-line">
              <span>Subtotal:</span>
              <span>${order.subtotal.toFixed(2)}</span>
            </div>
            <div class="total-line">
              <span>Tax:</span>
              <span>${order.tax.toFixed(2)}</span>
            </div>
            {#if order.delivery_charge > 0}
              <div class="total-line">
                <span>Delivery:</span>
                <span>${order.delivery_charge.toFixed(2)}</span>
              </div>
            {/if}
            {#if order.tip_amount > 0}
              <div class="total-line">
                <span>Tip:</span>
                <span>${order.tip_amount.toFixed(2)}</span>
              </div>
            {/if}
            <div class="total-line grand-total">
              <span>Total:</span>
              <span>${order.total.toFixed(2)}</span>
            </div>
          </div>
        </div>
      {/if}

      <!-- Customer Information -->
      <div class="form-section">
        <h4>Contact Information</h4>
        <div class="form-row">
          <div class="form-group">
            <label for="first-name">First Name</label>
            <input
              type="text"
              id="first-name"
              bind:value={firstName}
              placeholder="First Name"
              class="form-input"
              required
            />
          </div>
          <div class="form-group">
            <label for="last-name">Last Name</label>
            <input
              type="text"
              id="last-name"
              bind:value={lastName}
              placeholder="Last Name"
              class="form-input"
              required
            />
          </div>
        </div>
        <div class="form-group">
          <label for="phone-number">Phone Number</label>
          <input
            type="tel"
            id="phone-number"
            bind:value={phoneNumber}
            on:blur={() => {
              // Format phone number on blur
              if (phoneNumber) {
                const formatted = phoneNumber.replace(/\D/g, '');
                if (formatted) phoneNumber = formatted;
                console.log('Phone number formatted on blur:', phoneNumber);
              }
            }}
            placeholder="Enter your phone number (digits only)"
            class="form-input"
            required
          />
          <small class="form-text">You'll receive order updates via SMS at this number</small>
        </div>
      </div>

      <!-- Delivery Method -->
      <div class="form-section">
        <h4>Delivery Method</h4>
        <div class="radio-group">
          <label class="radio-label">
            <input type="radio" bind:group={deliveryMethod} value="pickup" name="delivery-method" />
            <span>Pickup</span>
          </label>
          <label class="radio-label">
            <input type="radio" bind:group={deliveryMethod} value="delivery" name="delivery-method" />
            <span>Delivery</span>
          </label>
        </div>
      </div>

      <!-- Delivery Address (only shown if delivery is selected) -->
      {#if deliveryMethod === 'delivery'}
        <div class="form-section">
          <h4>Delivery Address</h4>
          <div class="form-group">
            <label for="address-line1">Address Line 1</label>
            <input
              type="text"
              id="address-line1"
              bind:value={deliveryAddressLine1}
              placeholder="Street Address"
              class="form-input"
              required={deliveryMethod === 'delivery'}
            />
          </div>
          <div class="form-group">
            <label for="address-line2">Address Line 2 (Optional)</label>
            <input
              type="text"
              id="address-line2"
              bind:value={deliveryAddressLine2}
              placeholder="Apt, Suite, Unit, etc."
              class="form-input"
            />
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="city">City</label>
              <input
                type="text"
                id="city"
                bind:value={deliveryCity}
                placeholder="City"
                class="form-input"
                required={deliveryMethod === 'delivery'}
              />
            </div>
            <div class="form-group">
              <label for="state">State</label>
              <input
                type="text"
                id="state"
                bind:value={deliveryState}
                placeholder="State"
                class="form-input"
                required={deliveryMethod === 'delivery'}
              />
            </div>
            <div class="form-group">
              <label for="zip-code">Zip Code</label>
              <input
                type="text"
                id="zip-code"
                bind:value={deliveryZipCode}
                placeholder="Zip Code"
                class="form-input"
                required={deliveryMethod === 'delivery'}
              />
            </div>
          </div>
        </div>

        <!-- Delivery Charges -->
        <div class="form-section">
          <h4>Delivery Charges</h4>
          <div class="form-group">
            <p class="fee-label">Delivery Fee: ${deliveryCharge.toFixed(2)}</p>
          </div>
          <div class="form-group">
            <label for="tip-amount">Tip Amount</label>
            <div class="tip-buttons">
              <button type="button" class="tip-button" on:click={() => setTipPercentage(0)}>No Tip</button>
              <button type="button" class="tip-button" on:click={() => setTipPercentage(15)}>15%</button>
              <button type="button" class="tip-button" on:click={() => setTipPercentage(18)}>18%</button>
              <button type="button" class="tip-button" on:click={() => setTipPercentage(20)}>20%</button>
            </div>
            <input
              type="number"
              id="tip-amount"
              bind:value={tipAmount}
              min="0"
              step="0.01"
              class="form-input"
            />
          </div>
        </div>
      {/if}

      <!-- Payment Method -->
      <div class="form-section">
        <h4>Payment Method</h4>
        <div class="radio-group">
          {#if deliveryMethod === 'pickup'}
            <label class="radio-label">
              <input type="radio" bind:group={paymentMethod} value="pay_later" name="payment-method" />
              <span>Pay Later (at pickup)</span>
            </label>
          {/if}
          <label class="radio-label">
            <input type="radio" bind:group={paymentMethod} value="pay_now" name="payment-method" />
            <span>Pay Now (credit card)</span>
          </label>
        </div>
      </div>

      <!-- Payment Section (if pay now is selected) -->
      {#if paymentMethod === 'pay_now'}
        <div class="form-section">
          <h4>Payment Information</h4>
          <div class="form-group">
            <p class="fee-label">Total Amount: ${calculateTotal().toFixed(2)}</p>
          </div>
          {#if selectedPaymentMethod}
            <div class="selected-payment-method">
              <div class="payment-method-info">
                <span class="card-name">{selectedPaymentMethod.name}</span>
                <span class="card-details">{selectedPaymentMethod.brand} •••• {selectedPaymentMethod.last4}</span>
                <span class="postal-code">Postal code: {selectedPaymentMethod.postalCode}</span>
              </div>
              <button class="change-payment-btn" on:click={() => selectedPaymentMethod = null}>
                Change
              </button>
            </div>
          {:else}
            <div class="credit-card-form">
              <StripePaymentForm
                bind:this={stripePaymentForm}
                amount={calculateTotal()}
                on:error={(e) => {
                  stripeFormError = e.detail.message;
                  orderError = e.detail.message; // Also show in order error
                }}
                on:success={handlePaymentSuccess}
              />
            </div>
          {/if}
          {#if stripeFormError}
            <div class="error-message">{stripeFormError}</div>
          {/if}
        </div>
      {/if}

      {#if orderError}
        <div class="error">{orderError}</div>
      {/if}

      <div class="button-group">
        <button
          class="cancel"
          on:click={() => showOrderModal = false}
        >
          Cancel
        </button>
        <button
          class="submit"
          on:click={handleSubmitOrder}
          disabled={submittingOrder}
        >
          {submittingOrder ? 'Submitting...' : 'Submit Order'}
        </button>
      </div>
    </div>
  </Modal>
{/if}

{#if showEmailModal}
  <Modal on:close={() => showEmailModal = false}>
    <div class="order-modal {theme === 'dark' ? 'dark-theme' : ''}">
      <h3>Email Chat History</h3>
      <p>Enter your email address to receive a copy of this conversation</p>

      <div class="form-group">
        <input
          type="email"
          id="email-address"
          bind:value={emailAddress}
          placeholder="Enter your email address"
          class="form-input"
          on:keydown={(e) => e.key === 'Enter' && !sendingEmail && handleEmailChat()}
        />
      </div>

      {#if emailError}
        <div class="error">{emailError}</div>
      {/if}

      <div class="button-group">
        <button
          class="cancel"
          on:click={() => showEmailModal = false}
        >
          Cancel
        </button>
        <button
          class="submit"
          on:click={handleEmailChat}
          disabled={sendingEmail || !emailAddress}
        >
          {sendingEmail ? 'Sending...' : 'Send Email'}
        </button>
      </div>
    </div>
  </Modal>
{/if}

{#if showCameraModal}
  <Modal on:close={closeCamera}>
    <div class="camera-modal {theme === 'dark' ? 'dark-theme' : ''}">
      <h3>Take Photo</h3>
      
      {#if cameraError}
        <div class="error-message">{cameraError}</div>
        <div class="camera-actions">
          <button type="button" class="cancel-btn" on:click={closeCamera}>
            Close
          </button>
        </div>
      {:else}
        <div class="camera-container">
          <video
            bind:this={videoElement}
            autoplay
            playsinline
            muted
            class="camera-video"
            style="width: 100%; height: auto;"
          ></video>
          <canvas bind:this={canvasElement} style="display: none;"></canvas>
        </div>
        
        <div class="camera-actions">
          <button type="button" class="capture-btn" on:click={capturePhoto}>
            Capture Photo
          </button>
          
          <button type="button" class="cancel-btn" on:click={closeCamera}>
            Cancel
          </button>
        </div>
      {/if}
    </div>
  </Modal>
{/if}

<style>
  /* Any ModernChat-specific styles can remain here */

  .selected-payment-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--input-border, #ddd);
    border-radius: 6px;
    background-color: var(--input-bg, #fff);
    margin-top: 8px;
  }

  .payment-method-info {
    display: flex;
    flex-direction: column;
  }

  .card-name {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .card-details {
    text-transform: capitalize;
    margin-bottom: 4px;
  }

  .postal-code {
    font-size: 14px;
    color: var(--chat-text-secondary, #666);
  }

  .change-payment-btn {
    background-color: transparent;
    border: 1px solid var(--input-border, #ddd);
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    color: var(--chat-text, #333);
    font-size: 14px;
  }

  .change-payment-btn:hover {
    background-color: var(--input-border, #ddd);
  }

  /* Camera Modal Styles */
  .camera-modal {
    padding: 1.5rem;
    max-width: 500px;
    width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .camera-modal h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    color: var(--chat-text, #333);
    text-align: center;
    font-weight: 600;
  }

  .camera-container {
    position: relative;
    width: 100%;
    height: 300px;
    margin: 0 auto 1.5rem;
    border-radius: 16px;
    overflow: hidden;
    background: #000;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
  }

  .camera-video {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }

  .camera-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }

  .capture-btn {
    background: var(--chat-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    order: 1;
  }

  .capture-btn:hover {
    background: var(--chat-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  }

  .secondary-btn {
    background: var(--input-bg, #f8fafc);
    color: var(--chat-text, #374151);
    border: 1px solid var(--input-border, #e2e8f0);
    border-radius: 8px;
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    order: 2;
  }

  .secondary-btn:hover {
    background: var(--input-border, #e2e8f0);
    transform: translateY(-1px);
  }

  .cancel-btn {
    background: transparent;
    color: var(--chat-text-secondary, #6b7280);
    border: 1px solid var(--input-border, #e2e8f0);
    border-radius: 8px;
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    order: 2;
  }

  .cancel-btn:hover {
    background: var(--input-border, #f3f4f6);
    color: var(--chat-text, #374151);
  }

  .error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 500;
  }

  /* Camera Button Styling */
  .camera-button {
    color: var(--chat-primary, #3b82f6);
  }

  .camera-button:hover {
    background: var(--chat-primary-light, #dbeafe);
    color: var(--chat-primary-dark, #2563eb);
  }

  /* Dark theme support for camera modal */
  .camera-modal.dark-theme {
    background: var(--chat-bg-dark, #1f2937);
    color: var(--chat-text-dark, #f9fafb);
  }

  .camera-modal.dark-theme h3 {
    color: var(--chat-text-dark, #f9fafb);
  }

  .camera-modal.dark-theme .secondary-btn {
    background: var(--chat-bg-dark, #374151);
    border-color: var(--chat-border-dark, #4b5563);
    color: var(--chat-text-dark, #f9fafb);
  }

  .camera-modal.dark-theme .secondary-btn:hover {
    background: var(--chat-border-dark, #4b5563);
  }

  .camera-modal.dark-theme .cancel-btn {
    border-color: var(--chat-border-dark, #4b5563);
    color: var(--chat-text-secondary-dark, #9ca3af);
  }

  .camera-modal.dark-theme .cancel-btn:hover {
    background: var(--chat-border-dark, #374151);
    color: var(--chat-text-dark, #f9fafb);
  }

  .camera-modal.dark-theme .error-message {
    background: #431e1e;
    border-color: #7f2928;
    color: #fca5a5;
  }
</style>
