<script>
  import { createEventDispatcher } from 'svelte';

  // Use a constant instead of an export property since it's not being used externally
  export const amount = 0;
  // We'll use CSS variables for theming instead of a prop

  const dispatch = createEventDispatcher();

  // Card form data
  let cardNumber = '';
  let cardExpiry = '';
  let cardCvc = '';
  let billingName = '';
  let postalCode = '';
  let cardError = '';
  let processing = false;

  // Validate card number format (basic validation)
  function validateCardNumber(number) {
    const cleaned = number.replace(/\s+/g, '');
    return /^\d{16}$/.test(cleaned);
  }

  // Validate expiry date format (MM/YY)
  function validateExpiry(expiry) {
    return /^(0[1-9]|1[0-2])\/\d{2}$/.test(expiry);
  }

  // Validate CVC format (3-4 digits)
  function validateCvc(cvc) {
    return /^\d{3,4}$/.test(cvc);
  }

  // Format card number with spaces for readability
  function formatCardNumber(e) {
    const input = e.target;
    let value = input.value.replace(/\s+/g, '');
    value = value.replace(/\D/g, '');

    if (value.length > 16) {
      value = value.substr(0, 16);
    }

    // Add spaces after every 4 digits
    const parts = [];
    for (let i = 0; i < value.length; i += 4) {
      parts.push(value.substr(i, 4));
    }

    cardNumber = parts.join(' ');
  }

  // Format expiry date as MM/YY
  function formatExpiry(e) {
    const input = e.target;
    let value = input.value.replace(/\D/g, '');

    if (value.length > 4) {
      value = value.substr(0, 4);
    }

    if (value.length > 2) {
      cardExpiry = value.substr(0, 2) + '/' + value.substr(2);
    } else {
      cardExpiry = value;
    }
  }

  // Validate postal code (basic validation - allows international formats)
  function validatePostalCode(code) {
    return code.trim().length >= 3;
  }

  // Validate all fields
  function validateForm() {
    if (!billingName.trim()) {
      cardError = 'Please enter the name on card';
      return false;
    }

    if (!validateCardNumber(cardNumber)) {
      cardError = 'Please enter a valid card number';
      return false;
    }

    if (!validateExpiry(cardExpiry)) {
      cardError = 'Please enter a valid expiry date (MM/YY)';
      return false;
    }

    if (!validateCvc(cardCvc)) {
      cardError = 'Please enter a valid CVC code';
      return false;
    }

    if (!validatePostalCode(postalCode)) {
      cardError = 'Please enter a valid postal code';
      return false;
    }

    cardError = '';
    return true;
  }

  // Public method to check if the form is valid (can be called from parent components)
  export function isValid() {
    return validateForm();
  }
  
  // Handle form submission - export so it can be called from parent components
  export function handleSubmit() {
    if (validateForm()) {
      createPaymentMethod().then(result => {
        if (result.success) {
          // Payment method created successfully
          dispatch('success', { 
            paymentMethodId: result.paymentMethodId,
            brand: result.brand,
            last4: result.last4,
            billingDetails: result.billingDetails
          });
        } else {
          // Payment method creation failed
          dispatch('error', { message: result.error });
        }
      }).catch(err => {
        console.error('Payment error:', err);
        dispatch('error', { message: err.message || 'Payment processing failed' });
      });
    }
  }

  // Create a payment method that will work with Stripe in test mode
  export async function createPaymentMethod() {
    if (!validateForm()) {
      return { success: false, error: cardError };
    }

    processing = true;

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Extract last 4 digits of card number
      const last4 = cardNumber.replace(/\s+/g, '').slice(-4);

      // Determine card brand based on first digit
      let brand = 'unknown';
      const firstDigit = cardNumber.trim()[0];
      if (firstDigit === '4') brand = 'visa';
      else if (firstDigit === '5') brand = 'mastercard';
      else if (firstDigit === '3') brand = 'amex';
      else if (firstDigit === '6') brand = 'discover';

      // Use a consistent test payment method ID that works with Stripe in test mode
      // This is a special test payment method ID that Stripe recognizes in test mode
      const testPaymentMethodId = 'pm_card_visa';
      
      console.log('Created test payment method:', testPaymentMethodId);
      
      processing = false;
      return {
        success: true,
        paymentMethodId: testPaymentMethodId,
        last4,
        brand,
        billingDetails: {
          name: billingName,
          postalCode: postalCode
        }
      };
    } catch (err) {
      processing = false;
      console.error('Error creating payment method:', err);
      return { success: false, error: 'Payment processing failed. Please try again.' };
    }
  }
</script>

<div class="custom-payment-form">
  <div class="form-row">
    <div class="form-col">
      <label for="billing-name">Name on Card</label>
      <input
        type="text"
        id="billing-name"
        class="form-input"
        placeholder="John Smith"
        bind:value={billingName}
        autocomplete="cc-name"
      />
    </div>
  </div>

  <div class="form-row">
    <div class="form-col">
      <label for="card-number">Card Number</label>
      <input
        type="text"
        id="card-number"
        class="form-input"
        placeholder="1234 5678 9012 3456"
        bind:value={cardNumber}
        on:input={formatCardNumber}
        maxlength="19"
        autocomplete="cc-number"
      />
    </div>
  </div>

  <div class="form-row form-row-split">
    <div class="form-col">
      <label for="card-expiry">Expiry Date</label>
      <input
        type="text"
        id="card-expiry"
        class="form-input"
        placeholder="MM/YY"
        bind:value={cardExpiry}
        on:input={formatExpiry}
        maxlength="5"
        autocomplete="cc-exp"
      />
    </div>
    <div class="form-col">
      <label for="card-cvc">CVC</label>
      <input
        type="text"
        id="card-cvc"
        class="form-input"
        placeholder="123"
        bind:value={cardCvc}
        maxlength="4"
        autocomplete="cc-csc"
      />
    </div>
    <div class="form-col">
      <label for="postal-code">Zip Code</label>
      <input
        type="text"
        id="postal-code"
        class="form-input"
        placeholder="12345"
        bind:value={postalCode}
        maxlength="10"
        autocomplete="postal-code"
      />
    </div>
  </div>

  {#if cardError}
    <div class="card-error">{cardError}</div>
  {/if}

  <div class="payment-info">
    <div class="secure-badge">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
      </svg>
      <span>Secure payment</span>
    </div>
  </div>
</div>

<style>
  .custom-payment-form {
    margin-top: 16px;
  }

  .form-row {
    margin-bottom: 20px;
  }

  .form-row-split {
    display: flex;
    gap: 16px;
  }

  .form-col {
    flex: 1;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    color: var(--chat-text);
  }

  .form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--input-border, #ddd);
    border-radius: 6px;
    background-color: var(--input-bg, #fff);
    font-size: 16px;
    color: var(--chat-text, #333);
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--chat-primary, #3b82f6);
    box-shadow: 0 0 0 1px var(--chat-primary, #3b82f6);
  }

  .card-error {
    color: var(--chat-error, #e53935);
    font-size: 12px;
    margin-top: 8px;
    margin-bottom: 16px;
  }

  .payment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    font-size: 14px;
  }

  .secure-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--chat-success, #43a047);
  }

  .amount-display {
    font-weight: 600;
    color: var(--chat-text);
  }
  
  .payment-submit-btn {
    width: 100%;
    padding: 12px;
    margin-top: 16px;
    background-color: var(--chat-primary, #3b82f6);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .payment-submit-btn:hover {
    background-color: var(--chat-primary-dark, #2563eb);
  }
  
  .payment-submit-btn:disabled {
    background-color: var(--chat-disabled, #9ca3af);
    cursor: not-allowed;
  }
</style>
