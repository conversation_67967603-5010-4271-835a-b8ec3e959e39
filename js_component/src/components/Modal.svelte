<script>
  import { createEventDispatcher } from 'svelte';
  import { fade } from 'svelte/transition';
  
  const dispatch = createEventDispatcher();
  
  function handleKeydown(event) {
    if (event.key === 'Escape') {
      dispatch('close');
    }
  }
</script>

<div 
  class="modal-backdrop" 
  on:click={() => dispatch('close')} 
  on:keydown={handleKeydown}
  transition:fade
  role="dialog"
  aria-modal="true"
  tabindex="-1"
>
  <div 
    class="modal-content" 
    on:click|stopPropagation
    on:keydown|stopPropagation
  >
    <slot></slot>
  </div>
</div> 