export function getScriptBasePath() {
  // Try to get the current script element
  let currentScript = document.currentScript;
  
  // Fallback to last script element if currentScript not available
  if (!currentScript) {
    const scripts = document.getElementsByTagName('script');
    currentScript = scripts[scripts.length - 1];
  }
  
  // Get the script's URL
  const scriptUrl = new URL(currentScript.src);
  let basePath = scriptUrl.origin;
  
  return basePath;
}

export const basePath = getScriptBasePath();
