var gs=Object.defineProperty;var ms=(i,e,t)=>e in i?gs(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var v=(i,e,t)=>(ms(i,typeof e!="symbol"?e+"":e,t),t);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const r of s)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function t(s){const r={};return s.integrity&&(r.integrity=s.integrity),s.referrerPolicy&&(r.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?r.credentials="include":s.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(s){if(s.ep)return;s.ep=!0;const r=t(s);fetch(s.href,r)}})();function Ye(){}const Ei=i=>i;function ie(i,e){for(const t in e)i[t]=e[t];return i}function Si(i){return i()}function Zn(){return Object.create(null)}function ve(i){i.forEach(Si)}function Ut(i){return typeof i=="function"}function Oe(i,e){return i!=i?e==e:i!==e||i&&typeof i=="object"||typeof i=="function"}let Et;function Yn(i,e){return Et||(Et=document.createElement("a")),Et.href=e,i===Et.href}function As(i){return Object.keys(i).length===0}function Ft(i,e,t,n){if(i){const s=Ri(i,e,t,n);return i[0](s)}}function Ri(i,e,t,n){return i[1]&&n?ie(t.ctx.slice(),i[1](n(e))):t.ctx}function Vt(i,e,t,n){if(i[2]&&n){const s=i[2](n(t));if(e.dirty===void 0)return s;if(typeof s=="object"){const r=[],o=Math.max(e.dirty.length,s.length);for(let l=0;l<o;l+=1)r[l]=e.dirty[l]|s[l];return r}return e.dirty|s}return e.dirty}function Bt(i,e,t,n,s,r){if(s){const o=Ri(e,t,n,r);i.p(o,s)}}function Ht(i){if(i.ctx.length>32){const e=[],t=i.ctx.length/32;for(let n=0;n<t;n++)e[n]=-1;return e}return-1}function Re(i){const e={};for(const t in i)t[0]!=="$"&&(e[t]=i[t]);return e}function Xn(i,e){const t={};e=new Set(e);for(const n in i)!e.has(n)&&n[0]!=="$"&&(t[n]=i[n]);return t}function Qn(i){const e=typeof i=="string"&&i.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return e?[parseFloat(e[1]),e[2]||"px"]:[i,"px"]}const Ci=typeof window<"u";let vi=Ci?()=>window.performance.now():()=>Date.now(),gn=Ci?i=>requestAnimationFrame(i):Ye;const qe=new Set;function Li(i){qe.forEach(e=>{e.c(i)||(qe.delete(e),e.f())}),qe.size!==0&&gn(Li)}function Ii(i){let e;return qe.size===0&&gn(Li),{promise:new Promise(t=>{qe.add(e={c:i,f:t})}),abort(){qe.delete(e)}}}function N(i,e){i.appendChild(e)}function Ni(i){if(!i)return document;const e=i.getRootNode?i.getRootNode():i.ownerDocument;return e&&e.host?e:i.ownerDocument}function _s(i){const e=U("style");return ks(Ni(i),e),e.sheet}function ks(i,e){return N(i.head||i,e),e.sheet}function Ae(i,e,t){i.insertBefore(e,t||null)}function ce(i){i.parentNode&&i.parentNode.removeChild(i)}function bs(i,e){for(let t=0;t<i.length;t+=1)i[t]&&i[t].d(e)}function U(i){return document.createElement(i)}function Oi(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function mn(i){return document.createTextNode(i)}function we(){return mn(" ")}function An(){return mn("")}function St(i,e,t,n){return i.addEventListener(e,t,n),()=>i.removeEventListener(e,t,n)}function ws(i){return function(e){return e.preventDefault(),i.call(this,e)}}function x(i,e,t){t==null?i.removeAttribute(e):i.getAttribute(e)!==t&&i.setAttribute(e,t)}function Nt(i,e){for(const t in e)x(i,t,e[t])}function ys(i){return Array.from(i.childNodes)}function Ts(i,e){e=""+e,i.data!==e&&(i.data=e)}function Kn(i,e){i.value=e??""}function Mi(i,e,{bubbles:t=!1,cancelable:n=!1}={}){const s=document.createEvent("CustomEvent");return s.initCustomEvent(i,t,n,e),s}const Ot=new Map;let Mt=0;function xs(i){let e=5381,t=i.length;for(;t--;)e=(e<<5)-e^i.charCodeAt(t);return e>>>0}function Es(i,e){const t={stylesheet:_s(e),rules:{}};return Ot.set(i,t),t}function Di(i,e,t,n,s,r,o,l=0){const u=16.666/n;let a=`{
`;for(let w=0;w<=1;w+=u){const y=e+(t-e)*r(w);a+=w*100+`%{${o(y,1-y)}}
`}const f=a+`100% {${o(t,1-t)}}
}`,g=`__svelte_${xs(f)}_${l}`,h=Ni(i),{stylesheet:d,rules:p}=Ot.get(h)||Es(h,i);p[g]||(p[g]=!0,d.insertRule(`@keyframes ${g} ${f}`,d.cssRules.length));const _=i.style.animation||"";return i.style.animation=`${_?`${_}, `:""}${g} ${n}ms linear ${s}ms 1 both`,Mt+=1,g}function cn(i,e){const t=(i.style.animation||"").split(", "),n=t.filter(e?r=>r.indexOf(e)<0:r=>r.indexOf("__svelte")===-1),s=t.length-n.length;s&&(i.style.animation=n.join(", "),Mt-=s,Mt||Ss())}function Ss(){gn(()=>{Mt||(Ot.forEach(i=>{const{ownerNode:e}=i.stylesheet;e&&ce(e)}),Ot.clear())})}let dt;function ft(i){dt=i}function Pi(){if(!dt)throw new Error("Function called outside component initialization");return dt}function $i(i){Pi().$$.on_mount.push(i)}function Rs(){const i=Pi();return(e,t,{cancelable:n=!1}={})=>{const s=i.$$.callbacks[e];if(s){const r=Mi(e,t,{cancelable:n});return s.slice().forEach(o=>{o.call(i,r)}),!r.defaultPrevented}return!0}}const je=[],un=[];let Ze=[];const Jn=[],Cs=Promise.resolve();let fn=!1;function vs(){fn||(fn=!0,Cs.then(zi))}function Xe(i){Ze.push(i)}const tn=new Set;let We=0;function zi(){if(We!==0)return;const i=dt;do{try{for(;We<je.length;){const e=je[We];We++,ft(e),Ls(e.$$)}}catch(e){throw je.length=0,We=0,e}for(ft(null),je.length=0,We=0;un.length;)un.pop()();for(let e=0;e<Ze.length;e+=1){const t=Ze[e];tn.has(t)||(tn.add(t),t())}Ze.length=0}while(je.length);for(;Jn.length;)Jn.pop()();fn=!1,tn.clear(),ft(i)}function Ls(i){if(i.fragment!==null){i.update(),ve(i.before_update);const e=i.dirty;i.dirty=[-1],i.fragment&&i.fragment.p(i.ctx,e),i.after_update.forEach(Xe)}}function Is(i){const e=[],t=[];Ze.forEach(n=>i.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),Ze=e}let nt;function Ui(){return nt||(nt=Promise.resolve(),nt.then(()=>{nt=null})),nt}function Dt(i,e,t){i.dispatchEvent(Mi(`${e?"intro":"outro"}${t}`))}const Lt=new Set;let Se;function Fi(){Se={r:0,c:[],p:Se}}function Vi(){Se.r||ve(Se.c),Se=Se.p}function B(i,e){i&&i.i&&(Lt.delete(i),i.i(e))}function Y(i,e,t,n){if(i&&i.o){if(Lt.has(i))return;Lt.add(i),Se.c.push(()=>{Lt.delete(i),n&&(t&&i.d(1),n())}),i.o(e)}else n&&n()}const Bi={duration:0};function Ns(i,e,t){const n={direction:"in"};let s=e(i,t,n),r=!1,o,l,u=0;function a(){o&&cn(i,o)}function f(){const{delay:h=0,duration:d=300,easing:p=Ei,tick:_=Ye,css:w}=s||Bi;w&&(o=Di(i,0,1,d,h,p,w,u++)),_(0,1);const y=vi()+h,V=y+d;l&&l.abort(),r=!0,Xe(()=>Dt(i,!0,"start")),l=Ii(O=>{if(r){if(O>=V)return _(1,0),Dt(i,!0,"end"),a(),r=!1;if(O>=y){const C=p((O-y)/d);_(C,1-C)}}return r})}let g=!1;return{start(){g||(g=!0,cn(i),Ut(s)?(s=s(n),Ui().then(f)):f())},invalidate(){g=!1},end(){r&&(a(),r=!1)}}}function Os(i,e,t){const n={direction:"out"};let s=e(i,t,n),r=!0,o;const l=Se;l.r+=1;function u(){const{delay:a=0,duration:f=300,easing:g=Ei,tick:h=Ye,css:d}=s||Bi;d&&(o=Di(i,1,0,f,a,g,d));const p=vi()+a,_=p+f;Xe(()=>Dt(i,!1,"start")),Ii(w=>{if(r){if(w>=_)return h(0,1),Dt(i,!1,"end"),--l.r||ve(l.c),!1;if(w>=p){const y=g((w-p)/f);h(1-y,y)}}return r})}return Ut(s)?Ui().then(()=>{s=s(n),u()}):u(),{end(a){a&&s.tick&&s.tick(1,0),r&&(o&&cn(i,o),r=!1)}}}function Ms(i,e){i.d(1),e.delete(i.key)}function Ds(i,e,t,n,s,r,o,l,u,a,f,g){let h=i.length,d=r.length,p=h;const _={};for(;p--;)_[i[p].key]=p;const w=[],y=new Map,V=new Map,O=[];for(p=d;p--;){const S=g(s,r,p),L=t(S);let M=o.get(L);M?n&&O.push(()=>M.p(S,e)):(M=a(L,S),M.c()),y.set(L,w[p]=M),L in _&&V.set(L,Math.abs(p-_[L]))}const C=new Set,X=new Set;function re(S){B(S,1),S.m(l,f),o.set(S.key,S),f=S.first,d--}for(;h&&d;){const S=w[d-1],L=i[h-1],M=S.key,_e=L.key;S===L?(f=S.first,h--,d--):y.has(_e)?!o.has(M)||C.has(M)?re(S):X.has(_e)?h--:V.get(M)>V.get(_e)?(X.add(M),re(S)):(C.add(_e),h--):(u(L,o),h--)}for(;h--;){const S=i[h];y.has(S.key)||u(S,o)}for(;d;)re(w[d-1]);return ve(O),w}function gt(i,e){const t={},n={},s={$$scope:1};let r=i.length;for(;r--;){const o=i[r],l=e[r];if(l){for(const u in o)u in l||(n[u]=1);for(const u in l)s[u]||(t[u]=l[u],s[u]=1);i[r]=l}else for(const u in o)s[u]=1}for(const o in n)o in t||(t[o]=void 0);return t}function _n(i){return typeof i=="object"&&i!==null?i:{}}function Ce(i){i&&i.c()}function ye(i,e,t,n){const{fragment:s,after_update:r}=i.$$;s&&s.m(e,t),n||Xe(()=>{const o=i.$$.on_mount.map(Si).filter(Ut);i.$$.on_destroy?i.$$.on_destroy.push(...o):ve(o),i.$$.on_mount=[]}),r.forEach(Xe)}function Te(i,e){const t=i.$$;t.fragment!==null&&(Is(t.after_update),ve(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function Ps(i,e){i.$$.dirty[0]===-1&&(je.push(i),vs(),i.$$.dirty.fill(0)),i.$$.dirty[e/31|0]|=1<<e%31}function Qe(i,e,t,n,s,r,o,l=[-1]){const u=dt;ft(i);const a=i.$$={fragment:null,ctx:[],props:r,update:Ye,not_equal:s,bound:Zn(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:Zn(),dirty:l,skip_bound:!1,root:e.target||u.$$.root};o&&o(a.root);let f=!1;if(a.ctx=t?t(i,e.props||{},(g,h,...d)=>{const p=d.length?d[0]:h;return a.ctx&&s(a.ctx[g],a.ctx[g]=p)&&(!a.skip_bound&&a.bound[g]&&a.bound[g](p),f&&Ps(i,g)),h}):[],a.update(),f=!0,ve(a.before_update),a.fragment=n?n(a.ctx):!1,e.target){if(e.hydrate){const g=ys(e.target);a.fragment&&a.fragment.l(g),g.forEach(ce)}else a.fragment&&a.fragment.c();e.intro&&B(i.$$.fragment),ye(i,e.target,e.anchor,e.customElement),zi()}ft(u)}class Ke{$destroy(){Te(this,1),this.$destroy=Ye}$on(e,t){if(!Ut(t))return Ye;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{const s=n.indexOf(t);s!==-1&&n.splice(s,1)}}$set(e){this.$$set&&!As(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const it="https://aida.research-triangle.ai/api";class kn{constructor(){this.conversationId=null,this.history=[],this.config=null,this.csrfToken=null}async getCsrfToken(){try{const e=await fetch(`${it}/csrf/`,{credentials:"include"});if(!e.ok)throw new Error("Failed to get CSRF token");const t=await e.json();return this.csrfToken=t.csrfToken,this.csrfToken}catch(e){throw console.error("CSRF token error:",e),e}}async getConfig(){try{const e=await fetch(`${it}/chat/config/`,{headers:{Referer:window.location.href}});if(!e.ok)throw new Error("Failed to get chat configuration");const t=await e.json();return this.config=t,this.llm_slug=t.llm_slug,t}catch(e){throw console.error("Failed to get chat config:",e),e}}async startNewConversation(){try{this.config||await this.getConfig();const e=`chat-${Date.now()}`,t=await fetch(`${it}/chat/thread/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({workspace_slug:this.config.llm_slug,chat_id:e})});if(t.ok){const n=await t.json();this.conversationId=n.thread_id,this.workspaceSlug=this.config.llm_slug,this.chatSlug=n.thread_id}else throw new Error("Failed to create new chat session")}catch(e){throw console.error("Error starting new conversation:",e),e}}async sendMessage(e){try{this.config||await this.getConfig(),this.conversationId||await this.startNewConversation();const t=`${it}/chat/message/`;this.csrfToken||await this.getCsrfToken();const n={thread_id:this.chatSlug,message:e,llm_slug:this.llm_slug};console.log("Request payload:",n);const s=await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","X-CSRFToken":this.csrfToken},body:JSON.stringify(n)});if(!s.ok)throw new Error("Failed to send message");const r=await s.json();return this.conversationId=r.id,this.history.push({role:"assistant",message:r.textResponse,metrics:r.metrics}),{response:r.textResponse,history:this.history,metrics:r.metrics}}catch(t){throw console.error("Chat error:",t),t}}async getConversation(){if(!this.conversationId)return null;try{const e=await fetch(`${it}/chat/${this.conversationId}/`);if(!e.ok)throw new Error("Failed to get conversation");const t=await e.json();return this.history=t.history,t.history}catch(e){throw console.error("Get conversation error:",e),e}}addMessage(e,t){this.history.push({role:e,message:t})}getMessage(e){return this.history[e]}getAllMessages(){return this.history}clearHistory(){this.history=[]}}/**
 * @license lucide-svelte v0.473.0 - ISC
 *
 * ISC License
 * 
 * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
 * 
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 * 
 */const $s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"},ei=$s;function ti(i,e,t){const n=i.slice();return n[11]=e[t][0],n[12]=e[t][1],n}function nn(i){let e,t=[i[12]],n={};for(let s=0;s<t.length;s+=1)n=ie(n,t[s]);return{c(){e=Oi(i[11]),Nt(e,n)},m(s,r){Ae(s,e,r)},p(s,r){Nt(e,n=gt(t,[r&32&&s[12]]))},d(s){s&&ce(e)}}}function ni(i){let e=i[11],t,n=i[11]&&nn(i);return{c(){n&&n.c(),t=An()},m(s,r){n&&n.m(s,r),Ae(s,t,r)},p(s,r){s[11]?e?Oe(e,s[11])?(n.d(1),n=nn(s),e=s[11],n.c(),n.m(t.parentNode,t)):n.p(s,r):(n=nn(s),e=s[11],n.c(),n.m(t.parentNode,t)):e&&(n.d(1),n=null,e=s[11])},d(s){s&&ce(t),n&&n.d(s)}}}function zs(i){let e,t,n,s,r,o=i[5],l=[];for(let h=0;h<o.length;h+=1)l[h]=ni(ti(i,o,h));const u=i[10].default,a=Ft(u,i,i[9],null);let f=[ei,i[7],{width:i[2]},{height:i[2]},{stroke:i[1]},{"stroke-width":n=i[4]?Number(i[3])*24/Number(i[2]):i[3]},{class:s=i[6]("lucide-icon","lucide",i[0]?`lucide-${i[0]}`:"",i[8].class)}],g={};for(let h=0;h<f.length;h+=1)g=ie(g,f[h]);return{c(){e=Oi("svg");for(let h=0;h<l.length;h+=1)l[h].c();t=An(),a&&a.c(),Nt(e,g)},m(h,d){Ae(h,e,d);for(let p=0;p<l.length;p+=1)l[p]&&l[p].m(e,null);N(e,t),a&&a.m(e,null),r=!0},p(h,[d]){if(d&32){o=h[5];let p;for(p=0;p<o.length;p+=1){const _=ti(h,o,p);l[p]?l[p].p(_,d):(l[p]=ni(_),l[p].c(),l[p].m(e,t))}for(;p<l.length;p+=1)l[p].d(1);l.length=o.length}a&&a.p&&(!r||d&512)&&Bt(a,u,h,h[9],r?Vt(u,h[9],d,null):Ht(h[9]),null),Nt(e,g=gt(f,[ei,d&128&&h[7],(!r||d&4)&&{width:h[2]},(!r||d&4)&&{height:h[2]},(!r||d&2)&&{stroke:h[1]},(!r||d&28&&n!==(n=h[4]?Number(h[3])*24/Number(h[2]):h[3]))&&{"stroke-width":n},(!r||d&257&&s!==(s=h[6]("lucide-icon","lucide",h[0]?`lucide-${h[0]}`:"",h[8].class)))&&{class:s}]))},i(h){r||(B(a,h),r=!0)},o(h){Y(a,h),r=!1},d(h){h&&ce(e),bs(l,h),a&&a.d(h)}}}function Us(i,e,t){const n=["name","color","size","strokeWidth","absoluteStrokeWidth","iconNode"];let s=Xn(e,n),{$$slots:r={},$$scope:o}=e,{name:l=void 0}=e,{color:u="currentColor"}=e,{size:a=24}=e,{strokeWidth:f=2}=e,{absoluteStrokeWidth:g=!1}=e,{iconNode:h=[]}=e;const d=(...p)=>p.filter((_,w,y)=>!!_&&y.indexOf(_)===w).join(" ");return i.$$set=p=>{t(8,e=ie(ie({},e),Re(p))),t(7,s=Xn(e,n)),"name"in p&&t(0,l=p.name),"color"in p&&t(1,u=p.color),"size"in p&&t(2,a=p.size),"strokeWidth"in p&&t(3,f=p.strokeWidth),"absoluteStrokeWidth"in p&&t(4,g=p.absoluteStrokeWidth),"iconNode"in p&&t(5,h=p.iconNode),"$$scope"in p&&t(9,o=p.$$scope)},e=Re(e),[l,u,a,f,g,h,d,s,e,o,r]}class Fs extends Ke{constructor(e){super(),Qe(this,e,Us,zs,Oe,{name:0,color:1,size:2,strokeWidth:3,absoluteStrokeWidth:4,iconNode:5})}}const bn=Fs;function Vs(i){let e;const t=i[2].default,n=Ft(t,i,i[3],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||r&8)&&Bt(n,t,s,s[3],e?Vt(t,s[3],r,null):Ht(s[3]),null)},i(s){e||(B(n,s),e=!0)},o(s){Y(n,s),e=!1},d(s){n&&n.d(s)}}}function Bs(i){let e,t;const n=[{name:"maximize-2"},i[1],{iconNode:i[0]}];let s={$$slots:{default:[Vs]},$$scope:{ctx:i}};for(let r=0;r<n.length;r+=1)s=ie(s,n[r]);return e=new bn({props:s}),{c(){Ce(e.$$.fragment)},m(r,o){ye(e,r,o),t=!0},p(r,[o]){const l=o&3?gt(n,[n[0],o&2&&_n(r[1]),o&1&&{iconNode:r[0]}]):{};o&8&&(l.$$scope={dirty:o,ctx:r}),e.$set(l)},i(r){t||(B(e.$$.fragment,r),t=!0)},o(r){Y(e.$$.fragment,r),t=!1},d(r){Te(e,r)}}}function Hs(i,e,t){let{$$slots:n={},$$scope:s}=e;const r=[["polyline",{points:"15 3 21 3 21 9"}],["polyline",{points:"9 21 3 21 3 15"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14"}]];return i.$$set=o=>{t(1,e=ie(ie({},e),Re(o))),"$$scope"in o&&t(3,s=o.$$scope)},e=Re(e),[r,e,n,s]}class Gs extends Ke{constructor(e){super(),Qe(this,e,Hs,Bs,Oe,{})}}const Hi=Gs;function Ws(i){let e;const t=i[2].default,n=Ft(t,i,i[3],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||r&8)&&Bt(n,t,s,s[3],e?Vt(t,s[3],r,null):Ht(s[3]),null)},i(s){e||(B(n,s),e=!0)},o(s){Y(n,s),e=!1},d(s){n&&n.d(s)}}}function js(i){let e,t;const n=[{name:"send"},i[1],{iconNode:i[0]}];let s={$$slots:{default:[Ws]},$$scope:{ctx:i}};for(let r=0;r<n.length;r+=1)s=ie(s,n[r]);return e=new bn({props:s}),{c(){Ce(e.$$.fragment)},m(r,o){ye(e,r,o),t=!0},p(r,[o]){const l=o&3?gt(n,[n[0],o&2&&_n(r[1]),o&1&&{iconNode:r[0]}]):{};o&8&&(l.$$scope={dirty:o,ctx:r}),e.$set(l)},i(r){t||(B(e.$$.fragment,r),t=!0)},o(r){Y(e.$$.fragment,r),t=!1},d(r){Te(e,r)}}}function qs(i,e,t){let{$$slots:n={},$$scope:s}=e;const r=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"}],["path",{d:"m21.854 2.147-10.94 10.939"}]];return i.$$set=o=>{t(1,e=ie(ie({},e),Re(o))),"$$scope"in o&&t(3,s=o.$$scope)},e=Re(e),[r,e,n,s]}class Zs extends Ke{constructor(e){super(),Qe(this,e,qs,js,Oe,{})}}const Ys=Zs;function Xs(i){let e;const t=i[2].default,n=Ft(t,i,i[3],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||r&8)&&Bt(n,t,s,s[3],e?Vt(t,s[3],r,null):Ht(s[3]),null)},i(s){e||(B(n,s),e=!0)},o(s){Y(n,s),e=!1},d(s){n&&n.d(s)}}}function Qs(i){let e,t;const n=[{name:"x"},i[1],{iconNode:i[0]}];let s={$$slots:{default:[Xs]},$$scope:{ctx:i}};for(let r=0;r<n.length;r+=1)s=ie(s,n[r]);return e=new bn({props:s}),{c(){Ce(e.$$.fragment)},m(r,o){ye(e,r,o),t=!0},p(r,[o]){const l=o&3?gt(n,[n[0],o&2&&_n(r[1]),o&1&&{iconNode:r[0]}]):{};o&8&&(l.$$scope={dirty:o,ctx:r}),e.$set(l)},i(r){t||(B(e.$$.fragment,r),t=!0)},o(r){Y(e.$$.fragment,r),t=!1},d(r){Te(e,r)}}}function Ks(i,e,t){let{$$slots:n={},$$scope:s}=e;const r=[["path",{d:"M18 6 6 18"}],["path",{d:"m6 6 12 12"}]];return i.$$set=o=>{t(1,e=ie(ie({},e),Re(o))),"$$scope"in o&&t(3,s=o.$$scope)},e=Re(e),[r,e,n,s]}class Js extends Ke{constructor(e){super(),Qe(this,e,Ks,Qs,Oe,{})}}const er=Js;function wn(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Me=wn();function Gi(i){Me=i}const ht={exec:()=>null};function R(i,e=""){let t=typeof i=="string"?i:i.source;const n={replace:(s,r)=>{let o=typeof r=="string"?r:r.source;return o=o.replace(J.caret,"$1"),t=t.replace(s,o),n},getRegex:()=>new RegExp(t,e)};return n}const J={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:i=>new RegExp(`^( {0,3}${i})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}#`),htmlBeginRegex:i=>new RegExp(`^ {0,${Math.min(3,i-1)}}<(?:[a-z].*>|!--)`,"i")},tr=/^(?:[ \t]*(?:\n|$))+/,nr=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ir=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,mt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,sr=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Wi=/(?:[*+-]|\d{1,9}[.)])/,ji=R(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Wi).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),yn=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,rr=/^[^\n]+/,Tn=/(?!\s*\])(?:\\.|[^\[\]\\])+/,or=R(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Tn).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),lr=R(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Wi).getRegex(),Gt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",xn=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ar=R("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",xn).replace("tag",Gt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),qi=R(yn).replace("hr",mt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Gt).getRegex(),cr=R(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",qi).getRegex(),En={blockquote:cr,code:nr,def:or,fences:ir,heading:sr,hr:mt,html:ar,lheading:ji,list:lr,newline:tr,paragraph:qi,table:ht,text:rr},ii=R("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",mt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Gt).getRegex(),ur={...En,table:ii,paragraph:R(yn).replace("hr",mt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ii).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Gt).getRegex()},fr={...En,html:R(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",xn).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ht,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:R(yn).replace("hr",mt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ji).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},hr=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,pr=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Zi=/^( {2,}|\\)\n(?!\s*$)/,dr=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Wt=/[\p{P}\p{S}]/u,Sn=/[\s\p{P}\p{S}]/u,Yi=/[^\s\p{P}\p{S}]/u,gr=R(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Sn).getRegex(),Xi=/(?!~)[\p{P}\p{S}]/u,mr=/(?!~)[\s\p{P}\p{S}]/u,Ar=/(?:[^\s\p{P}\p{S}]|~)/u,_r=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Qi=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,kr=R(Qi,"u").replace(/punct/g,Wt).getRegex(),br=R(Qi,"u").replace(/punct/g,Xi).getRegex(),Ki="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",wr=R(Ki,"gu").replace(/notPunctSpace/g,Yi).replace(/punctSpace/g,Sn).replace(/punct/g,Wt).getRegex(),yr=R(Ki,"gu").replace(/notPunctSpace/g,Ar).replace(/punctSpace/g,mr).replace(/punct/g,Xi).getRegex(),Tr=R("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Yi).replace(/punctSpace/g,Sn).replace(/punct/g,Wt).getRegex(),xr=R(/\\(punct)/,"gu").replace(/punct/g,Wt).getRegex(),Er=R(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Sr=R(xn).replace("(?:-->|$)","-->").getRegex(),Rr=R("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Sr).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Pt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Cr=R(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Pt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ji=R(/^!?\[(label)\]\[(ref)\]/).replace("label",Pt).replace("ref",Tn).getRegex(),es=R(/^!?\[(ref)\](?:\[\])?/).replace("ref",Tn).getRegex(),vr=R("reflink|nolink(?!\\()","g").replace("reflink",Ji).replace("nolink",es).getRegex(),Rn={_backpedal:ht,anyPunctuation:xr,autolink:Er,blockSkip:_r,br:Zi,code:pr,del:ht,emStrongLDelim:kr,emStrongRDelimAst:wr,emStrongRDelimUnd:Tr,escape:hr,link:Cr,nolink:es,punctuation:gr,reflink:Ji,reflinkSearch:vr,tag:Rr,text:dr,url:ht},Lr={...Rn,link:R(/^!?\[(label)\]\((.*?)\)/).replace("label",Pt).getRegex(),reflink:R(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Pt).getRegex()},hn={...Rn,emStrongRDelimAst:yr,emStrongLDelim:br,url:R(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ir={...hn,br:R(Zi).replace("{2,}","*").getRegex(),text:R(hn.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Rt={normal:En,gfm:ur,pedantic:fr},st={normal:Rn,gfm:hn,breaks:Ir,pedantic:Lr},Nr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},si=i=>Nr[i];function me(i,e){if(e){if(J.escapeTest.test(i))return i.replace(J.escapeReplace,si)}else if(J.escapeTestNoEncode.test(i))return i.replace(J.escapeReplaceNoEncode,si);return i}function ri(i){try{i=encodeURI(i).replace(J.percentDecode,"%")}catch{return null}return i}function oi(i,e){var r;const t=i.replace(J.findPipe,(o,l,u)=>{let a=!1,f=l;for(;--f>=0&&u[f]==="\\";)a=!a;return a?"|":" |"}),n=t.split(J.splitPipe);let s=0;if(n[0].trim()||n.shift(),n.length>0&&!((r=n.at(-1))!=null&&r.trim())&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;s<n.length;s++)n[s]=n[s].trim().replace(J.slashPipe,"|");return n}function rt(i,e,t){const n=i.length;if(n===0)return"";let s=0;for(;s<n&&i.charAt(n-s-1)===e;)s++;return i.slice(0,n-s)}function Or(i,e){if(i.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<i.length;n++)if(i[n]==="\\")n++;else if(i[n]===e[0])t++;else if(i[n]===e[1]&&(t--,t<0))return n;return-1}function li(i,e,t,n,s){const r=e.href,o=e.title||null,l=i[1].replace(s.other.outputLinkReplace,"$1");if(i[0].charAt(0)!=="!"){n.state.inLink=!0;const u={type:"link",raw:t,href:r,title:o,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,u}return{type:"image",raw:t,href:r,title:o,text:l}}function Mr(i,e,t){const n=i.match(t.other.indentCodeCompensation);if(n===null)return e;const s=n[1];return e.split(`
`).map(r=>{const o=r.match(t.other.beginningSpace);if(o===null)return r;const[l]=o;return l.length>=s.length?r.slice(s.length):r}).join(`
`)}class $t{constructor(e){v(this,"options");v(this,"rules");v(this,"lexer");this.options=e||Me}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:rt(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=Mr(n,t[3]||"",this.rules);return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){const s=rt(n,"#");(this.options.pedantic||!s||this.rules.other.endingSpaceChar.test(s))&&(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:rt(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let n=rt(t[0],`
`).split(`
`),s="",r="";const o=[];for(;n.length>0;){let l=!1;const u=[];let a;for(a=0;a<n.length;a++)if(this.rules.other.blockquoteStart.test(n[a]))u.push(n[a]),l=!0;else if(!l)u.push(n[a]);else break;n=n.slice(a);const f=u.join(`
`),g=f.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");s=s?`${s}
${f}`:f,r=r?`${r}
${g}`:g;const h=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(g,o,!0),this.lexer.state.top=h,n.length===0)break;const d=o.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){const p=d,_=p.raw+`
`+n.join(`
`),w=this.blockquote(_);o[o.length-1]=w,s=s.substring(0,s.length-p.raw.length)+w.raw,r=r.substring(0,r.length-p.text.length)+w.text;break}else if((d==null?void 0:d.type)==="list"){const p=d,_=p.raw+`
`+n.join(`
`),w=this.list(_);o[o.length-1]=w,s=s.substring(0,s.length-d.raw.length)+w.raw,r=r.substring(0,r.length-p.raw.length)+w.raw,n=_.substring(o.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:s,tokens:o,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,r={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=this.rules.other.listItemRegex(n);let l=!1;for(;e;){let a=!1,f="",g="";if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;f=t[0],e=e.substring(f.length);let h=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,V=>" ".repeat(3*V.length)),d=e.split(`
`,1)[0],p=!h.trim(),_=0;if(this.options.pedantic?(_=2,g=h.trimStart()):p?_=t[1].length+1:(_=t[2].search(this.rules.other.nonSpaceChar),_=_>4?1:_,g=h.slice(_),_+=t[1].length),p&&this.rules.other.blankLine.test(d)&&(f+=d+`
`,e=e.substring(d.length+1),a=!0),!a){const V=this.rules.other.nextBulletRegex(_),O=this.rules.other.hrRegex(_),C=this.rules.other.fencesBeginRegex(_),X=this.rules.other.headingBeginRegex(_),re=this.rules.other.htmlBeginRegex(_);for(;e;){const S=e.split(`
`,1)[0];let L;if(d=S,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),L=d):L=d.replace(this.rules.other.tabCharGlobal,"    "),C.test(d)||X.test(d)||re.test(d)||V.test(d)||O.test(d))break;if(L.search(this.rules.other.nonSpaceChar)>=_||!d.trim())g+=`
`+L.slice(_);else{if(p||h.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||C.test(h)||X.test(h)||O.test(h))break;g+=`
`+d}!p&&!d.trim()&&(p=!0),f+=S+`
`,e=e.substring(S.length+1),h=L.slice(_)}}r.loose||(l?r.loose=!0:this.rules.other.doubleBlankLine.test(f)&&(l=!0));let w=null,y;this.options.gfm&&(w=this.rules.other.listIsTask.exec(g),w&&(y=w[0]!=="[ ] ",g=g.replace(this.rules.other.listReplaceTask,""))),r.items.push({type:"list_item",raw:f,task:!!w,checked:y,loose:!1,text:g,tokens:[]}),r.raw+=f}const u=r.items.at(-1);if(u)u.raw=u.raw.trimEnd(),u.text=u.text.trimEnd();else return;r.raw=r.raw.trimEnd();for(let a=0;a<r.items.length;a++)if(this.lexer.state.top=!1,r.items[a].tokens=this.lexer.blockTokens(r.items[a].text,[]),!r.loose){const f=r.items[a].tokens.filter(h=>h.type==="space"),g=f.length>0&&f.some(h=>this.rules.other.anyLine.test(h.raw));r.loose=g}if(r.loose)for(let a=0;a<r.items.length;a++)r.items[a].loose=!0;return r}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),s=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:r}}}table(e){var l;const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const n=oi(t[1]),s=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),r=(l=t[3])!=null&&l.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const u of s)this.rules.other.tableAlignRight.test(u)?o.align.push("right"):this.rules.other.tableAlignCenter.test(u)?o.align.push("center"):this.rules.other.tableAlignLeft.test(u)?o.align.push("left"):o.align.push(null);for(let u=0;u<n.length;u++)o.header.push({text:n[u],tokens:this.lexer.inline(n[u]),header:!0,align:o.align[u]});for(const u of r)o.rows.push(oi(u,o.header.length).map((a,f)=>({text:a,tokens:this.lexer.inline(a),header:!1,align:o.align[f]})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;const o=rt(n.slice(0,-1),"\\");if((n.length-o.length)%2===0)return}else{const o=Or(t[2],"()");if(o>-1){const u=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,u).trim(),t[3]=""}}let s=t[2],r="";if(this.options.pedantic){const o=this.rules.other.pedanticHrefTitle.exec(s);o&&(s=o[1],r=o[3])}else r=t[3]?t[3].slice(1,-1):"";return s=s.trim(),this.rules.other.startAngleBracket.test(s)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?s=s.slice(1):s=s.slice(1,-1)),li(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),r=t[s.toLowerCase()];if(!r){const o=n[0].charAt(0);return{type:"text",raw:o,text:o}}return li(n,r,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(!s||s[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;if(!(s[1]||s[2]||"")||!n||this.rules.inline.punctuation.exec(n)){const o=[...s[0]].length-1;let l,u,a=o,f=0;const g=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(g.lastIndex=0,t=t.slice(-1*e.length+o);(s=g.exec(t))!=null;){if(l=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!l)continue;if(u=[...l].length,s[3]||s[4]){a+=u;continue}else if((s[5]||s[6])&&o%3&&!((o+u)%3)){f+=u;continue}if(a-=u,a>0)continue;u=Math.min(u,u+a+f);const h=[...s[0]][0].length,d=e.slice(0,o+s.index+h+u);if(Math.min(o,u)%2){const _=d.slice(1,-1);return{type:"em",raw:d,text:_,tokens:this.lexer.inlineTokens(_)}}const p=d.slice(2,-2);return{type:"strong",raw:d,text:p,tokens:this.lexer.inlineTokens(p)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal," ");const s=this.rules.other.nonSpaceChar.test(n),r=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return s&&r&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=t[1],s="mailto:"+n):(n=t[1],s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let s,r;if(t[2]==="@")s=t[0],r="mailto:"+s;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);s=t[0],t[1]==="www."?r="http://"+t[0]:r=t[0]}return{type:"link",raw:t[0],text:s,href:r,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const n=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:n}}}}class oe{constructor(e){v(this,"tokens");v(this,"options");v(this,"state");v(this,"tokenizer");v(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Me,this.options.tokenizer=this.options.tokenizer||new $t,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:J,block:Rt.normal,inline:st.normal};this.options.pedantic?(t.block=Rt.pedantic,t.inline=st.pedantic):this.options.gfm&&(t.block=Rt.gfm,this.options.breaks?t.inline=st.breaks:t.inline=st.gfm),this.tokenizer.rules=t}static get rules(){return{block:Rt,inline:st}}static lex(e,t){return new oe(t).lex(e)}static lexInline(e,t){return new oe(t).inlineTokens(e)}lex(e){e=e.replace(J.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){var s,r,o;for(this.options.pedantic&&(e=e.replace(J.tabCharGlobal,"    ").replace(J.spaceLine,""));e;){let l;if((r=(s=this.options.extensions)==null?void 0:s.block)!=null&&r.some(a=>(l=a.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const a=t.at(-1);l.raw.length===1&&a!==void 0?a.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.at(-1).src=a.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.raw,this.inlineQueue.at(-1).src=a.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let u=e;if((o=this.options.extensions)!=null&&o.startBlock){let a=1/0;const f=e.slice(1);let g;this.options.extensions.startBlock.forEach(h=>{g=h.call({lexer:this},f),typeof g=="number"&&g>=0&&(a=Math.min(a,g))}),a<1/0&&a>=0&&(u=e.substring(0,a+1))}if(this.state.top&&(l=this.tokenizer.paragraph(u))){const a=t.at(-1);n&&(a==null?void 0:a.type)==="paragraph"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):t.push(l),n=u.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):t.push(l);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,u,a;let n=e,s=null;if(this.tokens.links){const f=Object.keys(this.tokens.links);if(f.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)f.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,s.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let r=!1,o="";for(;e;){r||(o=""),r=!1;let f;if((u=(l=this.options.extensions)==null?void 0:l.inline)!=null&&u.some(h=>(f=h.call({lexer:this},e,t))?(e=e.substring(f.raw.length),t.push(f),!0):!1))continue;if(f=this.tokenizer.escape(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.tag(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.link(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(f.raw.length);const h=t.at(-1);f.type==="text"&&(h==null?void 0:h.type)==="text"?(h.raw+=f.raw,h.text+=f.text):t.push(f);continue}if(f=this.tokenizer.emStrong(e,n,o)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.codespan(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.br(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.del(e)){e=e.substring(f.raw.length),t.push(f);continue}if(f=this.tokenizer.autolink(e)){e=e.substring(f.raw.length),t.push(f);continue}if(!this.state.inLink&&(f=this.tokenizer.url(e))){e=e.substring(f.raw.length),t.push(f);continue}let g=e;if((a=this.options.extensions)!=null&&a.startInline){let h=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(_=>{p=_.call({lexer:this},d),typeof p=="number"&&p>=0&&(h=Math.min(h,p))}),h<1/0&&h>=0&&(g=e.substring(0,h+1))}if(f=this.tokenizer.inlineText(g)){e=e.substring(f.raw.length),f.raw.slice(-1)!=="_"&&(o=f.raw.slice(-1)),r=!0;const h=t.at(-1);(h==null?void 0:h.type)==="text"?(h.raw+=f.raw,h.text+=f.text):t.push(f);continue}if(e){const h="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(h);break}else throw new Error(h)}}return t}}class zt{constructor(e){v(this,"options");v(this,"parser");this.options=e||Me}space(e){return""}code({text:e,lang:t,escaped:n}){var o;const s=(o=(t||"").match(J.notSpaceStart))==null?void 0:o[0],r=e.replace(J.endingNewline,"")+`
`;return s?'<pre><code class="language-'+me(s)+'">'+(n?r:me(r,!0))+`</code></pre>
`:"<pre><code>"+(n?r:me(r,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,n=e.start;let s="";for(let l=0;l<e.items.length;l++){const u=e.items[l];s+=this.listitem(u)}const r=t?"ol":"ul",o=t&&n!==1?' start="'+n+'"':"";return"<"+r+o+`>
`+s+"</"+r+`>
`}listitem(e){var n;let t="";if(e.task){const s=this.checkbox({checked:!!e.checked});e.loose?((n=e.tokens[0])==null?void 0:n.type)==="paragraph"?(e.tokens[0].text=s+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=s+" "+me(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:s+" ",text:s+" ",escaped:!0}):t+=s+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let r=0;r<e.header.length;r++)n+=this.tablecell(e.header[r]);t+=this.tablerow({text:n});let s="";for(let r=0;r<e.rows.length;r++){const o=e.rows[r];n="";for(let l=0;l<o.length;l++)n+=this.tablecell(o[l]);s+=this.tablerow({text:n})}return s&&(s=`<tbody>${s}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+s+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${me(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const s=this.parser.parseInline(n),r=ri(e);if(r===null)return s;e=r;let o='<a href="'+e+'"';return t&&(o+=' title="'+me(t)+'"'),o+=">"+s+"</a>",o}image({href:e,title:t,text:n}){const s=ri(e);if(s===null)return me(n);e=s;let r=`<img src="${e}" alt="${n}"`;return t&&(r+=` title="${me(t)}"`),r+=">",r}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:me(e.text)}}class Cn{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class le{constructor(e){v(this,"options");v(this,"renderer");v(this,"textRenderer");this.options=e||Me,this.options.renderer=this.options.renderer||new zt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Cn}static parse(e,t){return new le(t).parse(e)}static parseInline(e,t){return new le(t).parseInline(e)}parse(e,t=!0){var s,r;let n="";for(let o=0;o<e.length;o++){const l=e[o];if((r=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&r[l.type]){const a=l,f=this.options.extensions.renderers[a.type].call({parser:this},a);if(f!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(a.type)){n+=f||"";continue}}const u=l;switch(u.type){case"space":{n+=this.renderer.space(u);continue}case"hr":{n+=this.renderer.hr(u);continue}case"heading":{n+=this.renderer.heading(u);continue}case"code":{n+=this.renderer.code(u);continue}case"table":{n+=this.renderer.table(u);continue}case"blockquote":{n+=this.renderer.blockquote(u);continue}case"list":{n+=this.renderer.list(u);continue}case"html":{n+=this.renderer.html(u);continue}case"paragraph":{n+=this.renderer.paragraph(u);continue}case"text":{let a=u,f=this.renderer.text(a);for(;o+1<e.length&&e[o+1].type==="text";)a=e[++o],f+=`
`+this.renderer.text(a);t?n+=this.renderer.paragraph({type:"paragraph",raw:f,text:f,tokens:[{type:"text",raw:f,text:f,escaped:!0}]}):n+=f;continue}default:{const a='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return n}parseInline(e,t=this.renderer){var s,r;let n="";for(let o=0;o<e.length;o++){const l=e[o];if((r=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&r[l.type]){const a=this.options.extensions.renderers[l.type].call({parser:this},l);if(a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){n+=a||"";continue}}const u=l;switch(u.type){case"escape":{n+=t.text(u);break}case"html":{n+=t.html(u);break}case"link":{n+=t.link(u);break}case"image":{n+=t.image(u);break}case"strong":{n+=t.strong(u);break}case"em":{n+=t.em(u);break}case"codespan":{n+=t.codespan(u);break}case"br":{n+=t.br(u);break}case"del":{n+=t.del(u);break}case"text":{n+=t.text(u);break}default:{const a='Token with "'+u.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return n}}class pt{constructor(e){v(this,"options");v(this,"block");this.options=e||Me}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?oe.lex:oe.lexInline}provideParser(){return this.block?le.parse:le.parseInline}}v(pt,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class Dr{constructor(...e){v(this,"defaults",wn());v(this,"options",this.setOptions);v(this,"parse",this.parseMarkdown(!0));v(this,"parseInline",this.parseMarkdown(!1));v(this,"Parser",le);v(this,"Renderer",zt);v(this,"TextRenderer",Cn);v(this,"Lexer",oe);v(this,"Tokenizer",$t);v(this,"Hooks",pt);this.use(...e)}walkTokens(e,t){var s,r;let n=[];for(const o of e)switch(n=n.concat(t.call(this,o)),o.type){case"table":{const l=o;for(const u of l.header)n=n.concat(this.walkTokens(u.tokens,t));for(const u of l.rows)for(const a of u)n=n.concat(this.walkTokens(a.tokens,t));break}case"list":{const l=o;n=n.concat(this.walkTokens(l.items,t));break}default:{const l=o;(r=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&r[l.type]?this.defaults.extensions.childTokens[l.type].forEach(u=>{const a=l[u].flat(1/0);n=n.concat(this.walkTokens(a,t))}):l.tokens&&(n=n.concat(this.walkTokens(l.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{const s={...n};if(s.async=this.defaults.async||s.async||!1,n.extensions&&(n.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const o=t.renderers[r.name];o?t.renderers[r.name]=function(...l){let u=r.renderer.apply(this,l);return u===!1&&(u=o.apply(this,l)),u}:t.renderers[r.name]=r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=t[r.level];o?o.unshift(r.tokenizer):t[r.level]=[r.tokenizer],r.start&&(r.level==="block"?t.startBlock?t.startBlock.push(r.start):t.startBlock=[r.start]:r.level==="inline"&&(t.startInline?t.startInline.push(r.start):t.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(t.childTokens[r.name]=r.childTokens)}),s.extensions=t),n.renderer){const r=this.defaults.renderer||new zt(this.defaults);for(const o in n.renderer){if(!(o in r))throw new Error(`renderer '${o}' does not exist`);if(["options","parser"].includes(o))continue;const l=o,u=n.renderer[l],a=r[l];r[l]=(...f)=>{let g=u.apply(r,f);return g===!1&&(g=a.apply(r,f)),g||""}}s.renderer=r}if(n.tokenizer){const r=this.defaults.tokenizer||new $t(this.defaults);for(const o in n.tokenizer){if(!(o in r))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const l=o,u=n.tokenizer[l],a=r[l];r[l]=(...f)=>{let g=u.apply(r,f);return g===!1&&(g=a.apply(r,f)),g}}s.tokenizer=r}if(n.hooks){const r=this.defaults.hooks||new pt;for(const o in n.hooks){if(!(o in r))throw new Error(`hook '${o}' does not exist`);if(["options","block"].includes(o))continue;const l=o,u=n.hooks[l],a=r[l];pt.passThroughHooks.has(o)?r[l]=f=>{if(this.defaults.async)return Promise.resolve(u.call(r,f)).then(h=>a.call(r,h));const g=u.call(r,f);return a.call(r,g)}:r[l]=(...f)=>{let g=u.apply(r,f);return g===!1&&(g=a.apply(r,f)),g}}s.hooks=r}if(n.walkTokens){const r=this.defaults.walkTokens,o=n.walkTokens;s.walkTokens=function(l){let u=[];return u.push(o.call(this,l)),r&&(u=u.concat(r.call(this,l))),u}}this.defaults={...this.defaults,...s}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return oe.lex(e,t??this.defaults)}parser(e,t){return le.parse(e,t??this.defaults)}parseMarkdown(e){return(n,s)=>{const r={...s},o={...this.defaults,...r},l=this.onError(!!o.silent,!!o.async);if(this.defaults.async===!0&&r.async===!1)return l(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof n>"u"||n===null)return l(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=e);const u=o.hooks?o.hooks.provideLexer():e?oe.lex:oe.lexInline,a=o.hooks?o.hooks.provideParser():e?le.parse:le.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(n):n).then(f=>u(f,o)).then(f=>o.hooks?o.hooks.processAllTokens(f):f).then(f=>o.walkTokens?Promise.all(this.walkTokens(f,o.walkTokens)).then(()=>f):f).then(f=>a(f,o)).then(f=>o.hooks?o.hooks.postprocess(f):f).catch(l);try{o.hooks&&(n=o.hooks.preprocess(n));let f=u(n,o);o.hooks&&(f=o.hooks.processAllTokens(f)),o.walkTokens&&this.walkTokens(f,o.walkTokens);let g=a(f,o);return o.hooks&&(g=o.hooks.postprocess(g)),g}catch(f){return l(f)}}}onError(e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const s="<p>An error occurred:</p><pre>"+me(n.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(n);throw n}}}const Ne=new Dr;function E(i,e){return Ne.parse(i,e)}E.options=E.setOptions=function(i){return Ne.setOptions(i),E.defaults=Ne.defaults,Gi(E.defaults),E};E.getDefaults=wn;E.defaults=Me;E.use=function(...i){return Ne.use(...i),E.defaults=Ne.defaults,Gi(E.defaults),E};E.walkTokens=function(i,e){return Ne.walkTokens(i,e)};E.parseInline=Ne.parseInline;E.Parser=le;E.parser=le.parse;E.Renderer=zt;E.TextRenderer=Cn;E.Lexer=oe;E.lexer=oe.lex;E.Tokenizer=$t;E.Hooks=pt;E.parse=E;E.options;E.setOptions;E.use;E.walkTokens;E.parseInline;le.parse;oe.lex;/*! @license DOMPurify 3.2.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.3/LICENSE */const{entries:ts,setPrototypeOf:ai,isFrozen:Pr,getPrototypeOf:$r,getOwnPropertyDescriptor:zr}=Object;let{freeze:ee,seal:ae,create:ns}=Object,{apply:pn,construct:dn}=typeof Reflect<"u"&&Reflect;ee||(ee=function(e){return e});ae||(ae=function(e){return e});pn||(pn=function(e,t,n){return e.apply(t,n)});dn||(dn=function(e,t){return new e(...t)});const Ct=se(Array.prototype.forEach),ci=se(Array.prototype.pop),ot=se(Array.prototype.push),It=se(String.prototype.toLowerCase),sn=se(String.prototype.toString),ui=se(String.prototype.match),lt=se(String.prototype.replace),Ur=se(String.prototype.indexOf),Fr=se(String.prototype.trim),ue=se(Object.prototype.hasOwnProperty),K=se(RegExp.prototype.test),at=Vr(TypeError);function se(i){return function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return pn(i,e,n)}}function Vr(i){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return dn(i,t)}}function T(i,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:It;ai&&ai(i,null);let n=e.length;for(;n--;){let s=e[n];if(typeof s=="string"){const r=t(s);r!==s&&(Pr(e)||(e[n]=r),s=r)}i[s]=!0}return i}function Br(i){for(let e=0;e<i.length;e++)ue(i,e)||(i[e]=null);return i}function Ie(i){const e=ns(null);for(const[t,n]of ts(i))ue(i,t)&&(Array.isArray(n)?e[t]=Br(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=Ie(n):e[t]=n);return e}function ct(i,e){for(;i!==null;){const n=zr(i,e);if(n){if(n.get)return se(n.get);if(typeof n.value=="function")return se(n.value)}i=$r(i)}function t(){return null}return t}const fi=ee(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),rn=ee(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),on=ee(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Hr=ee(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ln=ee(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Gr=ee(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),hi=ee(["#text"]),pi=ee(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),an=ee(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),di=ee(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),vt=ee(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Wr=ae(/\{\{[\w\W]*|[\w\W]*\}\}/gm),jr=ae(/<%[\w\W]*|[\w\W]*%>/gm),qr=ae(/\$\{[\w\W]*}/gm),Zr=ae(/^data-[\-\w.\u00B7-\uFFFF]+$/),Yr=ae(/^aria-[\-\w]+$/),is=ae(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Xr=ae(/^(?:\w+script|data):/i),Qr=ae(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ss=ae(/^html$/i),Kr=ae(/^[a-z][.\w]*(-[.\w]+)+$/i);var gi=Object.freeze({__proto__:null,ARIA_ATTR:Yr,ATTR_WHITESPACE:Qr,CUSTOM_ELEMENT:Kr,DATA_ATTR:Zr,DOCTYPE_NAME:ss,ERB_EXPR:jr,IS_ALLOWED_URI:is,IS_SCRIPT_OR_DATA:Xr,MUSTACHE_EXPR:Wr,TMPLIT_EXPR:qr});const ut={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Jr=function(){return typeof window>"u"?null:window},eo=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const s="data-tt-policy-suffix";t&&t.hasAttribute(s)&&(n=t.getAttribute(s));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+r+" could not be created."),null}},mi=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function rs(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Jr();const e=k=>rs(k);if(e.version="3.2.3",e.removed=[],!i||!i.document||i.document.nodeType!==ut.document)return e.isSupported=!1,e;let{document:t}=i;const n=t,s=n.currentScript,{DocumentFragment:r,HTMLTemplateElement:o,Node:l,Element:u,NodeFilter:a,NamedNodeMap:f=i.NamedNodeMap||i.MozNamedAttrMap,HTMLFormElement:g,DOMParser:h,trustedTypes:d}=i,p=u.prototype,_=ct(p,"cloneNode"),w=ct(p,"remove"),y=ct(p,"nextSibling"),V=ct(p,"childNodes"),O=ct(p,"parentNode");if(typeof o=="function"){const k=t.createElement("template");k.content&&k.content.ownerDocument&&(t=k.content.ownerDocument)}let C,X="";const{implementation:re,createNodeIterator:S,createDocumentFragment:L,getElementsByTagName:M}=t,{importNode:_e}=n;let P=mi();e.isSupported=typeof ts=="function"&&typeof O=="function"&&re&&re.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:fe,ERB_EXPR:De,TMPLIT_EXPR:xe,DATA_ATTR:Pe,ARIA_ATTR:$e,IS_SCRIPT_OR_DATA:he,ATTR_WHITESPACE:ze,CUSTOM_ELEMENT:At}=gi;let{IS_ALLOWED_URI:G}=gi,z=null;const pe=T({},[...fi,...rn,...on,...ln,...hi]);let F=null;const W=T({},[...pi,...an,...di,...vt]);let I=Object.seal(ns(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ee=null,j=null,b=!0,$=!0,te=!1,vn=!0,Ue=!1,jt=!0,Le=!1,qt=!1,Zt=!1,Fe=!1,_t=!1,kt=!1,Ln=!0,In=!1;const ls="user-content-";let Yt=!0,Je=!1,Ve={},Be=null;const Nn=T({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let On=null;const Mn=T({},["audio","video","img","source","image","track"]);let Xt=null;const Dn=T({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),bt="http://www.w3.org/1998/Math/MathML",wt="http://www.w3.org/2000/svg",ke="http://www.w3.org/1999/xhtml";let He=ke,Qt=!1,Kt=null;const as=T({},[bt,wt,ke],sn);let yt=T({},["mi","mo","mn","ms","mtext"]),Tt=T({},["annotation-xml"]);const cs=T({},["title","style","font","a","script"]);let et=null;const us=["application/xhtml+xml","text/html"],fs="text/html";let H=null,Ge=null;const hs=t.createElement("form"),Pn=function(c){return c instanceof RegExp||c instanceof Function},Jt=function(){let c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Ge&&Ge===c)){if((!c||typeof c!="object")&&(c={}),c=Ie(c),et=us.indexOf(c.PARSER_MEDIA_TYPE)===-1?fs:c.PARSER_MEDIA_TYPE,H=et==="application/xhtml+xml"?sn:It,z=ue(c,"ALLOWED_TAGS")?T({},c.ALLOWED_TAGS,H):pe,F=ue(c,"ALLOWED_ATTR")?T({},c.ALLOWED_ATTR,H):W,Kt=ue(c,"ALLOWED_NAMESPACES")?T({},c.ALLOWED_NAMESPACES,sn):as,Xt=ue(c,"ADD_URI_SAFE_ATTR")?T(Ie(Dn),c.ADD_URI_SAFE_ATTR,H):Dn,On=ue(c,"ADD_DATA_URI_TAGS")?T(Ie(Mn),c.ADD_DATA_URI_TAGS,H):Mn,Be=ue(c,"FORBID_CONTENTS")?T({},c.FORBID_CONTENTS,H):Nn,Ee=ue(c,"FORBID_TAGS")?T({},c.FORBID_TAGS,H):{},j=ue(c,"FORBID_ATTR")?T({},c.FORBID_ATTR,H):{},Ve=ue(c,"USE_PROFILES")?c.USE_PROFILES:!1,b=c.ALLOW_ARIA_ATTR!==!1,$=c.ALLOW_DATA_ATTR!==!1,te=c.ALLOW_UNKNOWN_PROTOCOLS||!1,vn=c.ALLOW_SELF_CLOSE_IN_ATTR!==!1,Ue=c.SAFE_FOR_TEMPLATES||!1,jt=c.SAFE_FOR_XML!==!1,Le=c.WHOLE_DOCUMENT||!1,Fe=c.RETURN_DOM||!1,_t=c.RETURN_DOM_FRAGMENT||!1,kt=c.RETURN_TRUSTED_TYPE||!1,Zt=c.FORCE_BODY||!1,Ln=c.SANITIZE_DOM!==!1,In=c.SANITIZE_NAMED_PROPS||!1,Yt=c.KEEP_CONTENT!==!1,Je=c.IN_PLACE||!1,G=c.ALLOWED_URI_REGEXP||is,He=c.NAMESPACE||ke,yt=c.MATHML_TEXT_INTEGRATION_POINTS||yt,Tt=c.HTML_INTEGRATION_POINTS||Tt,I=c.CUSTOM_ELEMENT_HANDLING||{},c.CUSTOM_ELEMENT_HANDLING&&Pn(c.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(I.tagNameCheck=c.CUSTOM_ELEMENT_HANDLING.tagNameCheck),c.CUSTOM_ELEMENT_HANDLING&&Pn(c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(I.attributeNameCheck=c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),c.CUSTOM_ELEMENT_HANDLING&&typeof c.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(I.allowCustomizedBuiltInElements=c.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ue&&($=!1),_t&&(Fe=!0),Ve&&(z=T({},hi),F=[],Ve.html===!0&&(T(z,fi),T(F,pi)),Ve.svg===!0&&(T(z,rn),T(F,an),T(F,vt)),Ve.svgFilters===!0&&(T(z,on),T(F,an),T(F,vt)),Ve.mathMl===!0&&(T(z,ln),T(F,di),T(F,vt))),c.ADD_TAGS&&(z===pe&&(z=Ie(z)),T(z,c.ADD_TAGS,H)),c.ADD_ATTR&&(F===W&&(F=Ie(F)),T(F,c.ADD_ATTR,H)),c.ADD_URI_SAFE_ATTR&&T(Xt,c.ADD_URI_SAFE_ATTR,H),c.FORBID_CONTENTS&&(Be===Nn&&(Be=Ie(Be)),T(Be,c.FORBID_CONTENTS,H)),Yt&&(z["#text"]=!0),Le&&T(z,["html","head","body"]),z.table&&(T(z,["tbody"]),delete Ee.tbody),c.TRUSTED_TYPES_POLICY){if(typeof c.TRUSTED_TYPES_POLICY.createHTML!="function")throw at('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof c.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw at('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');C=c.TRUSTED_TYPES_POLICY,X=C.createHTML("")}else C===void 0&&(C=eo(d,s)),C!==null&&typeof X=="string"&&(X=C.createHTML(""));ee&&ee(c),Ge=c}},$n=T({},[...rn,...on,...Hr]),zn=T({},[...ln,...Gr]),ps=function(c){let m=O(c);(!m||!m.tagName)&&(m={namespaceURI:He,tagName:"template"});const A=It(c.tagName),D=It(m.tagName);return Kt[c.namespaceURI]?c.namespaceURI===wt?m.namespaceURI===ke?A==="svg":m.namespaceURI===bt?A==="svg"&&(D==="annotation-xml"||yt[D]):!!$n[A]:c.namespaceURI===bt?m.namespaceURI===ke?A==="math":m.namespaceURI===wt?A==="math"&&Tt[D]:!!zn[A]:c.namespaceURI===ke?m.namespaceURI===wt&&!Tt[D]||m.namespaceURI===bt&&!yt[D]?!1:!zn[A]&&(cs[A]||!$n[A]):!!(et==="application/xhtml+xml"&&Kt[c.namespaceURI]):!1},de=function(c){ot(e.removed,{element:c});try{O(c).removeChild(c)}catch{w(c)}},xt=function(c,m){try{ot(e.removed,{attribute:m.getAttributeNode(c),from:m})}catch{ot(e.removed,{attribute:null,from:m})}if(m.removeAttribute(c),c==="is")if(Fe||_t)try{de(m)}catch{}else try{m.setAttribute(c,"")}catch{}},Un=function(c){let m=null,A=null;if(Zt)c="<remove></remove>"+c;else{const q=ui(c,/^[\r\n\t ]+/);A=q&&q[0]}et==="application/xhtml+xml"&&He===ke&&(c='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+c+"</body></html>");const D=C?C.createHTML(c):c;if(He===ke)try{m=new h().parseFromString(D,et)}catch{}if(!m||!m.documentElement){m=re.createDocument(He,"template",null);try{m.documentElement.innerHTML=Qt?X:D}catch{}}const Z=m.body||m.documentElement;return c&&A&&Z.insertBefore(t.createTextNode(A),Z.childNodes[0]||null),He===ke?M.call(m,Le?"html":"body")[0]:Le?m.documentElement:Z},Fn=function(c){return S.call(c.ownerDocument||c,c,a.SHOW_ELEMENT|a.SHOW_COMMENT|a.SHOW_TEXT|a.SHOW_PROCESSING_INSTRUCTION|a.SHOW_CDATA_SECTION,null)},en=function(c){return c instanceof g&&(typeof c.nodeName!="string"||typeof c.textContent!="string"||typeof c.removeChild!="function"||!(c.attributes instanceof f)||typeof c.removeAttribute!="function"||typeof c.setAttribute!="function"||typeof c.namespaceURI!="string"||typeof c.insertBefore!="function"||typeof c.hasChildNodes!="function")},Vn=function(c){return typeof l=="function"&&c instanceof l};function be(k,c,m){Ct(k,A=>{A.call(e,c,m,Ge)})}const Bn=function(c){let m=null;if(be(P.beforeSanitizeElements,c,null),en(c))return de(c),!0;const A=H(c.nodeName);if(be(P.uponSanitizeElement,c,{tagName:A,allowedTags:z}),c.hasChildNodes()&&!Vn(c.firstElementChild)&&K(/<[/\w]/g,c.innerHTML)&&K(/<[/\w]/g,c.textContent)||c.nodeType===ut.progressingInstruction||jt&&c.nodeType===ut.comment&&K(/<[/\w]/g,c.data))return de(c),!0;if(!z[A]||Ee[A]){if(!Ee[A]&&Gn(A)&&(I.tagNameCheck instanceof RegExp&&K(I.tagNameCheck,A)||I.tagNameCheck instanceof Function&&I.tagNameCheck(A)))return!1;if(Yt&&!Be[A]){const D=O(c)||c.parentNode,Z=V(c)||c.childNodes;if(Z&&D){const q=Z.length;for(let ne=q-1;ne>=0;--ne){const ge=_(Z[ne],!0);ge.__removalCount=(c.__removalCount||0)+1,D.insertBefore(ge,y(c))}}}return de(c),!0}return c instanceof u&&!ps(c)||(A==="noscript"||A==="noembed"||A==="noframes")&&K(/<\/no(script|embed|frames)/i,c.innerHTML)?(de(c),!0):(Ue&&c.nodeType===ut.text&&(m=c.textContent,Ct([fe,De,xe],D=>{m=lt(m,D," ")}),c.textContent!==m&&(ot(e.removed,{element:c.cloneNode()}),c.textContent=m)),be(P.afterSanitizeElements,c,null),!1)},Hn=function(c,m,A){if(Ln&&(m==="id"||m==="name")&&(A in t||A in hs))return!1;if(!($&&!j[m]&&K(Pe,m))){if(!(b&&K($e,m))){if(!F[m]||j[m]){if(!(Gn(c)&&(I.tagNameCheck instanceof RegExp&&K(I.tagNameCheck,c)||I.tagNameCheck instanceof Function&&I.tagNameCheck(c))&&(I.attributeNameCheck instanceof RegExp&&K(I.attributeNameCheck,m)||I.attributeNameCheck instanceof Function&&I.attributeNameCheck(m))||m==="is"&&I.allowCustomizedBuiltInElements&&(I.tagNameCheck instanceof RegExp&&K(I.tagNameCheck,A)||I.tagNameCheck instanceof Function&&I.tagNameCheck(A))))return!1}else if(!Xt[m]){if(!K(G,lt(A,ze,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&c!=="script"&&Ur(A,"data:")===0&&On[c])){if(!(te&&!K(he,lt(A,ze,"")))){if(A)return!1}}}}}}return!0},Gn=function(c){return c!=="annotation-xml"&&ui(c,At)},Wn=function(c){be(P.beforeSanitizeAttributes,c,null);const{attributes:m}=c;if(!m||en(c))return;const A={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let D=m.length;for(;D--;){const Z=m[D],{name:q,namespaceURI:ne,value:ge}=Z,tt=H(q);let Q=q==="value"?ge:Fr(ge);if(A.attrName=tt,A.attrValue=Q,A.keepAttr=!0,A.forceKeepAttr=void 0,be(P.uponSanitizeAttribute,c,A),Q=A.attrValue,In&&(tt==="id"||tt==="name")&&(xt(q,c),Q=ls+Q),jt&&K(/((--!?|])>)|<\/(style|title)/i,Q)){xt(q,c);continue}if(A.forceKeepAttr||(xt(q,c),!A.keepAttr))continue;if(!vn&&K(/\/>/i,Q)){xt(q,c);continue}Ue&&Ct([fe,De,xe],qn=>{Q=lt(Q,qn," ")});const jn=H(c.nodeName);if(Hn(jn,tt,Q)){if(C&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!ne)switch(d.getAttributeType(jn,tt)){case"TrustedHTML":{Q=C.createHTML(Q);break}case"TrustedScriptURL":{Q=C.createScriptURL(Q);break}}try{ne?c.setAttributeNS(ne,q,Q):c.setAttribute(q,Q),en(c)?de(c):ci(e.removed)}catch{}}}be(P.afterSanitizeAttributes,c,null)},ds=function k(c){let m=null;const A=Fn(c);for(be(P.beforeSanitizeShadowDOM,c,null);m=A.nextNode();)be(P.uponSanitizeShadowNode,m,null),Bn(m),Wn(m),m.content instanceof r&&k(m.content);be(P.afterSanitizeShadowDOM,c,null)};return e.sanitize=function(k){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,A=null,D=null,Z=null;if(Qt=!k,Qt&&(k="<!-->"),typeof k!="string"&&!Vn(k))if(typeof k.toString=="function"){if(k=k.toString(),typeof k!="string")throw at("dirty is not a string, aborting")}else throw at("toString is not a function");if(!e.isSupported)return k;if(qt||Jt(c),e.removed=[],typeof k=="string"&&(Je=!1),Je){if(k.nodeName){const ge=H(k.nodeName);if(!z[ge]||Ee[ge])throw at("root node is forbidden and cannot be sanitized in-place")}}else if(k instanceof l)m=Un("<!---->"),A=m.ownerDocument.importNode(k,!0),A.nodeType===ut.element&&A.nodeName==="BODY"||A.nodeName==="HTML"?m=A:m.appendChild(A);else{if(!Fe&&!Ue&&!Le&&k.indexOf("<")===-1)return C&&kt?C.createHTML(k):k;if(m=Un(k),!m)return Fe?null:kt?X:""}m&&Zt&&de(m.firstChild);const q=Fn(Je?k:m);for(;D=q.nextNode();)Bn(D),Wn(D),D.content instanceof r&&ds(D.content);if(Je)return k;if(Fe){if(_t)for(Z=L.call(m.ownerDocument);m.firstChild;)Z.appendChild(m.firstChild);else Z=m;return(F.shadowroot||F.shadowrootmode)&&(Z=_e.call(n,Z,!0)),Z}let ne=Le?m.outerHTML:m.innerHTML;return Le&&z["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&K(ss,m.ownerDocument.doctype.name)&&(ne="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+ne),Ue&&Ct([fe,De,xe],ge=>{ne=lt(ne,ge," ")}),C&&kt?C.createHTML(ne):ne},e.setConfig=function(){let k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Jt(k),qt=!0},e.clearConfig=function(){Ge=null,qt=!1},e.isValidAttribute=function(k,c,m){Ge||Jt({});const A=H(k),D=H(c);return Hn(A,D,m)},e.addHook=function(k,c){typeof c=="function"&&ot(P[k],c)},e.removeHook=function(k){return ci(P[k])},e.removeHooks=function(k){P[k]=[]},e.removeAllHooks=function(){P=mi()},e}var Ai=rs();function _i(i,e,t){const n=i.slice();return n[15]=e[t],n}function ki(i){let e,t;return{c(){e=U("img"),Yn(e.src,t=i[0])||x(e,"src",t),x(e,"alt","Chat Icon"),x(e,"class","w-6 h-6")},m(n,s){Ae(n,e,s)},p(n,s){s&1&&!Yn(e.src,t=n[0])&&x(e,"src",t)},d(n){n&&ce(e)}}}function to(i){let e,t;return e=new Hi({props:{class:"h-4 w-4 text-gray-500"}}),{c(){Ce(e.$$.fragment)},m(n,s){ye(e,n,s),t=!0},i(n){t||(B(e.$$.fragment,n),t=!0)},o(n){Y(e.$$.fragment,n),t=!1},d(n){Te(e,n)}}}function no(i){let e,t;return e=new Hi({props:{class:"h-4 w-4 text-gray-500 transform rotate-180"}}),{c(){Ce(e.$$.fragment)},m(n,s){ye(e,n,s),t=!0},i(n){t||(B(e.$$.fragment,n),t=!0)},o(n){Y(e.$$.fragment,n),t=!1},d(n){Te(e,n)}}}function bi(i){let e,t;return{c(){e=U("div"),t=mn(i[4]),x(e,"class","bg-red-50 text-red-500 p-3 rounded-lg mb-4")},m(n,s){Ae(n,e,s),N(e,t)},p(n,s){s&16&&Ts(t,n[4])},d(n){n&&ce(e)}}}function wi(i,e){let t,n,s=Ai.sanitize(E.parse(e[15].text))+"",r,o;return{key:i,first:null,c(){t=U("div"),n=U("div"),x(n,"class",r="rounded-lg p-3 max-w-[80%] "+(e[15].sender==="user"?"bg-blue-600 text-white":"bg-gray-100 text-gray-900")),x(t,"class",o="flex "+(e[15].sender==="user"?"justify-end":"justify-start")),this.first=t},m(l,u){Ae(l,t,u),N(t,n),n.innerHTML=s},p(l,u){e=l,u&2&&s!==(s=Ai.sanitize(E.parse(e[15].text))+"")&&(n.innerHTML=s),u&2&&r!==(r="rounded-lg p-3 max-w-[80%] "+(e[15].sender==="user"?"bg-blue-600 text-white":"bg-gray-100 text-gray-900"))&&x(n,"class",r),u&2&&o!==(o="flex "+(e[15].sender==="user"?"justify-end":"justify-start"))&&x(t,"class",o)},d(l){l&&ce(t)}}}function yi(i){let e;return{c(){e=U("div"),e.innerHTML=`<div class="bg-gray-100 text-gray-900 rounded-lg p-3"><div class="flex space-x-2"><div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div> 
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div> 
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div></div></div>`,x(e,"class","flex justify-start")},m(t,n){Ae(t,e,n)},d(t){t&&ce(e)}}}function io(i){let e,t,n,s,r,o,l,u,a,f,g,h,d,p,_,w,y,V,O=[],C=new Map,X,re,S,L,M,_e,P,fe,De,xe,Pe,$e,he,ze,At,G=i[0]&&ki(i);const z=[no,to],pe=[];function F(b,$){return b[6]?0:1}a=F(i),f=pe[a]=z[a](i),p=new er({props:{class:"h-4 w-4 text-gray-500"}});let W=i[4]&&bi(i),I=i[1];const Ee=b=>b[15].id;for(let b=0;b<I.length;b+=1){let $=_i(i,I,b),te=Ee($);C.set(te,O[b]=wi(te,$))}let j=i[3]&&yi();return fe=new Ys({props:{class:"h-4 w-4"}}),{c(){e=U("div"),t=U("div"),n=U("div"),G&&G.c(),s=we(),r=U("h2"),r.textContent="Chat Assistant",o=we(),l=U("div"),u=U("button"),f.c(),h=we(),d=U("button"),Ce(p.$$.fragment),_=we(),w=U("div"),y=U("div"),W&&W.c(),V=we();for(let b=0;b<O.length;b+=1)O[b].c();X=we(),j&&j.c(),re=we(),S=U("div"),L=U("form"),M=U("input"),_e=we(),P=U("button"),Ce(fe.$$.fragment),De=we(),xe=U("span"),xe.textContent="Send",x(r,"class","text-lg font-semibold"),x(n,"class","flex items-center gap-2"),x(u,"class","p-1 hover:bg-gray-100 rounded-md transition-colors"),x(u,"aria-label",g=i[6]?"Restore chat":"Maximize chat"),x(d,"class","p-1 hover:bg-gray-100 rounded-md transition-colors"),x(d,"aria-label","Close chat"),x(l,"class","flex items-center gap-2"),x(t,"class","p-4 border-b flex justify-between items-center"),x(y,"class","h-full overflow-y-auto pr-4 space-y-4 svelte-na9m5q"),x(w,"class","flex-grow overflow-hidden p-4"),x(M,"type","text"),x(M,"placeholder","Type your message..."),x(M,"class","flex-grow px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"),M.disabled=i[3],x(xe,"class","sr-only"),x(P,"type","submit"),x(P,"class","p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-400"),P.disabled=Pe=i[3]||!i[2].trim(),x(L,"class","flex items-center space-x-2"),x(S,"class","p-4 border-t"),x(e,"class",$e="flex flex-col bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300 "+(i[6]?"fixed h-full inset-0 z-50 rounded-none":"fixed max-h-[600px] h-[calc(100%-48px)] max-w-[calc(100%-20px)] w-[400px] bottom-0 right-0 mb-4 mr-4"))},m(b,$){Ae(b,e,$),N(e,t),N(t,n),G&&G.m(n,null),N(n,s),N(n,r),N(t,o),N(t,l),N(l,u),pe[a].m(u,null),N(l,h),N(l,d),ye(p,d,null),N(e,_),N(e,w),N(w,y),W&&W.m(y,null),N(y,V);for(let te=0;te<O.length;te+=1)O[te]&&O[te].m(y,null);N(y,X),j&&j.m(y,null),i[10](y),N(e,re),N(e,S),N(S,L),N(L,M),Kn(M,i[2]),N(L,_e),N(L,P),ye(fe,P,null),N(P,De),N(P,xe),he=!0,ze||(At=[St(u,"click",i[7]),St(d,"click",i[9]),St(M,"input",i[11]),St(L,"submit",ws(i[8]))],ze=!0)},p(b,[$]){b[0]?G?G.p(b,$):(G=ki(b),G.c(),G.m(n,s)):G&&(G.d(1),G=null);let te=a;a=F(b),a!==te&&(Fi(),Y(pe[te],1,1,()=>{pe[te]=null}),Vi(),f=pe[a],f||(f=pe[a]=z[a](b),f.c()),B(f,1),f.m(u,null)),(!he||$&64&&g!==(g=b[6]?"Restore chat":"Maximize chat"))&&x(u,"aria-label",g),b[4]?W?W.p(b,$):(W=bi(b),W.c(),W.m(y,V)):W&&(W.d(1),W=null),$&2&&(I=b[1],O=Ds(O,$,Ee,1,b,I,C,y,Ms,wi,X,_i)),b[3]?j||(j=yi(),j.c(),j.m(y,null)):j&&(j.d(1),j=null),(!he||$&8)&&(M.disabled=b[3]),$&4&&M.value!==b[2]&&Kn(M,b[2]),(!he||$&12&&Pe!==(Pe=b[3]||!b[2].trim()))&&(P.disabled=Pe),(!he||$&64&&$e!==($e="flex flex-col bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300 "+(b[6]?"fixed h-full inset-0 z-50 rounded-none":"fixed max-h-[600px] h-[calc(100%-48px)] max-w-[calc(100%-20px)] w-[400px] bottom-0 right-0 mb-4 mr-4")))&&x(e,"class",$e)},i(b){he||(B(f),B(p.$$.fragment,b),B(fe.$$.fragment,b),he=!0)},o(b){Y(f),Y(p.$$.fragment,b),Y(fe.$$.fragment,b),he=!1},d(b){b&&ce(e),G&&G.d(),pe[a].d(),Te(p),W&&W.d();for(let $=0;$<O.length;$+=1)O[$].d();j&&j.d(),i[10](null),Te(fe),ze=!1,ve(At)}}}function so(i,e,t){let{chatIcon:n}=e;const s=Rs();let r=[{id:1,text:"Hello! How can I help you today?",sender:"bot"}],o="",l=!1,u=null,a,f=!1;const g=new kn;function h(){t(6,f=!f),s("maximize",{maximized:f})}$i(()=>{g.startNewConversation(),new Audio("data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjI5LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAAFbgCenp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6e//////////////////////////////////////////////////////////////////8AAAAATGF2YzU4LjU0AAAAAAAAAAAAAAAAJAAAAAAAAAAAAQVuix9NcQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQZAAP8AAAaQAAAAgAAA0gAAABAAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVU=")});async function d(){if(!o.trim()||l)return;t(3,l=!0),t(4,u=null);const y=o;t(2,o=""),t(1,r=[...r,{id:r.length+1,text:y,sender:"user"}]);try{const V=await g.sendMessage(y);t(1,r=[...r,{id:r.length+1,text:V.response,sender:"bot"}])}catch(V){t(4,u="Failed to send message. Please try again."),console.error("Chat error:",V)}finally{t(3,l=!1),a&&setTimeout(()=>{t(5,a.scrollTop=a.scrollHeight,a)},100)}}function p(){s("close")}function _(y){un[y?"unshift":"push"](()=>{a=y,t(5,a)})}function w(){o=this.value,t(2,o)}return i.$$set=y=>{"chatIcon"in y&&t(0,n=y.chatIcon)},[n,r,o,l,u,a,f,h,d,p,_,w]}class ro extends Ke{constructor(e){super(),Qe(this,e,so,io,Oe,{chatIcon:0})}}function oo(i){const e=i-1;return e*e*e+1}function Ti(i,{delay:e=0,duration:t=400,easing:n=oo,x:s=0,y:r=0,opacity:o=0}={}){const l=getComputedStyle(i),u=+l.opacity,a=l.transform==="none"?"":l.transform,f=u*(1-o),[g,h]=Qn(s),[d,p]=Qn(r);return{delay:e,duration:t,easing:n,css:(_,w)=>`
			transform: ${a} translate(${(1-_)*g}${h}, ${(1-_)*d}${p});
			opacity: ${u-f*w}`}}function lo(){let i=document.currentScript;if(!i){const n=document.getElementsByTagName("script");i=n[n.length-1]}return new URL(i.src).origin}const os=lo();function xi(i){let e,t,n,s,r;return t=new ro({props:{chatIcon:i[1]}}),t.$on("close",i[2]),{c(){e=U("div"),Ce(t.$$.fragment),x(e,"class","aida-chat-container svelte-nmil5y")},m(o,l){Ae(o,e,l),ye(t,e,null),r=!0},p(o,l){const u={};l&2&&(u.chatIcon=o[1]),t.$set(u)},i(o){r||(B(t.$$.fragment,o),Xe(()=>{r&&(s&&s.end(1),n=Ns(e,Ti,{y:20,duration:300}),n.start())}),r=!0)},o(o){Y(t.$$.fragment,o),n&&n.invalidate(),s=Os(e,Ti,{y:20,duration:200}),r=!1},d(o){o&&ce(e),Te(t),o&&s&&s.end()}}}function ao(i){let e,t,n=i[0]&&xi(i);return{c(){n&&n.c(),e=An()},m(s,r){n&&n.m(s,r),Ae(s,e,r),t=!0},p(s,[r]){s[0]?n?(n.p(s,r),r&1&&B(n,1)):(n=xi(s),n.c(),B(n,1),n.m(e.parentNode,e)):n&&(Fi(),Y(n,1,1,()=>{n=null}),Vi())},i(s){t||(B(n),t=!0)},o(s){Y(n),t=!1},d(s){n&&n.d(s),s&&ce(e)}}}function co(i,e,t){let{showChat:n=!1}=e,s="/images/chat.png";function r(){t(0,n=!1)}let o=null,l="";const u=new kn;return $i(async()=>{try{o=await u.getConfig(),l=o.llm_slug,o.chat_icon&&t(1,s=o.chat_icon),t(1,s=os+s),o.initial_message&&u.addMessage("assistant",o.initial_message)}catch(a){console.error(a)}}),i.$$set=a=>{"showChat"in a&&t(0,n=a.showChat)},[n,s,r]}class uo extends Ke{constructor(e){super(),Qe(this,e,co,ao,Oe,{showChat:0})}}(()=>{if(window.__AIDA_LOADED__){console.warn("Aida component is already loaded");return}let i=document.getElementById("aida-root");i||(i=document.createElement("div"),i.id="aida-root",document.body.appendChild(i));const e=new kn,t=document.createElement("div");t.id="aida-chat-icon",t.style.display="none",t.style.position="fixed",t.style.cursor="pointer",t.style.zIndex="9998",document.body.appendChild(t),window.AIDA=new uo({target:i,props:{showChat:!1}}),e.getConfig().then(n=>{var s,r;t.style.bottom=((s=n.chat_position)==null?void 0:s.bottom)||"20px",t.style.right=((r=n.chat_position)==null?void 0:r.right)||"20px",t.innerHTML=`
      <img src="${os+(n.chat_icon||"/images/chat.png")}"
           alt="Chat Icon"
           width="48"
           height="48" />
    `,t.style.display="block",t.addEventListener("click",()=>{window.AIDA.$set({showChat:!0})})}).catch(n=>{console.error("Failed to load chat config:",n)}),window.__AIDA_LOADED__=!0})();
